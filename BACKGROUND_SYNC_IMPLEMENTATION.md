# Background Report Retrieval Implementation

## Overview
This implementation adds comprehensive background report retrieval capabilities to the EventManager app, allowing it to fetch reports from the server even when the app is closed.

## Components Implemented

### 1. DataSyncWorker
**File**: `app/src/main/java/com/logikden/eventmanager/services/DataSyncWorker.kt`

A dedicated WorkManager worker for background data synchronization:
- **Periodic Sync**: Runs at configurable intervals (default: 30 minutes)
- **Smart Constraints**: Respects WiFi-only, battery optimization, and network requirements
- **Location Handling**: Uses cached location when current location is unavailable
- **Error Handling**: Implements retry logic with exponential backoff
- **Settings Integration**: Automatically starts/stops based on user preferences

**Key Features**:
- Network type constraints (WiFi-only or any network)
- Battery optimization (requires battery not low, charging optional)
- Cached location fallback for background operations
- Automatic retry on failure

### 2. LocationCacheManager
**File**: `app/src/main/java/com/logikden/eventmanager/utils/LocationCacheManager.kt`

Manages location caching for background sync operations:
- **Smart Caching**: Only caches location when significant time/distance changes occur
- **Cache Validation**: Ensures cached location is not older than 24 hours
- **Criteria-Based**: Minimum 5-minute interval and 100-meter distance change
- **Thread-Safe**: Singleton pattern with proper synchronization

**Caching Criteria**:
- Minimum time interval: 5 minutes
- Minimum distance change: 100 meters
- Maximum cache age: 24 hours

### 3. Enhanced SettingsManager
**File**: `app/src/main/java/com/logikden/eventmanager/utils/SettingsManager.kt`

Added background sync preferences:
- **Background Sync Enabled**: Master toggle for background sync
- **Sync Interval**: Configurable interval (default: 30 minutes)
- **WiFi Only Sync**: Option to sync only on WiFi
- **Battery Optimization**: Respect battery optimization settings
- **Location Caching**: Store and retrieve cached location data

**New Settings**:
```kotlin
// Background sync settings
fun isBackgroundSyncEnabled(): Boolean
fun getBackgroundSyncInterval(): Int
fun isWifiOnlySyncEnabled(): Boolean
fun isBatteryOptimizationEnabled(): Boolean

// Location cache settings
fun cacheLocation(latitude: Double, longitude: Double)
fun getCachedLocation(): Location?
fun hasValidCachedLocation(): Boolean
```

### 4. Enhanced ProximityWorker
**File**: `app/src/main/java/com/logikden/eventmanager/services/ProximityWorker.kt`

Updated to trigger data sync:
- **Dual Purpose**: Maintains proximity checking and triggers data sync
- **Conditional Sync**: Only triggers sync if background sync is enabled
- **Logging**: Enhanced logging for debugging

### 5. Enhanced ProximityService
**File**: `app/src/main/java/com/logikden/eventmanager/services/ProximityService.kt`

Integrated location caching and data sync management:
- **Location Caching**: Automatically caches location updates
- **Worker Management**: Starts/stops DataSyncWorker based on settings
- **Settings Monitoring**: Responds to background sync setting changes
- **Lifecycle Management**: Proper initialization and cleanup

**Key Additions**:
- LocationCacheManager integration
- setupDataSyncWorker() method
- Settings change handling for background sync
- Automatic location caching on location updates

### 6. Enhanced RemoteEventRepository
**File**: `app/src/main/java/com/logikden/eventmanager/ui/events/RemoteEventRepository.kt`

Improved background sync capabilities:
- **Cached Location Support**: Falls back to cached location when current location unavailable
- **Background Sync Logic**: Enhanced syncPendingData() method
- **Temporary Location Setting**: Safely uses cached location for sync operations
- **Comprehensive Logging**: Detailed logging for background operations

## How It Works

### 1. Location Caching
1. **ProximityService** receives location updates
2. **LocationCacheManager** evaluates if location should be cached based on:
   - Time since last cache (minimum 5 minutes)
   - Distance from last cached location (minimum 100 meters)
3. Valid locations are stored in SharedPreferences with timestamp

### 2. Background Sync Process
1. **DataSyncWorker** runs periodically based on user settings
2. Checks if background sync is enabled
3. Validates network connectivity and constraints
4. Attempts to get current location, falls back to cached location
5. Uses **RemoteEventRepository** to fetch reports from server
6. Updates local database with new reports

### 3. Settings Integration
1. User can enable/disable background sync in settings
2. **ProximityService** monitors setting changes
3. **DataSyncWorker** is started/stopped automatically
4. Sync interval and constraints are updated dynamically

### 4. Constraint Handling
- **Network**: Requires connected network (WiFi-only optional)
- **Battery**: Respects battery optimization settings
- **Storage**: Requires sufficient storage space
- **Location**: Uses cached location when current location unavailable

## Configuration Options

### Background Sync Settings
- **Enabled**: Master toggle (default: true)
- **Interval**: Sync frequency in minutes (default: 30)
- **WiFi Only**: Restrict to WiFi networks (default: false)
- **Battery Optimization**: Respect battery settings (default: true)

### Location Cache Settings
- **Cache Duration**: 24 hours maximum age
- **Update Frequency**: Minimum 5 minutes between caches
- **Distance Threshold**: Minimum 100 meters movement

## Benefits

### 1. Always Up-to-Date Data
- Reports are fetched even when app is closed
- Users see fresh data when opening the app
- Reduces manual refresh needs

### 2. Battery Efficient
- Smart location caching reduces GPS usage
- Configurable constraints respect battery optimization
- Background sync only when conditions are met

### 3. Network Aware
- WiFi-only option for data-conscious users
- Automatic retry on network failures
- Respects network availability

### 4. User Controlled
- Complete user control over background sync
- Granular settings for different use cases
- Easy to disable if not needed

## Usage

### For Users
1. Background sync is enabled by default
2. Configure preferences in Settings
3. App will automatically fetch reports in background
4. Fresh data available when app is opened

### For Developers
1. **DataSyncWorker** handles all background sync logic
2. **LocationCacheManager** provides location caching utilities
3. Settings are automatically synchronized across components
4. Comprehensive logging for debugging

## Testing

### Manual Testing
1. Enable background sync in settings
2. Close the app completely
3. Wait for sync interval
4. Check logs for background sync activity
5. Open app and verify fresh data

### Debugging
- Check WorkManager status in device settings
- Monitor logs with tag "DataSyncWorker"
- Verify location cache with LocationCacheManager.getCacheInfo()
- Check settings with SettingsManager methods

## Future Enhancements

### Potential Improvements
1. **Adaptive Sync**: Adjust frequency based on user activity
2. **Geofencing**: Trigger sync when entering/leaving areas
3. **Push Notifications**: Server-initiated sync requests
4. **Sync Statistics**: Track sync success/failure rates
5. **Data Usage Monitoring**: Track background data consumption

### Performance Optimizations
1. **Incremental Sync**: Only fetch new/changed reports
2. **Compression**: Compress API responses
3. **Caching Strategy**: More sophisticated cache invalidation
4. **Background Processing**: Optimize database operations

This implementation provides a robust, efficient, and user-friendly background sync system that ensures the app always has fresh data while respecting user preferences and device constraints.
