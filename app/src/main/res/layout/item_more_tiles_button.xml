<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="10dp"
    android:layout_marginHorizontal="12dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:strokeWidth="0dp">

    <LinearLayout
        android:id="@+id/moreTilesButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:padding="16dp"
        android:background="@color/zatani_primary">

        <ImageView
            android:id="@+id/expandIcon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_expand_more"
            android:tint="#FFFFFF"
            android:layout_marginEnd="8dp" />

        <TextView
            android:id="@+id/moreTilesText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="More Report Types"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:textStyle="bold" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
