<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="8dp">

    <com.google.android.material.card.MaterialCardView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="2dp"
        app:cardElevation="4dp"
        app:cardCornerRadius="8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="16dp">

            <TextView
                android:id="@+id/tileTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/tile_title"
                android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                android:textColor="?attr/colorPrimary"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintEnd_toStartOf="@id/deleteButton"
                tools:text="Road Blocked" />

            <TextView
                android:id="@+id/tileOrder"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="Order: 1"
                android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tileTitle"
                app:layout_constraintEnd_toStartOf="@id/deleteButton"
                tools:text="Order: 1" />

            <ImageButton
                android:id="@+id/deleteButton"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="TODO"
                android:padding="12dp"
                android:src="@drawable/ic_delete"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:tint="?attr/colorError" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </com.google.android.material.card.MaterialCardView>

</androidx.constraintlayout.widget.ConstraintLayout>