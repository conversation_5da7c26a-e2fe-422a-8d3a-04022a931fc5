<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F5F5F5">

    <TextView
        android:id="@+id/homeSubtitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="6dp"
        android:layout_marginBottom="0dp"
        android:text="Tap left section to submit a report, right section to view details"
        android:textColor="#757575"
        android:textSize="14sp"
        android:textStyle="italic"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_editor_absoluteX="16dp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/reportTypesRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:clipToPadding="false"
        android:paddingVertical="8dp"
        android:paddingHorizontal="4dp"
        android:layout_marginTop="2dp"
        android:overScrollMode="never"
        android:scrollbarStyle="outsideOverlay"
        android:scrollbars="vertical"
        android:fadeScrollbars="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/homeSubtitle"
        tools:listitem="@layout/item_report_type" />

</androidx.constraintlayout.widget.ConstraintLayout>

