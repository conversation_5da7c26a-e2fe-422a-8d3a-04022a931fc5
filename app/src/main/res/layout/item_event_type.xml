<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="8dp"
    app:cardElevation="4dp"
    app:cardCornerRadius="8dp"
    app:cardUseCompatPadding="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- Event Type Name -->
        <TextView
            android:id="@+id/eventTypeName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Event Type"
            android:textSize="18sp"
            android:textAllCaps="true"
            app:flow_verticalAlign="center"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/eventCount"
           />


        <!-- Event Count -->
        <TextView
            android:id="@+id/eventCount"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:background="@drawable/circle_background"
            android:gravity="center"
            android:minWidth="32dp"
            android:text="0"
            android:textColor="@android:color/white"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <!-- Container for child items -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/eventDetails"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/eventTypeName"  
            android:layout_height="wrap_content"
            android:layout_width="0dp"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>