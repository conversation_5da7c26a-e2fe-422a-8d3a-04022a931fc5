<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="4dp"
    app:cardElevation="2dp"
    app:cardCornerRadius="4dp"
    app:cardUseCompatPadding="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:id="@+id/eventDetailContainer"
        android:layout_height="wrap_content"
        android:padding="8dp">

        <TextView
            android:id="@+id/eventDetailTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Event Time"
            android:textSize="14sp"
            android:textColor="@android:color/darker_gray"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/eventDetailDistance"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Distance"
            android:textSize="16sp"
            android:textColor="@android:color/darker_gray"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/eventDetailSpeedIndicator" />

        <!-- Speed Indicator Section -->
        <LinearLayout
            android:id="@+id/eventDetailSpeedIndicator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/proximityCountBadge"
            app:layout_constraintStart_toEndOf="@+id/eventDetailDistance">

            <TextView
                android:id="@+id/speedCategoryIndicator"
                android:layout_width="8dp"
                android:layout_height="8dp"
                android:text="●"
                android:textSize="10sp"
                android:gravity="center"
                android:layout_marginEnd="4dp"
                android:background="@android:color/darker_gray" />

            <TextView
                android:id="@+id/speedCategoryText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Stationary"
                android:textSize="12sp"
                android:textColor="@android:color/darker_gray"
                android:layout_marginEnd="4dp" />

            <TextView
                android:id="@+id/speedValueText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0 km/h"
                android:textSize="12sp"
                android:textColor="@android:color/darker_gray" />

        </LinearLayout>

        <!-- Badge for proximity count -->
        <TextView
            android:id="@+id/proximityCountBadge"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="@drawable/orange_badge_background"
            android:gravity="center"
            android:text="0"
            android:textColor="@android:color/white"
            android:textSize="14sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/eventDetailTime"
            android:layout_marginTop="2dp" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>