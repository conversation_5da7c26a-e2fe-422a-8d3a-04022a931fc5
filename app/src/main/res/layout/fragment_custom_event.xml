<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    tools:context=".ui.home.CustomEventFragment">

    <!-- Title Field -->
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/titleInputLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/titleInput"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Title" />
    </com.google.android.material.textfield.TextInputLayout>

    <!-- Order Field -->
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/orderInputLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titleInputLayout">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/orderInput"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Order"
            android:inputType="number" />
    </com.google.android.material.textfield.TextInputLayout>

    <!-- Save Button -->
    <Button
        android:id="@+id/saveButton"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="Save"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/orderInputLayout" />

    <!-- Update Button -->
    <Button
        android:id="@+id/updateButton"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="Update"
        android:visibility="gone"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toBottomOf="@id/saveButton" />

    <!-- Clear Button -->
    <Button
        android:id="@+id/clearButton"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        style="@style/Widget.MaterialComponents.Button.OutlinedButton"
        android:layout_marginTop="16dp"
        android:text="@string/clear"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/updateButton" />

    <!-- RecyclerView to List Tiles -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/tilesRecyclerView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="40dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/clearButton" />

</androidx.constraintlayout.widget.ConstraintLayout>