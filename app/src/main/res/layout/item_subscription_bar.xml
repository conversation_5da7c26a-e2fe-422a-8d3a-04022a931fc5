<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="8dp"
    android:layout_marginVertical="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <com.google.android.material.button.MaterialButton
        android:id="@+id/subscriptionButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="64dp"
        android:layout_marginHorizontal="6dp"
        android:gravity="center_vertical|start"
        android:padding="16dp"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textAllCaps="false"
        app:cornerRadius="8dp"
        app:icon="@drawable/ic_default"
        app:iconGravity="start"
        app:iconPadding="16dp"
        app:iconSize="24dp" />

</com.google.android.material.card.MaterialCardView>