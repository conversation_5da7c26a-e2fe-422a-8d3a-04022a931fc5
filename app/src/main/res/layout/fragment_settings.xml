<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Immediate Updates Switch -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/settings_item_background"
            android:padding="16dp"
            android:layout_marginBottom="8dp">

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/immediate_updates_switch"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Apply changes immediately"
                android:textSize="16sp"
                android:checked="true"/>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="When enabled, settings will be applied as you change them"
                style="@style/SettingsDescriptionStyle"/>
        </LinearLayout>

        <!-- Save Settings Button - Only visible when immediate updates is off -->
        <Button
            android:id="@+id/save_settings_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Save Settings"
            android:visibility="gone"
            android:layout_marginBottom="8dp"
            android:background="@drawable/settings_item_background"
            android:textColor="@color/white"
            android:backgroundTint="@color/zatani_primary"/>

        <!-- Update Radius Setting -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/settings_item_background"
            android:padding="16dp"
            android:layout_marginBottom="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_radius"
                    android:layout_marginEnd="8dp"
                    android:tint="@color/zatani_primary"
                    android:contentDescription="Radius icon"/>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/update_radius_title"
                    style="@style/SettingsTitleStyle"/>
            </LinearLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/update_radius_description"
                style="@style/SettingsDescriptionStyle"/>

            <SeekBar
                android:id="@+id/radius_seekbar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                style="@style/SettingsSeekBarStyle"/>

            <TextView
                android:id="@+id/radius_value_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="5 km"
                style="@style/SettingsValueStyle"/>
        </LinearLayout>

        <!-- Check Frequency Setting -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/settings_item_background"
            android:padding="16dp"
            android:layout_marginBottom="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_timer"
                    android:layout_marginEnd="8dp"
                    android:tint="@color/zatani_primary"
                    android:contentDescription="Frequency icon"/>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/check_frequency_title"
                    style="@style/SettingsTitleStyle"/>
            </LinearLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/check_frequency_description"
                style="@style/SettingsDescriptionStyle"/>

            <SeekBar
                android:id="@+id/check_frequency_seekbar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                style="@style/SettingsSeekBarStyle"/>

            <TextView
                android:id="@+id/check_frequency_value_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="15 minutes"
                style="@style/SettingsValueStyle"/>
        </LinearLayout>

        <!-- Movement Update Setting -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/settings_item_background"
            android:padding="16dp"
            android:layout_marginBottom="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_movement"
                    android:layout_marginEnd="8dp"
                    android:tint="@color/zatani_primary"
                    android:contentDescription="Movement icon"/>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/moved_update_title"
                    style="@style/SettingsTitleStyle"/>
            </LinearLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/moved_update_description"
                style="@style/SettingsDescriptionStyle"/>

            <SeekBar
                android:id="@+id/moved_update_seekbar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                style="@style/SettingsSeekBarStyle"/>

            <TextView
                android:id="@+id/moved_update_value_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="5 KM"
                style="@style/SettingsValueStyle"/>
        </LinearLayout>

        <!-- Event Expiry Setting -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/settings_item_background"
            android:padding="16dp"
            android:layout_marginBottom="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_expiry"
                    android:layout_marginEnd="8dp"
                    android:tint="@color/zatani_primary"
                    android:contentDescription="Expiry icon"/>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/event_expiry_title"
                    style="@style/SettingsTitleStyle"/>
            </LinearLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/event_expiry_description"
                style="@style/SettingsDescriptionStyle"/>

            <SeekBar
                android:id="@+id/event_expiry_seekbar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                style="@style/SettingsSeekBarStyle"/>

            <TextView
                android:id="@+id/event_expiry_value_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="24 hours"
                style="@style/SettingsValueStyle"/>
        </LinearLayout>

        <!-- Notification Cooldown Setting -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/settings_item_background"
            android:padding="16dp"
            android:layout_marginBottom="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_cooldown"
                    android:layout_marginEnd="8dp"
                    android:tint="@color/zatani_primary"
                    android:contentDescription="Cooldown icon"/>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Notification Cooldown"
                    style="@style/SettingsTitleStyle"/>
            </LinearLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Minimum time between notifications"
                style="@style/SettingsDescriptionStyle"/>

            <SeekBar
                android:id="@+id/notification_cooldown_seekbar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                style="@style/SettingsSeekBarStyle"/>

            <TextView
                android:id="@+id/notification_cooldown_value"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="3 seconds"
                style="@style/SettingsValueStyle"/>
        </LinearLayout>

        <!-- Proximity Alert Distance Setting -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/settings_item_background"
            android:padding="16dp"
            android:layout_marginBottom="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_proximity"
                    android:layout_marginEnd="8dp"
                    android:tint="@color/zatani_primary"
                    android:contentDescription="Proximity icon"/>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/proximity_alert_distance"
                    style="@style/SettingsTitleStyle"/>
            </LinearLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/proximity_alert_description"
                style="@style/SettingsDescriptionStyle"/>

            <SeekBar
                android:id="@+id/proximity_threshold_seekbar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                style="@style/SettingsSeekBarStyle"/>

            <TextView
                android:id="@+id/proximity_threshold_value_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                style="@style/SettingsValueStyle"
                android:text="@string/_1_0_km"/>
        </LinearLayout>

        <!-- Battery Optimization Settings -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/settings_item_background"
            android:padding="16dp"
            android:layout_marginBottom="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@android:drawable/ic_lock_power_off"
                    android:layout_marginEnd="8dp"
                    android:tint="@color/zatani_primary"
                    android:contentDescription="Battery optimization icon"/>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Battery Optimization"
                    style="@style/SettingsTitleStyle"/>
            </LinearLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Configure battery-saving features for location tracking"
                style="@style/SettingsDescriptionStyle"
                android:layout_marginBottom="16dp"/>

            <!-- Activity Recognition Switch -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="8dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Smart Location Tracking"
                    android:textSize="16sp"/>

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/activity_recognition_switch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="true"/>
            </LinearLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Only track location when driving is detected (saves battery)"
                android:textSize="12sp"
                android:textColor="@android:color/darker_gray"
                android:layout_marginBottom="16dp"/>

            <!-- Speed-Based Frequency Switch -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="8dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Speed-Based Frequency"
                    android:textSize="16sp"/>

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/speed_based_frequency_switch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="true"/>
            </LinearLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Automatically adjust location update frequency based on your speed (saves battery when stationary)"
                android:textSize="12sp"
                android:textColor="@android:color/darker_gray"
                android:layout_marginBottom="8dp"/>
        </LinearLayout>

        <!-- Navigation Features Settings -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/settings_item_background"
            android:padding="16dp"
            android:layout_marginBottom="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_navigation"
                    android:layout_marginEnd="8dp"
                    android:tint="@color/zatani_primary"
                    android:contentDescription="Navigation features icon"/>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Navigation Features"
                    style="@style/SettingsTitleStyle"/>
            </LinearLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Configure real-time navigation features"
                style="@style/SettingsDescriptionStyle"
                android:layout_marginBottom="16dp"/>

            <!-- Real-time Distance Updates Switch -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="8dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Real-time Distance Updates"
                    android:textSize="16sp"/>

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/realtime_updates_switch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="true"/>
            </LinearLayout>

            <!-- Keep Screen On Switch -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="8dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Keep Screen On"
                    android:textSize="16sp"/>

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/keep_screen_on_switch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="false"/>
            </LinearLayout>

            <!-- Voice Announcements Switch -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="8dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Voice Announcements"
                    android:textSize="16sp"/>

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/voice_announcements_switch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="false"/>
            </LinearLayout>

            <!-- Voice Announcement Interval -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Announcement Interval"
                android:textSize="16sp"
                android:layout_marginTop="8dp"/>

            <SeekBar
                android:id="@+id/voice_announcement_interval_seekbar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                style="@style/SettingsSeekBarStyle"/>

            <TextView
                android:id="@+id/voice_announcement_interval_value_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="100 meters"
                style="@style/SettingsValueStyle"/>
        </LinearLayout>

        <!-- Report Type Notification Settings -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/settings_item_background"
            android:padding="16dp"
            android:layout_marginBottom="50dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="8dp">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_notification"
                    android:layout_marginEnd="8dp"
                    android:tint="@color/whatsapp_dark_green"
                    android:contentDescription="Notification icon" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/report_notifications_title"
                    style="@style/SettingsTitleStyle" />
            </LinearLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/report_notifications_description"
                style="@style/SettingsDescriptionStyle"
                android:layout_marginBottom="12dp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#E0E0E0"
                android:layout_marginBottom="8dp" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/report_notification_recycler_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:nestedScrollingEnabled="false" />
        </LinearLayout>

        <!-- Apply Settings Button -->
        <Button
            android:id="@+id/apply_settings_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/apply_settings"
            android:textAllCaps="false"
            android:padding="12dp"
            android:background="@drawable/settings_item_background"
            android:textColor="@color/white"
            android:backgroundTint="@color/zatani_primary" />

    </LinearLayout>
</ScrollView>
