<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="8dp"
    android:background="@drawable/debug_background"
    android:layout_margin="8dp">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Speed-Based Location Debug"
        android:textStyle="bold"
        android:textSize="14sp"
        android:textColor="@android:color/black"
        android:layout_marginBottom="4dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/statusIndicator"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:text="●"
            android:textSize="12sp"
            android:gravity="center"
            android:layout_marginEnd="8dp"
            android:background="@android:color/darker_gray" />

        <TextView
            android:id="@+id/speedCategoryText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Speed Category: Stationary"
            android:textSize="12sp"
            android:textColor="@android:color/darker_gray" />

    </LinearLayout>

    <TextView
        android:id="@+id/averageSpeedText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Average Speed: 0.0 km/h"
        android:textSize="12sp"
        android:textColor="@android:color/darker_gray"
        android:layout_marginTop="2dp" />

    <TextView
        android:id="@+id/updateIntervalText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="UI Update Interval: 30s"
        android:textSize="12sp"
        android:textColor="@android:color/darker_gray"
        android:layout_marginTop="2dp" />

</LinearLayout>
