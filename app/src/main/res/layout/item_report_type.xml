<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="10dp"
    android:layout_marginHorizontal="12dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:strokeWidth="0dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:id="@+id/reportTriggerSection"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@drawable/report_trigger_ripple"
            android:orientation="horizontal"
            android:padding="20dp"
            android:elevation="2dp"
            app:layout_constraintEnd_toStartOf="@id/reportCountSection"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintWidth_percent="0.7">

            <ImageView
                android:id="@+id/reportTypeIcon"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:padding="2dp"
                android:layout_gravity="center_vertical" />

            <TextView
                android:id="@+id/reportTypeTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="16dp"
                android:textSize="16sp"
                android:text="test"
                android:textStyle="bold"
                android:textColor="#FFFFFF"
                android:shadowColor="#33000000"
                android:shadowDx="1"
                android:textAllCaps="true"
                android:shadowDy="1"
                android:shadowRadius="1" />
        </LinearLayout>

        <View
            android:id="@+id/divider"
            android:layout_width="1dp"
            android:layout_height="0dp"
            android:background="#33FFFFFF"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/reportCountSection"
            app:layout_constraintStart_toEndOf="@id/reportTriggerSection"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:id="@+id/reportCountSection"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/report_count_ripple"
            android:orientation="vertical"
            android:gravity="center"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            android:paddingHorizontal="8dp"
            android:elevation="2dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/reportTriggerSection"
            app:layout_constraintTop_toTopOf="parent">

            <!-- Count and label in a single row -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center">

                <!-- Count number with orange badge -->
                <TextView
                    android:id="@+id/reportCount"
                    android:layout_width="22dp"
                    android:layout_height="22dp"
                    android:background="@drawable/orange_badge_background"
                    android:gravity="center"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/white" />

                <!-- Reports label -->
                <TextView
                    android:id="@+id/reportsLabel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="reports"
                    android:textSize="10sp"
                    android:textColor="@color/white"
                    android:includeFontPadding="false"
                    android:layout_marginTop="1dp" />
            </LinearLayout>

            <!-- View Details button -->
            <TextView
                android:id="@+id/viewButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="View"
                android:textSize="12sp"
                android:textColor="@color/white"
                android:background="@drawable/view_details_button"
                android:paddingHorizontal="6dp"
                android:paddingVertical="2dp"
                android:layout_marginTop="4dp"
                android:includeFontPadding="false" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>