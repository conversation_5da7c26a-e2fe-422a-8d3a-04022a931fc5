<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="8dp"
    android:gravity="center_vertical">

    <ImageView
        android:id="@+id/reportTypeIcon"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginEnd="16dp"
        android:src="@drawable/ic_default"
        android:background="@drawable/circle_icon_background"
        android:padding="6dp"
        android:contentDescription="Report type icon" />

    <TextView
        android:id="@+id/reportTypeName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:textSize="16sp"
        android:textColor="@color/black"
        android:textStyle="bold"
        android:textAllCaps="true"
        android:text="Report Type Name" />

    <androidx.appcompat.widget.SwitchCompat
        android:id="@+id/notificationToggle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:checked="true" />

</LinearLayout>
