<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.Toolbar
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/toolbar"
    android:layout_width="match_parent"
    android:layout_height="?attr/actionBarSize"
    android:background="?attr/colorPrimary"
    android:elevation="4dp"
    app:contentInsetStartWithNavigation="0dp"
    app:contentInsetStart="16dp"
    app:popupTheme="@style/PopupMenuStyle">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/toolbar_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/app_name"
            style="@style/ZataniTitleStyle"
            android:layout_marginStart="0dp"
            tools:text="Zatani" />

        <Space
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />

        <!-- We don't need custom icons here as we're using the standard menu system -->
        <!-- The menu items will be inflated by the fragment-specific menu providers -->

    </LinearLayout>

</androidx.appcompat.widget.Toolbar>
