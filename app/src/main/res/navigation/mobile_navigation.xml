<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mobile_navigation"
    app:startDestination="@id/navigation_home">

    <fragment
        android:id="@+id/navigation_home"
        android:name="com.logikden.eventmanager.ui.home.HomeFragment"
        android:label="@string/title_home"
        tools:layout="@layout/fragment_home" >
        <action
            android:id="@+id/action_navigation_home_to_customEventFragment"
            app:destination="@id/customEventFragment" />
        <action
            android:id="@+id/action_navigation_home_to_eventTrackingFragment"
            app:destination="@id/eventTrackingFragment" />
        <action
            android:id="@+id/action_navigation_home_to_eventTrackingFragment2"
            app:destination="@id/eventTrackingFragment" />
        <action
            android:id="@+id/action_navigation_home_to_navigation_events"
            app:destination="@id/navigation_events" />
        <action
            android:id="@+id/action_navigation_home_to_navigation_events2"
            app:destination="@id/navigation_events" />
        <action
            android:id="@+id/action_navigation_home_to_navigation_subscriptions"
            app:destination="@id/navigation_subscriptions" />
    </fragment>

    <fragment
        android:id="@+id/navigation_events"
        android:name="com.logikden.eventmanager.ui.events.EventsFragment"
        android:label="Events"
        tools:layout="@layout/fragment_events">
        <argument
            android:name="expandReportType"
            app:argType="string"
            android:defaultValue='""' />
        <action
            android:id="@+id/action_navigation_events_to_eventTrackingFragment"
            app:destination="@id/eventTrackingFragment" />
        <action
            android:id="@+id/action_navigation_events_to_navigation_home"
            app:destination="@id/navigation_home" />
    </fragment>

    <fragment
        android:id="@+id/navigation_subscriptions"
        android:name="com.logikden.eventmanager.ui.subscriptions.SubscriptionsFragment"
        android:label="@string/subscribe"
        tools:layout="@layout/fragment_subscriptions" >
        <action
            android:id="@+id/action_navigation_subscriptions_to_navigation_home"
            app:destination="@id/navigation_home" />
    </fragment>
    <fragment
        android:id="@+id/customEventFragment"
        android:name="com.logikden.eventmanager.ui.home.CustomEventFragment"
        android:label="Custom event"
        tools:layout="@layout/fragment_custom_event" >
        <action
            android:id="@+id/action_customEventFragment_to_eventTrackingFragment"
            app:destination="@id/eventTrackingFragment" />
    </fragment>
    <fragment
        android:id="@+id/addLocationFragment"
        android:name="com.logikden.eventmanager.ui.temporary.AddLocationFragment"
        android:label="Add Location" />
    <fragment
        android:id="@+id/settingsFragment"
        android:name="com.logikden.eventmanager.ui.settings.SettingsFragment"
        android:label="Settings" />
    <fragment
        android:id="@+id/eventTrackingFragment"
        android:name="com.logikden.eventmanager.ui.events.EventTrackingFragment"
        android:label="Event Tracking">
        <argument
            android:name="eventLat"
            app:argType="float"
            android:defaultValue="0.0" />
        <argument
            android:name="eventLng"
            app:argType="float"
            android:defaultValue="0.0" />
        <argument
            android:name="eventName"
            app:argType="string"
            android:defaultValue='""' />
        <argument
            android:name="sourceFragment"
            app:argType="string"
            android:defaultValue="home" />
        <argument
            android:name="reportTypeId"
            app:argType="integer"
            android:defaultValue="0" />
        <action
            android:id="@+id/action_eventTrackingFragment_to_navigation_home"
            app:destination="@id/navigation_home" />
        <action
            android:id="@+id/action_eventTrackingFragment_to_navigation_events"
            app:destination="@id/navigation_events" />
    </fragment>
    <action
        android:id="@+id/action_navigation_events_to_eventTrackingFragment"
        app:destination="@id/eventTrackingFragment" />
</navigation>