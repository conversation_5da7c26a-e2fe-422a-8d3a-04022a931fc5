<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Background track -->
    <item android:id="@android:id/background">
        <shape android:shape="rectangle">
            <corners android:radius="4dp" />
            <solid android:color="@color/zatani_seekbar_background" />
            <size android:height="6dp" />
        </shape>
    </item>
    
    <!-- Secondary progress (not used but required) -->
    <item android:id="@android:id/secondaryProgress">
        <clip>
            <shape android:shape="rectangle">
                <corners android:radius="4dp" />
                <solid android:color="@color/zatani_seekbar_background" />
                <size android:height="6dp" />
            </shape>
        </clip>
    </item>
    
    <!-- Progress -->
    <item android:id="@android:id/progress">
        <clip>
            <shape android:shape="rectangle">
                <corners android:radius="4dp" />
                <solid android:color="@color/zatani_primary" />
                <size android:height="6dp" />
            </shape>
        </clip>
    </item>
</layer-list>
