<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="Theme.EventManager" parent="Theme.MaterialComponents.Light.DarkActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/zatani_primary</item>
        <item name="colorPrimaryVariant">@color/zatani_primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/zatani_secondary</item>
        <item name="colorSecondaryVariant">@color/zatani_secondary_dark</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <!-- Background color. -->
        <item name="android:windowBackground">@color/zatani_background</item>
        <!-- Text colors. -->
        <item name="android:textColorPrimary">@color/zatani_text_primary</item>
        <item name="android:textColorSecondary">@color/zatani_text_secondary</item>
        <!-- Action bar styling -->
        <item name="colorControlNormal">@android:color/white</item>
        <item name="actionMenuTextColor">@android:color/white</item>
        <item name="android:actionMenuTextColor">@android:color/white</item>
        <item name="iconTint">@android:color/white</item>
        <item name="android:iconTint">@android:color/white</item>
        <item name="actionButtonStyle">@style/ActionBarMenuItemStyle</item>
        <item name="android:actionBarStyle">@style/AppTheme.ActionBar</item>
        <item name="actionBarStyle">@style/AppTheme.ActionBar</item>
    </style>
</resources>