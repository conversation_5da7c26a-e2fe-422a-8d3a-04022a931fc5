<resources xmlns:tools="http://schemas.android.com/tools">
    <style name="ToolbarTitleStyle" parent="TextAppearance.MaterialComponents.Headline6">
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:textColor">@android:color/white</item>
    </style>

    <style name="ZataniTitleStyle" parent="TextAppearance.MaterialComponents.Headline6">
        <!-- Text size is set programmatically based on screen -->
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:letterSpacing">0.0</item>
        <item name="android:shadowColor">#33000000</item>
        <item name="android:shadowDx">0</item>
        <item name="android:shadowDy">1</item>
        <item name="android:shadowRadius">1</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <!-- Style for the overflow menu button (3 dots) -->
    <style name="OverflowButtonStyle" parent="Widget.AppCompat.ActionButton.Overflow">
        <item name="android:tint">@android:color/white</item>
        <item name="android:src">@drawable/ic_more_vert_white</item>
    </style>

    <!-- Style for the popup menu -->
    <style name="PopupMenuStyle" parent="ThemeOverlay.AppCompat.Light">
        <item name="android:textColorPrimary">@color/black</item>
        <item name="android:textColorSecondary">@color/black</item>
        <item name="android:background">@android:color/white</item>
    </style>

    <!-- Style for action bar menu items -->
    <style name="ActionBarMenuItemStyle" parent="Widget.AppCompat.ActionButton">
        <item name="android:tint">@android:color/white</item>
        <item name="iconTint">@android:color/white</item>
        <item name="tint">@android:color/white</item>
    </style>

    <!-- Style for ActionBar title - normal size -->
    <style name="ActionBarTitleStyle" parent="TextAppearance.AppCompat.Widget.ActionBar.Title">
        <item name="android:textSize">20sp</item>
        <item name="android:textColor">@android:color/white</item>
    </style>

    <!-- Style for ActionBar title - large size for home screen -->
    <style name="ActionBarTitleStyleLarge" parent="TextAppearance.AppCompat.Widget.ActionBar.Title">
        <item name="android:textSize">30sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@android:color/white</item>
    </style>

    <!-- Custom ActionBar style -->
    <style name="AppTheme.ActionBar" parent="Widget.AppCompat.ActionBar.Solid">
        <item name="titleTextStyle">@style/ActionBarTitleStyle</item>
        <item name="background">@color/zatani_primary_dark</item>
        <item name="elevation">0dp</item>
    </style>

    <!-- Alert Dialog Theme -->
    <style name="AlertDialogTheme" parent="ThemeOverlay.AppCompat.Dialog.Alert">
        <item name="colorPrimary">@color/zatani_primary</item>
        <item name="colorPrimaryDark">@color/zatani_primary_dark</item>
        <item name="colorAccent">@color/zatani_accent</item>
        <item name="android:textColorPrimary">@color/black</item>
        <item name="android:windowBackground">@android:color/white</item>
        <item name="buttonBarPositiveButtonStyle">@style/AlertDialogButtonStyle</item>
    </style>

    <!-- Alert Dialog Button Style -->
    <style name="AlertDialogButtonStyle" parent="Widget.AppCompat.Button.ButtonBar.AlertDialog">
        <item name="android:textColor">@color/zatani_accent</item>
        <item name="android:textStyle">bold</item>
    </style>
</resources>