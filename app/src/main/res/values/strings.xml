<resources>
    <!-- Settings Screen -->
    <string name="settings_title">Settings</string>
    <string name="moved_update_title">Update Distance</string>
    <string name="moved_update_description">Distance interval to get updated.</string>
    <string name="move_value_format">%d km</string>
    <string name="update_radius_title">Update Radius</string>
    <string name="update_radius_description">Distance in kilometers to search for events around your location</string>
    <string name="radius_value_format">%d km</string>

    <string name="check_frequency_title">Update Frequency</string>
    <string name="check_frequency_description">How often to check for new events (in seconds)</string>
    <string name="frequency_value_format">%d seconds</string>

    <string name="event_expiry_title">Event Expiry Time</string>
    <string name="event_expiry_description">How long events should remain visible after reporting (in hours)</string>
    <string name="expiry_value_format">%d hours</string>

    <string name="notifications_title">Enable Notifications</string>
    <string name="notifications_description">Receive notifications about new events in your area</string>

    <string name="sync_wifi_title">Sync Only on WiFi</string>
    <string name="sync_wifi_description">Only download and sync event data when connected to WiFi</string>

    <string name="save_settings">Save Settings</string>
    <string name="apply_settings">Apply Settings</string>

    <string name="app_name">Zatani</string>
    <string name="title_home">Home</string>
    <string name="title_dashboard">Events</string>
    <string name="title_notifications">Notifications</string>
    <string name="road_accident">Road Accident</string>
    <string name="add_custom_event">Add custom event</string>
    <string name="water_outage">Water Outage</string>
    <string name="power_outage">Power Outage</string>
    <string name="burglary">Burglary</string>
    <string name="road_blocked">Road Blocked</string>
    <string name="traffic_jam">Traffic Jam</string>
    <string name="view_more_events">View More Events</string>
    <string name="subscribe">Subscribe</string>
    <string name="subscriptions">Subscriptions</string>
    <!-- Strings related to login -->
    <string name="prompt_email">Email</string>
    <string name="prompt_password">Password</string>
    <string name="action_sign_in">Sign in or register</string>
    <string name="action_sign_in_short">Sign in</string>
    <string name="welcome">"Welcome!"</string>
    <string name="invalid_username">Not a valid username</string>
    <string name="invalid_password">Password must be >5 characters</string>
    <string name="login_failed">"Login failed"</string>
    <string name="tile_title">Tile Title</string>
    <string name="clear">Clear</string>
    <string name="refresh">Refresh</string>
    <string name="settings">Settings</string>
    <string name="reset_subscriptions">Reset</string>
    <string name="sync_reports">Sync reports</string>
    <string name="refresh_counts">Refresh Counts</string>
    <!-- TODO: Remove or change this placeholder text -->
    <string name="hello_blank_fragment">Hello blank fragment</string>
    <string name="notification_channel_name">Proximity Alerts</string>
    <string name="notification_channel_description">Alerts for nearby locations and events</string>
    <string name="_60_seconds">60 seconds</string>
    <string name="proximity_threshold_format">%.1f km</string>
    <string name="proximity_alert_distance">Proximity Alert Distance</string>
    <string name="proximity_alert_description">Distance at which you\'ll be notified when approaching a location</string>
    <string name="_1_0_km">1.0 km</string>

    <string name="report_notifications_title">Report Type Notifications</string>
    <string name="report_notifications_description">Choose which types of reports you want to receive notifications for</string>

    <string name="notification_cooldown_title">Notification Cooldown</string>
    <string name="notification_cooldown_description">Minimum time between notifications for the same event type</string>
    <string name="notification_cooldown_format">%d seconds</string>

    <!-- Update Required Dialog -->
    <string name="update_required_title">Update Required</string>
    <string name="update_required_message">You are using an outdated version of the app. Please update to the latest version to continue using the app.</string>
    <string name="update_now">Update Now</string>
</resources>
