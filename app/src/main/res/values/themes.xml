<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.EventManager" parent="Theme.MaterialComponents.Light.DarkActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/zatani_primary</item>
        <item name="colorPrimaryVariant">@color/zatani_primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/zatani_secondary</item>
        <item name="colorSecondaryVariant">@color/zatani_secondary_dark</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <!-- Background color. -->
        <item name="android:windowBackground">@color/zatani_background</item>
        <!-- Text colors. -->
        <item name="android:textColorPrimary">@color/zatani_text_primary</item>
        <item name="android:textColorSecondary">@color/zatani_text_secondary</item>
        <!-- Action bar styling -->
        <item name="colorControlNormal">@android:color/white</item>
        <item name="actionMenuTextColor">@android:color/white</item>
        <item name="android:actionMenuTextColor">@android:color/white</item>
        <item name="iconTint">@android:color/white</item>
        <item name="android:iconTint">@android:color/white</item>
        <item name="actionButtonStyle">@style/ActionBarMenuItemStyle</item>
        <item name="android:actionBarStyle">@style/AppTheme.ActionBar</item>
        <item name="actionBarStyle">@style/AppTheme.ActionBar</item>
    </style>
    <style name="Theme.EventManager.Splash" parent="Theme.MaterialComponents.Light.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/zatani_primary</item>
        <item name="colorPrimaryVariant">@color/zatani_primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/zatani_secondary</item>
        <item name="colorSecondaryVariant">@color/zatani_secondary_dark</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <!-- Background color. -->
        <item name="android:windowBackground">@drawable/splash_background</item>
        <!-- Hide action bar and make fullscreen -->
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>
    <!-- Settings Styles -->
    <style name="SettingsTitleStyle">
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/zatani_primary</item>
        <item name="android:layout_marginBottom">4dp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>

    <style name="SettingsDescriptionStyle">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/zatani_text_secondary</item>
        <item name="android:layout_marginBottom">12dp</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>

    <style name="SettingsSeekBarStyle">
        <item name="android:layout_marginTop">8dp</item>
        <item name="android:layout_marginBottom">8dp</item>
        <item name="android:minHeight">48dp</item>
        <item name="android:progressDrawable">@drawable/seekbar_progress_background</item>
        <item name="android:thumb">@drawable/seekbar_thumb</item>
        <item name="android:splitTrack">false</item>
        <item name="android:thumbOffset">0dp</item>
    </style>

    <style name="SettingsValueStyle">
        <item name="android:textAlignment">center</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/zatani_text_primary</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:layout_marginTop">4dp</item>
    </style>
</resources>