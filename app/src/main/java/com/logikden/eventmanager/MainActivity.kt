package com.logikden.eventmanager

import android.app.ActivityManager
import android.app.Application
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.graphics.Typeface
import android.view.Gravity
import android.widget.TextView
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.annotation.RequiresApi
import com.logikden.eventmanager.ui.home.HomeFragment
import androidx.appcompat.app.ActionBar
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.navigation.NavController
import androidx.navigation.NavOptions
import androidx.navigation.findNavController
import androidx.navigation.ui.AppBarConfiguration
import androidx.navigation.ui.setupActionBarWithNavController
import androidx.navigation.ui.setupWithNavController
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.logikden.eventmanager.databinding.ActivityMainBinding
import com.logikden.eventmanager.data.AppDatabase
import com.logikden.eventmanager.services.ProximityService
import com.logikden.eventmanager.services.DataSyncWorker
import com.logikden.eventmanager.services.RetrofitClient
import com.logikden.eventmanager.ui.subscriptions.SubscriptionsFragment
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import androidx.core.app.ActivityCompat
import android.Manifest
import android.app.AlertDialog
import android.os.Build
import android.util.Log
import com.logikden.eventmanager.ui.home.HomeFragmentDirections
import com.logikden.eventmanager.utils.SettingsManager

class MainActivity : AppCompatActivity(), SubscriptionsFragment.OnCheckTilesListener {
    companion object {
        private const val LOCATION_PERMISSION_REQUEST_CODE = 1001
        private const val NOTIFICATION_PERMISSION_REQUEST_CODE = 1002
        private const val ACTIVITY_RECOGNITION_PERMISSION_REQUEST_CODE = 1003
    }

    private lateinit var binding: ActivityMainBinding
    private lateinit var navController: NavController
    private lateinit var database: AppDatabase

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Initialize the database first
        database = AppDatabase.getDatabase(this, lifecycleScope)

        // Setup navigation
        navController = findNavController(R.id.nav_host_fragment_activity_main)

        // Setup bottom navigation
        val bottomNavView: BottomNavigationView = binding.navView
        bottomNavView.setupWithNavController(navController)

        // Set up custom navigation listener
        bottomNavView.setOnItemSelectedListener { menuItem ->
            when (menuItem.itemId) {
                R.id.navigation_home -> {
                    if (navController.currentDestination?.id != R.id.navigation_home) {
                        navController.navigate(R.id.navigation_home)
                    }
                    true
                }
                R.id.navigation_events -> {
                    HomeFragmentDirections.actionNavigationHomeToNavigationEvents()
                    true
                }
                R.id.navigation_subscriptions -> {
                    HomeFragmentDirections.actionNavigationHomeToNavigationSubscriptions()
                    true
                }
                else -> false
            }
        }

        // Set the initial app title
        setActionBarTitle(getString(R.string.app_name), false)

        // Check and request permissions
        checkAndRequestPermissions()

        // Also check if Activity Recognition permission is needed when settings change
        checkActivityRecognitionPermissionOnStartup()

        // Set up navigation
        setupNavigation()

        // Check for active tiles and navigate if necessary
        checkForActiveTiles()

        // Handle notification intent if present
        handleNotificationIntent(intent)
    }

    private fun checkAndRequestPermissions() {
        Log.d("MainActivity", "Checking permissions...")

        // Check location permission
        if (!checkLocationPermission()) {
            Log.d("MainActivity", "Location permission not granted, requesting...")
            requestLocationPermission()
            return // Wait for location permission before checking others
        }

        // Check activity recognition permission for Android 10 and above
        // Only request if Activity Recognition is enabled in settings
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            val settingsManager = SettingsManager.getInstance(this)
            if (settingsManager.isActivityRecognitionEnabled()) {
                if (!checkActivityRecognitionPermission()) {
                    Log.d("MainActivity", "Activity Recognition enabled but permission not granted, requesting...")
                    requestActivityRecognitionPermission()
                    return // Wait for activity recognition permission before checking others
                } else {
                    Log.d("MainActivity", "Activity Recognition permission already granted")
                }
            } else {
                Log.d("MainActivity", "Activity Recognition disabled in settings, skipping permission request")
            }
        } else {
            Log.d("MainActivity", "Android version < 10, Activity Recognition permission not required")
        }

        // Check notification permission for Android 13 and above
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (!checkNotificationPermission()) {
                Log.d("MainActivity", "Notification permission not granted, requesting...")
                requestNotificationPermission()
                return // Wait for notification permission before starting service
            }
        }

        // Start service if all required permissions are granted
        if (checkLocationPermission() && !isServiceRunning(ProximityService::class.java)) {
            Log.d("MainActivity", "All permissions granted, starting ProximityService...")
            ProximityService.startService(this)
        }
    }

    override fun onCheckTilesClicked() {
        checkForActiveTiles()
    }

    private fun checkForActiveTiles() {
        if (!::database.isInitialized) {
            Log.e("MainActivity", "Database not initialized yet")
            return
        }

        // Use a coroutine to perform the database operation
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val activeTiles = database.tileDao().getAllActive()

                withContext(Dispatchers.Main) {
                    if (activeTiles.isEmpty()) {
                        // No active tiles found, navigate to the subscription page
                        navController.navigate(R.id.navigation_subscriptions)
                        // Hide other menu items
                        hideOtherMenuItems()
                    } else {
                        // Active tiles found, show all menu items and navigate to home
                        showAllMenuItems()
                    }
                }
            } catch (e: Exception) {
                Log.e("MainActivity", "Error checking active tiles", e)
            }
        }
    }

    private fun showAllMenuItems() {
        // Get the bottom navigation view
        val bottomNavView: BottomNavigationView = binding.navView

        // Get the menu
        val menu = bottomNavView.menu

        // Show all menu items
        menu.findItem(R.id.navigation_home).isVisible = true
        menu.findItem(R.id.navigation_events).isVisible = true
        menu.findItem(R.id.navigation_subscriptions).isVisible = true
    }

    private fun hideOtherMenuItems() {
        // Get the bottom navigation view
        val bottomNavView: BottomNavigationView = binding.navView

        // Get the menu
        val menu = bottomNavView.menu

        // Hide all menu items except the subscription page
        menu.findItem(R.id.navigation_home).isVisible = false
        menu.findItem(R.id.navigation_events).isVisible = false
        menu.findItem(R.id.navigation_subscriptions).isVisible = true
    }

    private fun setupNavigation() {
        navController = findNavController(R.id.nav_host_fragment_activity_main)
        val appBarConfiguration = AppBarConfiguration(
            setOf(
                R.id.navigation_home,
                R.id.navigation_events,
                R.id.navigation_subscriptions
            )
        )

        // Set up ActionBar with NavController
        setupActionBarWithNavController(navController, appBarConfiguration)

        // Set up bottom navigation
        binding.navView.setupWithNavController(navController)

        // Update ActionBar title based on destination
        navController.addOnDestinationChangedListener { _, destination, _ ->
            when (destination.id) {
                R.id.navigation_home -> {
                    setActionBarTitle("Zatani", true) // Use large style for home screen
                }
                R.id.navigation_events -> {
                    setActionBarTitle("Events", false)
                }
                R.id.navigation_subscriptions -> {
                    setActionBarTitle("Subscriptions", false)
                }
                R.id.settingsFragment -> {
                    setActionBarTitle("Settings", false)
                }
                R.id.customEventFragment -> {
                    setActionBarTitle("Custom event", false)
                }
                R.id.addLocationFragment -> {
                    setActionBarTitle("Add Location", false)
                }
                R.id.eventTrackingFragment -> {
                    setActionBarTitle("Event Tracking", false)
                }
                else -> {
                    // Use the label from the navigation graph if available
                    val label = destination.label?.toString()
                    if (!label.isNullOrEmpty()) {
                        setActionBarTitle(label, false)
                    } else {
                        setActionBarTitle("Zatani", false)
                    }
                }
            }

            // Invalidate options menu when destination changes
            invalidateOptionsMenu()
        }

        // Example: Home button in a BottomNavigationView
        binding.navView.setOnClickListener() { menuItem ->
            when (menuItem.id) {
                R.id.navigation_home -> {
                    navigateToHome(navController)
                    true
                }
                else -> false
            }
        }

        // Navigation is handled by the ActionBar
    }

    private fun navigateToHome(navController: NavController) {
        navController.popBackStack(R.id.navigation_home, false) // Navigate to HomeFragment
    }

    /**
     * Sets the ActionBar title with the appropriate style
     * @param title The title text to display
     * @param isLarge Whether to use the large style (for home screen)
     */
    private fun setActionBarTitle(title: String, isLarge: Boolean = false) {
        supportActionBar?.let { actionBar ->
            // Set the title text
            actionBar.title = title

            // Create a custom TextView with the desired style
            val customTextView = TextView(this).apply {
                text = title
                if (isLarge) {
                    textSize = 30f
                    setTypeface(typeface, Typeface.BOLD)
                } else {
                    textSize = 20f
                }
                setTextColor(ContextCompat.getColor(context, android.R.color.white))
                gravity = Gravity.START
                setPadding(16, 0, 16, 0) // Add some padding

                // Set layout parameters to match parent width
                layoutParams = ActionBar.LayoutParams(ActionBar.LayoutParams.MATCH_PARENT, ActionBar.LayoutParams.WRAP_CONTENT)
            }

            // Set the custom view on the ActionBar
            actionBar.setDisplayShowCustomEnabled(true)
            actionBar.setDisplayShowTitleEnabled(false) // Hide the default title
            actionBar.customView = customTextView
        }
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        // Clear the menu first to avoid duplicates
        menu.clear()

        // Inflate the appropriate menu based on the current fragment
        val currentFragment = navController.currentDestination?.id
        when (currentFragment) {
            R.id.navigation_home -> {
                menuInflater.inflate(R.menu.home_menu, menu)
                // Update visibility of specific items in home menu
                val homeFragment = supportFragmentManager.findFragmentById(R.id.nav_host_fragment_activity_main)
                    ?.childFragmentManager?.fragments?.firstOrNull() as? HomeFragment
                homeFragment?.updateMenuVisibility(menu)
            }
            R.id.navigation_subscriptions -> {
                menuInflater.inflate(R.menu.subscriptions_menu, menu)
            }
            R.id.navigation_events -> {
                // If events fragment has its own menu, inflate it here
                menuInflater.inflate(R.menu.home_menu, menu)
            }
            else -> {
                // Default menu for other fragments
                menuInflater.inflate(R.menu.home_menu, menu)
            }
        }

        return true
    }

    @RequiresApi(Build.VERSION_CODES.O)
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        // Handle menu item selection based on the current fragment
        val currentFragment = navController.currentDestination?.id

        return when (item.itemId) {
            R.id.action_refresh -> {
                when (currentFragment) {
                    R.id.navigation_home -> {
                        // Show confirmation dialog for refresh action
                        AlertDialog.Builder(this)
                            .setTitle("Refresh Report Counts")
                            .setMessage("Are you sure you want to refresh all report counts?")
                            .setPositiveButton("Refresh") { _, _ ->
                                // Refresh report counts in home fragment
                                val homeFragment = supportFragmentManager.findFragmentById(R.id.nav_host_fragment_activity_main)
                                    ?.childFragmentManager?.fragments?.firstOrNull() as? HomeFragment
                                homeFragment?.refreshReportCounts()
                                Toast.makeText(this, "Refreshing report counts...", Toast.LENGTH_SHORT).show()
                            }
                            .setNegativeButton("Cancel", null)
                            .show()
                    }
                    R.id.navigation_subscriptions -> {
                        // Show confirmation dialog for refresh action
                        AlertDialog.Builder(this)
                            .setTitle("Refresh Subscriptions")
                            .setMessage("Are you sure you want to refresh all subscriptions?")
                            .setPositiveButton("Refresh") { _, _ ->
                                // Handle refresh in subscriptions fragment
                                val subscriptionsFragment = supportFragmentManager.findFragmentById(R.id.nav_host_fragment_activity_main)
                                    ?.childFragmentManager?.fragments?.firstOrNull() as? SubscriptionsFragment
                                subscriptionsFragment?.refreshData()
                                Toast.makeText(this, "Refreshing subscriptions...", Toast.LENGTH_SHORT).show()
                            }
                            .setNegativeButton("Cancel", null)
                            .show()
                    }
                    else -> {
                        Toast.makeText(this, "Refresh not available on this screen", Toast.LENGTH_SHORT).show()
                    }
                }
                true
            }
            R.id.action_reset -> {
                // Show confirmation dialog for reset action
                AlertDialog.Builder(this)
                    .setTitle("Reset Subscriptions")
                    .setMessage("Are you sure you want to reset all subscriptions? This action cannot be undone.")
                    .setPositiveButton("Reset") { _, _ ->
                        when (currentFragment) {
                            R.id.navigation_home -> {
                                // Reset subscriptions from home fragment
                                val homeFragment = supportFragmentManager.findFragmentById(R.id.nav_host_fragment_activity_main)
                                    ?.childFragmentManager?.fragments?.firstOrNull() as? HomeFragment
                                homeFragment?.resetSubscriptions()
                            }
                            R.id.navigation_subscriptions -> {
                                // Reset subscriptions from subscriptions fragment
                                val subscriptionsFragment = supportFragmentManager.findFragmentById(R.id.nav_host_fragment_activity_main)
                                    ?.childFragmentManager?.fragments?.firstOrNull() as? SubscriptionsFragment
                                subscriptionsFragment?.resetAllSubscriptions()
                            }
                        }
                        Toast.makeText(this, "Resetting subscriptions...", Toast.LENGTH_SHORT).show()
                    }
                    .setNegativeButton("Cancel", null)
                    .show()
                true
            }
            R.id.action_settings -> {
                // Navigate to settings (common for all fragments)
                navController.navigate(R.id.settingsFragment)
                true
            }
            R.id.action_sync -> {
                // Sync reports (only available in home fragment)
                if (currentFragment == R.id.navigation_home) {
                    // Show confirmation dialog for sync action
                    AlertDialog.Builder(this)
                        .setTitle("Sync Reports")
                        .setMessage("Are you sure you want to sync all reports? This may take some time.")
                        .setPositiveButton("Sync") { _, _ ->
                            val homeFragment = supportFragmentManager.findFragmentById(R.id.nav_host_fragment_activity_main)
                                ?.childFragmentManager?.fragments?.firstOrNull() as? HomeFragment
                            homeFragment?.syncReports()
                            Toast.makeText(this, "Syncing reports...", Toast.LENGTH_SHORT).show()
                        }
                        .setNegativeButton("Cancel", null)
                        .show()
                } else {
                    Toast.makeText(this, "Sync only available on Home screen", Toast.LENGTH_SHORT).show()
                }
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        // Let the NavController handle the up button
        return navController.navigateUp() || super.onSupportNavigateUp()
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        // Handle the new intent
        handleNotificationIntent(intent)
    }

    private fun checkLocationPermission(): Boolean {
        return checkSelfPermission(Manifest.permission.ACCESS_FINE_LOCATION) ==
                PackageManager.PERMISSION_GRANTED
    }

    private fun checkNotificationPermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            checkSelfPermission(Manifest.permission.POST_NOTIFICATIONS) ==
                    PackageManager.PERMISSION_GRANTED
        } else {
            true
        }
    }

    private fun checkActivityRecognitionPermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            checkSelfPermission(Manifest.permission.ACTIVITY_RECOGNITION) ==
                    PackageManager.PERMISSION_GRANTED
        } else {
            true // Permission not required for Android 9 and below
        }
    }

    private fun requestLocationPermission() {
        val permissions = mutableListOf(
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.ACCESS_COARSE_LOCATION
        )

        // Add background location permission for Android 10+ (API 29+)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            permissions.add(Manifest.permission.ACCESS_BACKGROUND_LOCATION)
        }

        ActivityCompat.requestPermissions(
            this,
            permissions.toTypedArray(),
            LOCATION_PERMISSION_REQUEST_CODE
        )
    }

    private fun requestNotificationPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ActivityCompat.requestPermissions(
                this,
                arrayOf(Manifest.permission.POST_NOTIFICATIONS),
                NOTIFICATION_PERMISSION_REQUEST_CODE
            )
        }
    }

    private fun requestActivityRecognitionPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            Log.d("MainActivity", "Requesting Activity Recognition permission...")

            // Show explanation dialog before requesting permission
            AlertDialog.Builder(this)
                .setTitle("Activity Recognition Permission")
                .setMessage("This app uses Activity Recognition to optimize battery usage by only tracking your location when you're driving. This helps save battery while ensuring you receive proximity alerts when needed.")
                .setPositiveButton("Grant Permission") { _, _ ->
                    ActivityCompat.requestPermissions(
                        this,
                        arrayOf(Manifest.permission.ACTIVITY_RECOGNITION),
                        ACTIVITY_RECOGNITION_PERMISSION_REQUEST_CODE
                    )
                }
                .setNegativeButton("Skip") { _, _ ->
                    Log.d("MainActivity", "User skipped Activity Recognition permission")
                    Toast.makeText(
                        this,
                        "Activity recognition permission skipped. The app will work normally but may use more battery.",
                        Toast.LENGTH_LONG
                    ).show()
                    // Continue with next permission check
                    checkAndRequestPermissions()
                }
                .setCancelable(false)
                .show()
        }
    }

    private fun isServiceRunning(serviceClass: Class<*>): Boolean {
        val manager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        for (service in manager.getRunningServices(Integer.MAX_VALUE)) {
            if (serviceClass.name == service.service.className) {
                return true
            }
        }
        return false
    }

    /**
     * Handles intents from notifications to navigate to the EventTrackingFragment
     */
    private fun handleNotificationIntent(intent: Intent?) {
        if (intent?.action == ProximityService.ACTION_OPEN_EVENT_TRACKING) {
            // Extract event data from intent
            val eventName = intent.getStringExtra(ProximityService.EXTRA_EVENT_NAME) ?: return
            val eventLat = intent.getFloatExtra(ProximityService.EXTRA_EVENT_LAT, 0f)
            val eventLng = intent.getFloatExtra(ProximityService.EXTRA_EVENT_LNG, 0f)

            // Log the received data
            Log.d("MainActivity", "Received notification intent for event: $eventName at $eventLat,$eventLng")

            // Create navigation arguments
            val bundle = Bundle().apply {
                putFloat("eventLat", eventLat)
                putFloat("eventLng", eventLng)
                putString("eventName", eventName)
                putString("sourceFragment", "notification")
                putInt("reportTypeId", 0) // Default value for notifications
            }

            // Create navigation options to clear the back stack
            val navOptions = NavOptions.Builder()
                .setPopUpTo(R.id.navigation_home, false)
                .build()

            // Navigate to the EventTrackingFragment
            try {
                // Make sure navigation is initialized
                if (::navController.isInitialized) {
                    navController.navigate(R.id.eventTrackingFragment, bundle, navOptions)
                } else {
                    // If navigation isn't initialized yet, store the data to navigate later
                    lifecycleScope.launch(Dispatchers.Main) {
                        // Wait for navigation to be initialized
                        navController = findNavController(R.id.nav_host_fragment_activity_main)
                        navController.navigate(R.id.eventTrackingFragment, bundle, navOptions)
                    }
                }
            } catch (e: Exception) {
                Log.e("MainActivity", "Error navigating to EventTrackingFragment", e)
                Toast.makeText(this, "Error opening event tracking", Toast.LENGTH_SHORT).show()
            }
        }
    }

    /**
     * Check if Activity Recognition permission is needed on app startup
     * This handles cases where user enabled Activity Recognition but permission wasn't granted
     */
    private fun checkActivityRecognitionPermissionOnStartup() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            val settingsManager = SettingsManager.getInstance(this)
            if (settingsManager.isActivityRecognitionEnabled() && !checkActivityRecognitionPermission()) {
                Log.d("MainActivity", "Activity Recognition enabled but permission not granted on startup")
                // Don't automatically request permission on startup, just log it
                // User can enable it through settings if needed
            }
        }
    }

    /**
     * Public method to request Activity Recognition permission from Settings
     * This can be called when user enables Activity Recognition in settings
     */
    fun requestActivityRecognitionPermissionFromSettings() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            if (!checkActivityRecognitionPermission()) {
                Log.d("MainActivity", "Requesting Activity Recognition permission from Settings...")
                requestActivityRecognitionPermission()
            } else {
                Toast.makeText(this, "Activity Recognition permission already granted", Toast.LENGTH_SHORT).show()
            }
        } else {
            Toast.makeText(this, "Activity Recognition permission not required on this Android version", Toast.LENGTH_SHORT).show()
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        Log.d("MainActivity", "Permission result received for request code: $requestCode")

        when (requestCode) {
            LOCATION_PERMISSION_REQUEST_CODE -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    Log.d("MainActivity", "Location permission granted")
                    Toast.makeText(this, "Location permission granted", Toast.LENGTH_SHORT).show()
                    // Continue with next permission check
                    checkAndRequestPermissions()
                } else {
                    Log.w("MainActivity", "Location permission denied")
                    Toast.makeText(
                        this,
                        "Location permission is required for proximity monitoring",
                        Toast.LENGTH_LONG
                    ).show()
                }
            }
            ACTIVITY_RECOGNITION_PERMISSION_REQUEST_CODE -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    Log.d("MainActivity", "Activity Recognition permission granted")
                    Toast.makeText(this, "Activity recognition permission granted", Toast.LENGTH_SHORT).show()
                    // Continue with next permission check
                    checkAndRequestPermissions()
                } else {
                    Log.w("MainActivity", "Activity Recognition permission denied")
                    Toast.makeText(
                        this,
                        "Activity recognition permission helps optimize battery usage",
                        Toast.LENGTH_LONG
                    ).show()
                    // Continue even if denied - app can work without activity recognition
                    checkAndRequestPermissions()
                }
            }
            NOTIFICATION_PERMISSION_REQUEST_CODE -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    Toast.makeText(this, "Notification permission granted", Toast.LENGTH_SHORT).show()
                } else {
                    Toast.makeText(
                        this,
                        "Notifications are required for proximity alerts",
                        Toast.LENGTH_LONG
                    ).show()
                }
                // Start service regardless of notification permission result
                if (checkLocationPermission() && !isServiceRunning(ProximityService::class.java)) {
                    ProximityService.startService(this)
                }

                // Start background data sync worker
                DataSyncWorker.startPeriodicSync(this)
            }
        }
    }
}

class MyApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        RetrofitClient.init(this)
    }
}