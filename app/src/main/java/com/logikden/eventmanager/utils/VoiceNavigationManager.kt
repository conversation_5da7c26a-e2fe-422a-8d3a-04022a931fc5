package com.logikden.eventmanager.utils

import android.content.Context
import android.location.Location
import android.util.Log
import com.logikden.eventmanager.services.LocationService
import kotlinx.coroutines.*

/**
 * Manages voice navigation announcements with distance-based speech patterns
 * Integrates with speed-based location tracking for battery optimization
 */
class VoiceNavigationManager private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "VoiceNavigationManager"
        
        @Volatile
        private var INSTANCE: VoiceNavigationManager? = null
        
        fun getInstance(context: Context): VoiceNavigationManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: VoiceNavigationManager(context.applicationContext).also { INSTANCE = it }
            }
        }
        
        // Distance thresholds for announcements
        private const val ARRIVAL_THRESHOLD = 50f // 50 meters - "You have arrived"
        private const val CLOSE_THRESHOLD_1 = 200f // 200 meters
        private const val CLOSE_THRESHOLD_2 = 500f // 500 meters
        private const val FAR_THRESHOLD = 1000f // 1 km - switch to km announcements
        private const val KM_ANNOUNCEMENT_INTERVAL = 1000f // 1 km intervals
        
        // GPS accuracy tolerance for direction detection
        private const val GPS_ACCURACY_TOLERANCE = 20f // 20 meters tolerance for GPS fluctuations
        private const val MIN_DISTANCE_CHANGE = 10f // Minimum distance change to consider movement
        private const val DIRECTION_SAMPLES = 3 // Number of samples to determine direction trend
    }
    
    private val settingsManager = SettingsManager.getInstance(context)
    private val textToSpeechManager = TextToSpeechManager.getInstance(context)
    private val speedBasedUIManager = SpeedBasedUIManager.getInstance()
    
    // Navigation state
    private var isNavigating = false
    private var destinationName: String = ""
    private var destinationLat: Double = 0.0
    private var destinationLng: Double = 0.0
    
    // Distance tracking for direction logic
    private val distanceHistory = mutableListOf<Float>()
    private var lastAnnouncedDistance: Float = -1f
    private var lastAnnouncementTime: Long = 0
    private var isMovingTowardsDestination = true
    private var hasAnnouncedArrival = false
    
    // Announcement tracking
    private val announcedDistances = mutableSetOf<Int>()
    
    // Coroutine scope for distance checking
    private var navigationJob: Job? = null
    
    /**
     * Start voice navigation to a destination
     */
    fun startNavigation(destinationName: String, lat: Double, lng: Double) {
        Log.d(TAG, "Starting voice navigation to $destinationName")
        
        // Stop any existing navigation
        stopNavigation()
        
        // Initialize navigation state
        this.destinationName = destinationName
        this.destinationLat = lat
        this.destinationLng = lng
        this.isNavigating = true
        this.hasAnnouncedArrival = false
        
        // Clear tracking data
        distanceHistory.clear()
        announcedDistances.clear()
        lastAnnouncedDistance = -1f
        lastAnnouncementTime = 0
        isMovingTowardsDestination = true
        
        // Initialize TTS if needed
        textToSpeechManager.initialize()
        
        // Start monitoring distance changes
        startDistanceMonitoring()
        
        // Make initial announcement
        announceNavigationStart()
    }
    
    /**
     * Stop voice navigation
     */
    fun stopNavigation() {
        Log.d(TAG, "Stopping voice navigation")
        
        isNavigating = false
        navigationJob?.cancel()
        navigationJob = null
        
        // Clear state
        distanceHistory.clear()
        announcedDistances.clear()
        lastAnnouncedDistance = -1f
        isMovingTowardsDestination = true
        hasAnnouncedArrival = false
    }
    
    /**
     * Update current location and check for announcements
     */
    fun updateLocation(location: Location) {
        if (!isNavigating || !settingsManager.isVoiceAnnouncementsEnabled()) {
            return
        }
        
        // Calculate current distance
        val currentDistance = calculateDistance(location)
        if (currentDistance == null) {
            Log.w(TAG, "Could not calculate distance to destination")
            return
        }
        
        // Update direction tracking
        updateDirectionTracking(currentDistance)
        
        // Check if we should make an announcement
        checkForAnnouncement(currentDistance)
    }
    
    /**
     * Calculate distance to destination
     */
    private fun calculateDistance(location: Location): Float? {
        return try {
            val results = FloatArray(1)
            Location.distanceBetween(
                location.latitude, location.longitude,
                destinationLat, destinationLng,
                results
            )
            results[0]
        } catch (e: Exception) {
            Log.e(TAG, "Error calculating distance", e)
            null
        }
    }
    
    /**
     * Update direction tracking to determine if user is moving towards destination
     */
    private fun updateDirectionTracking(currentDistance: Float) {
        // Add current distance to history
        distanceHistory.add(currentDistance)
        
        // Keep only recent samples
        if (distanceHistory.size > DIRECTION_SAMPLES) {
            distanceHistory.removeAt(0)
        }
        
        // Determine direction trend if we have enough samples
        if (distanceHistory.size >= DIRECTION_SAMPLES) {
            val oldestDistance = distanceHistory.first()
            val newestDistance = distanceHistory.last()
            val distanceChange = oldestDistance - newestDistance
            
            // Only update direction if change is significant (accounts for GPS accuracy)
            if (kotlin.math.abs(distanceChange) > GPS_ACCURACY_TOLERANCE) {
                val wasMovingTowards = isMovingTowardsDestination
                isMovingTowardsDestination = distanceChange > 0 // Positive means getting closer
                
                if (wasMovingTowards != isMovingTowardsDestination) {
                    Log.d(TAG, "Direction changed: ${if (isMovingTowardsDestination) "towards" else "away from"} destination")
                }
            }
        }
    }
    
    /**
     * Check if an announcement should be made based on current distance
     */
    private fun checkForAnnouncement(currentDistance: Float) {
        // Don't announce if moving away from destination
        if (!isMovingTowardsDestination) {
            Log.v(TAG, "Not announcing - moving away from destination")
            return
        }
        
        // Check arrival
        if (currentDistance <= ARRIVAL_THRESHOLD && !hasAnnouncedArrival) {
            announceArrival()
            return
        }
        
        // Don't make other announcements if already arrived
        if (hasAnnouncedArrival) {
            return
        }
        
        // Determine what announcement to make based on distance
        val announcementDistance = getAnnouncementDistance(currentDistance)
        if (announcementDistance != null && shouldAnnounceDistance(announcementDistance, currentDistance)) {
            announceDistance(announcementDistance)
        }
    }
    
    /**
     * Get the appropriate announcement distance based on current distance
     */
    private fun getAnnouncementDistance(currentDistance: Float): Int? {
        return when {
            currentDistance <= ARRIVAL_THRESHOLD -> null // Handle arrival separately
            currentDistance <= CLOSE_THRESHOLD_1 -> 200 // 200m announcement
            currentDistance <= CLOSE_THRESHOLD_2 -> 500 // 500m announcement
            currentDistance < FAR_THRESHOLD -> null // No announcements between 500m and 1km
            else -> {
                // For distances >= 1km, announce every 1km
                val kmDistance = (currentDistance / KM_ANNOUNCEMENT_INTERVAL).toInt()
                if (kmDistance >= 1) kmDistance * 1000 else null
            }
        }
    }
    
    /**
     * Check if we should announce this distance
     */
    private fun shouldAnnounceDistance(announcementDistance: Int, currentDistance: Float): Boolean {
        // Don't announce if we've already announced this distance
        if (announcedDistances.contains(announcementDistance)) {
            return false
        }
        
        // For km announcements, only announce when we cross the threshold
        if (announcementDistance >= 1000) {
            val kmThreshold = announcementDistance.toFloat()
            // Announce when we're within 50m of the km mark (accounting for GPS accuracy)
            return currentDistance <= kmThreshold + 50f && currentDistance >= kmThreshold - 50f
        }
        
        // For meter announcements, announce when we're close to the threshold
        return currentDistance <= announcementDistance + 25f && currentDistance >= announcementDistance - 25f
    }
    
    /**
     * Make initial navigation announcement
     */
    private fun announceNavigationStart() {
        if (!settingsManager.isVoiceAnnouncementsEnabled()) return
        
        val message = "Navigation started to $destinationName"
        textToSpeechManager.speak(message)
        Log.d(TAG, "Announced: $message")
    }
    
    /**
     * Announce distance remaining
     */
    private fun announceDistance(distanceMeters: Int) {
        if (!settingsManager.isVoiceAnnouncementsEnabled()) return
        
        val message = if (distanceMeters >= 1000) {
            val km = distanceMeters / 1000
            "${km} kilometer${if (km > 1) "s" else ""} remaining"
        } else {
            "${distanceMeters} meters remaining"
        }
        
        textToSpeechManager.speak(message)
        announcedDistances.add(distanceMeters)
        lastAnnouncedDistance = distanceMeters.toFloat()
        lastAnnouncementTime = System.currentTimeMillis()
        
        Log.d(TAG, "Announced: $message")
    }
    
    /**
     * Announce arrival at destination
     */
    private fun announceArrival() {
        if (!settingsManager.isVoiceAnnouncementsEnabled()) return
        
        val message = "You have arrived at your reported location"
        textToSpeechManager.speak(message)
        hasAnnouncedArrival = true
        
        Log.d(TAG, "Announced: $message")
        
        // Stop navigation after arrival
        stopNavigation()
    }
    
    /**
     * Start monitoring distance changes with speed-based frequency
     */
    private fun startDistanceMonitoring() {
        navigationJob = CoroutineScope(Dispatchers.Main).launch {
            while (isActive && isNavigating) {
                try {
                    // Use speed-based UI manager's current interval for battery optimization
                    val updateInterval = speedBasedUIManager.getCurrentUIUpdateInterval()
                    delay(updateInterval)
                    
                    Log.v(TAG, "Distance monitoring active - interval: ${updateInterval}ms")
                } catch (e: Exception) {
                    Log.e(TAG, "Error in distance monitoring", e)
                    break
                }
            }
        }
    }
    
    /**
     * Check if voice navigation is currently active
     */
    fun isNavigating(): Boolean = isNavigating
    
    /**
     * Get current destination name
     */
    fun getDestinationName(): String = destinationName
    
    /**
     * Get navigation statistics for debugging
     */
    fun getNavigationStatistics(): String {
        return if (isNavigating) {
            "Navigating to: $destinationName | " +
            "Direction: ${if (isMovingTowardsDestination) "towards" else "away"} | " +
            "Announced: ${announcedDistances.size} distances | " +
            "Arrived: $hasAnnouncedArrival"
        } else {
            "Not navigating"
        }
    }
}
