package com.logikden.eventmanager.utils

import android.content.Context
import android.os.Build
import android.speech.tts.TextToSpeech
import android.speech.tts.UtteranceProgressListener
import android.util.Log
import java.util.Locale
import java.util.UUID

/**
 * Utility class to manage Text-to-Speech functionality
 */
class TextToSpeechManager private constructor(private val context: Context) {
    private var textToSpeech: TextToSpeech? = null
    private var isInitialized = false
    private var lastSpokenDistance: Int = -1
    private var lastSpeakTime: Long = 0
    private val SPEAK_COOLDOWN = 3000L // 3 seconds cooldown between announcements

    companion object {
        private const val TAG = "TextToSpeechManager"
        
        @Volatile
        private var instance: TextToSpeechManager? = null

        fun getInstance(context: Context): TextToSpeechManager {
            return instance ?: synchronized(this) {
                instance ?: TextToSpeechManager(context.applicationContext).also { instance = it }
            }
        }
    }

    /**
     * Initialize the TTS engine
     */
    fun initialize() {
        if (textToSpeech == null) {
            textToSpeech = TextToSpeech(context) { status ->
                if (status == TextToSpeech.SUCCESS) {
                    val result = textToSpeech?.setLanguage(Locale.getDefault())
                    if (result == TextToSpeech.LANG_MISSING_DATA || result == TextToSpeech.LANG_NOT_SUPPORTED) {
                        Log.e(TAG, "Language not supported")
                    } else {
                        isInitialized = true
                        Log.d(TAG, "TTS initialized successfully")
                        
                        // Set up utterance progress listener
                        textToSpeech?.setOnUtteranceProgressListener(object : UtteranceProgressListener() {
                            override fun onStart(utteranceId: String?) {
                                Log.d(TAG, "TTS started: $utteranceId")
                            }

                            override fun onDone(utteranceId: String?) {
                                Log.d(TAG, "TTS completed: $utteranceId")
                            }

                            @Deprecated("Deprecated in Java")
                            override fun onError(utteranceId: String?) {
                                Log.e(TAG, "TTS error: $utteranceId")
                            }

                            override fun onError(utteranceId: String?, errorCode: Int) {
                                super.onError(utteranceId, errorCode)
                                Log.e(TAG, "TTS error: $utteranceId, code: $errorCode")
                            }
                        })
                    }
                } else {
                    Log.e(TAG, "TTS initialization failed with status: $status")
                }
            }
        }
    }

    /**
     * Speak any text message with queue management
     * @param message The message to speak
     * @param queueMode How to handle the speech queue (QUEUE_FLUSH or QUEUE_ADD)
     * @return true if message was spoken, false otherwise
     */
    fun speak(message: String, queueMode: Int = TextToSpeech.QUEUE_FLUSH): Boolean {
        if (!isInitialized || textToSpeech == null) {
            Log.d(TAG, "TTS not initialized, cannot speak: $message")
            return false
        }

        val currentTime = System.currentTimeMillis()
        if (currentTime - lastSpeakTime < SPEAK_COOLDOWN && queueMode == TextToSpeech.QUEUE_FLUSH) {
            Log.d(TAG, "Skipping announcement due to cooldown: $message")
            return false
        }

        lastSpeakTime = currentTime
        Log.d(TAG, "Speaking: $message")

        val utteranceId = UUID.randomUUID().toString()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            textToSpeech?.speak(message, queueMode, null, utteranceId)
        } else {
            @Suppress("DEPRECATION")
            textToSpeech?.speak(message, queueMode, null)
        }
        return true
    }

    /**
     * Speak the distance announcement (legacy method for backward compatibility)
     * @param distanceInMeters The distance in meters
     * @param interval The announcement interval in meters
     * @return true if announcement was spoken, false otherwise
     */
    fun announceDistance(distanceInMeters: Float, interval: Int): Boolean {
        if (!isInitialized || textToSpeech == null) {
            Log.d(TAG, "TTS not initialized")
            return false
        }

        val currentTime = System.currentTimeMillis()
        if (currentTime - lastSpeakTime < SPEAK_COOLDOWN) {
            Log.d(TAG, "Skipping announcement due to cooldown")
            return false
        }

        // Round distance to nearest interval
        val roundedDistance = (distanceInMeters / interval).toInt() * interval

        // Only announce if distance has changed by at least one interval
        if (roundedDistance != lastSpokenDistance) {
            lastSpokenDistance = roundedDistance
            lastSpeakTime = currentTime

            val distanceText = if (roundedDistance >= 1000) {
                val kilometers = roundedDistance / 1000f
                "Approaching reported location in ${String.format("%.1f", kilometers)} kilometers"
            } else {
                "Approaching reported location in $roundedDistance meters"
            }

            return speak(distanceText)
        }
        
        return false
    }

    /**
     * Reset the last spoken distance
     */
    fun reset() {
        lastSpokenDistance = -1
    }

    /**
     * Shutdown the TTS engine
     */
    fun shutdown() {
        textToSpeech?.let {
            it.stop()
            it.shutdown()
        }
        textToSpeech = null
        isInitialized = false
        instance = null
    }
}
