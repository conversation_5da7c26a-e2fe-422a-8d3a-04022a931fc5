package com.logikden.eventmanager.utils

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.location.LocationManager
import android.os.Build
import android.provider.Settings
import android.util.Log
import androidx.core.app.ActivityCompat
import com.logikden.eventmanager.services.LocationService

object LocationDebugUtils {
    private const val TAG = "LocationDebugUtils"

    /**
     * Comprehensive location debugging information
     */
    fun getLocationDebugInfo(context: Context): String {
        return buildString {
            appendLine("=== LOCATION DEBUG INFORMATION ===")
            appendLine()
            
            // Basic permission checks
            appendLine("📱 PERMISSION STATUS:")
            appendLine("- Fine Location: ${checkPermission(context, Manifest.permission.ACCESS_FINE_LOCATION)}")
            appendLine("- Coarse Location: ${checkPermission(context, Manifest.permission.ACCESS_COARSE_LOCATION)}")
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                appendLine("- Background Location: ${checkPermission(context, Manifest.permission.ACCESS_BACKGROUND_LOCATION)}")
            }
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                appendLine("- Activity Recognition: ${checkPermission(context, Manifest.permission.ACTIVITY_RECOGNITION)}")
            }
            
            appendLine()
            
            // Location service status
            appendLine("🌍 LOCATION SERVICE STATUS:")
            val locationManager = context.getSystemService(Context.LOCATION_SERVICE) as LocationManager
            
            try {
                val gpsEnabled = locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)
                val networkEnabled = locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)
                val passiveEnabled = locationManager.isProviderEnabled(LocationManager.PASSIVE_PROVIDER)
                
                appendLine("- GPS Provider: $gpsEnabled")
                appendLine("- Network Provider: $networkEnabled")
                appendLine("- Passive Provider: $passiveEnabled")
                appendLine("- Overall Location Enabled: ${gpsEnabled || networkEnabled}")
                
                // Check location mode (Android settings)
                val locationMode = try {
                    Settings.Secure.getInt(context.contentResolver, Settings.Secure.LOCATION_MODE)
                } catch (e: Exception) {
                    -1
                }
                
                appendLine("- Location Mode: ${getLocationModeString(locationMode)}")
                
            } catch (e: Exception) {
                appendLine("- Error checking providers: ${e.message}")
            }
            
            appendLine()
            
            // LocationService status
            appendLine("🔧 LOCATION SERVICE STATUS:")
            try {
                val locationService = LocationService.getInstance(context)
                appendLine("- Permission Check: ${locationService.checkLocationPermission()}")
                appendLine("- Location Enabled Check: ${locationService.isLocationEnabled()}")
                appendLine("- Current Location: ${locationService.getCurrentLocation()}")
                appendLine("- Location State: ${locationService.locationState.value}")
            } catch (e: Exception) {
                appendLine("- Error accessing LocationService: ${e.message}")
            }
            
            appendLine()
            
            // System information
            appendLine("📋 SYSTEM INFORMATION:")
            appendLine("- Android Version: ${Build.VERSION.RELEASE} (API ${Build.VERSION.SDK_INT})")
            appendLine("- Device: ${Build.MANUFACTURER} ${Build.MODEL}")
            appendLine("- App Target SDK: ${context.applicationInfo.targetSdkVersion}")
            
            appendLine()
            
            // Recommendations
            appendLine("💡 TROUBLESHOOTING RECOMMENDATIONS:")
            
            if (!checkPermission(context, Manifest.permission.ACCESS_FINE_LOCATION)) {
                appendLine("❌ Grant Fine Location permission")
            }
            
            if (!locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER) && 
                !locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)) {
                appendLine("❌ Enable Location Services in device settings")
            }
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q && 
                !checkPermission(context, Manifest.permission.ACCESS_BACKGROUND_LOCATION)) {
                appendLine("⚠️ Consider granting Background Location permission for better tracking")
            }
            
            appendLine("✅ Check if battery optimization is disabled for this app")
            appendLine("✅ Ensure Google Play Services is updated")
            appendLine("✅ Try restarting the device if issues persist")
        }
    }
    
    /**
     * Log detailed location debug information
     */
    fun logLocationDebugInfo(context: Context) {
        val debugInfo = getLocationDebugInfo(context)
        Log.i(TAG, debugInfo)
    }
    
    /**
     * Check if a specific permission is granted
     */
    private fun checkPermission(context: Context, permission: String): Boolean {
        return ActivityCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * Convert location mode integer to readable string
     */
    private fun getLocationModeString(mode: Int): String {
        return when (mode) {
            Settings.Secure.LOCATION_MODE_OFF -> "OFF"
            Settings.Secure.LOCATION_MODE_SENSORS_ONLY -> "GPS_ONLY"
            Settings.Secure.LOCATION_MODE_BATTERY_SAVING -> "NETWORK_ONLY"
            Settings.Secure.LOCATION_MODE_HIGH_ACCURACY -> "HIGH_ACCURACY"
            else -> "UNKNOWN ($mode)"
        }
    }
    
    /**
     * Quick check if location is properly configured
     */
    fun isLocationProperlyConfigured(context: Context): Boolean {
        val hasPermission = checkPermission(context, Manifest.permission.ACCESS_FINE_LOCATION)
        val locationManager = context.getSystemService(Context.LOCATION_SERVICE) as LocationManager
        val isEnabled = locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER) ||
                       locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)
        
        return hasPermission && isEnabled
    }
    
    /**
     * Get a simple status message for UI display
     */
    fun getLocationStatusMessage(context: Context): String {
        val hasPermission = checkPermission(context, Manifest.permission.ACCESS_FINE_LOCATION)
        val locationManager = context.getSystemService(Context.LOCATION_SERVICE) as LocationManager
        val isEnabled = locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER) ||
                       locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)
        
        return when {
            !hasPermission -> "Location permission not granted"
            !isEnabled -> "Location services disabled in device settings"
            else -> "Location properly configured"
        }
    }
}
