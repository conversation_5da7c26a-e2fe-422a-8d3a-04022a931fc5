package com.logikden.eventmanager.utils

object LocationConstants {
    // Standard location update intervals (optimized for battery)
    const val UPDATE_INTERVAL = 10000L // 10 seconds
    const val FASTEST_INTERVAL = 5000L // 5 seconds
    const val MIN_DISTANCE_CHANGE = 50f // 50 meters
    const val LOCATION_ACCURACY = 20f // 20 meters accuracy threshold

    // High-frequency update intervals for navigation (when close to destination)
    const val NAVIGATION_UPDATE_INTERVAL = 2000L // 2 seconds
    const val NAVIGATION_FASTEST_INTERVAL = 1000L // 1 second
    const val NAVIGATION_MIN_DISTANCE_CHANGE = 5f // 5 meters

    // Battery-saving update intervals (when far from destination)
    const val BATTERY_SAVING_UPDATE_INTERVAL = 15000L // 15 seconds
    const val BATTERY_SAVING_FASTEST_INTERVAL = 10000L // 10 seconds
    const val BATTERY_SAVING_MIN_DISTANCE_CHANGE = 100f // 100 meters

    // Motion detection intervals (for stationary detection)
    const val STATIONARY_UPDATE_INTERVAL = 60000L // 1 minute when stationary
    const val STATIONARY_FASTEST_INTERVAL = 30000L // 30 seconds when stationary
    const val STATIONARY_MIN_DISTANCE_CHANGE = 200f // 200 meters when stationary

    // Speed-based update intervals
    // Stationary/Very slow (0-5 km/h) - walking speed
    const val SPEED_STATIONARY_UPDATE_INTERVAL = 30000L // 30 seconds
    const val SPEED_STATIONARY_FASTEST_INTERVAL = 15000L // 15 seconds
    const val SPEED_STATIONARY_MIN_DISTANCE = 100f // 100 meters

    // Slow speed (5-25 km/h) - cycling/slow driving in city
    const val SPEED_SLOW_UPDATE_INTERVAL = 10000L // 10 seconds
    const val SPEED_SLOW_FASTEST_INTERVAL = 5000L // 5 seconds
    const val SPEED_SLOW_MIN_DISTANCE = 50f // 50 meters

    // Medium speed (25-60 km/h) - normal city driving
    const val SPEED_MEDIUM_UPDATE_INTERVAL = 5000L // 5 seconds
    const val SPEED_MEDIUM_FASTEST_INTERVAL = 3000L // 3 seconds
    const val SPEED_MEDIUM_MIN_DISTANCE = 25f // 25 meters

    // High speed (60+ km/h) - highway driving
    const val SPEED_HIGH_UPDATE_INTERVAL = 3000L // 3 seconds
    const val SPEED_HIGH_FASTEST_INTERVAL = 1000L // 1 second
    const val SPEED_HIGH_MIN_DISTANCE = 10f // 10 meters

    // Speed thresholds in m/s (converted from km/h)
    const val SPEED_THRESHOLD_STATIONARY = 1.4f // 5 km/h in m/s
    const val SPEED_THRESHOLD_SLOW = 6.9f // 25 km/h in m/s
    const val SPEED_THRESHOLD_MEDIUM = 16.7f // 60 km/h in m/s

    // Minimum number of speed readings to calculate average
    const val MIN_SPEED_READINGS = 3
    // Maximum age of speed readings in milliseconds
    const val MAX_SPEED_READING_AGE = 60000L // 1 minute
}