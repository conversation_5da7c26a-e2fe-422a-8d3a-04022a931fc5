package com.logikden.eventmanager.utils

import android.content.Context
import android.os.PowerManager
import android.util.Log
import android.view.Window
import android.view.WindowManager

/**
 * Utility class to manage wake locks and screen-on functionality
 */
class WakeLockManager private constructor(private val context: Context) {
    private var wakeLock: PowerManager.WakeLock? = null
    private var isScreenLockEnabled = false

    companion object {
        private const val TAG = "WakeLockManager"
        private const val WAKE_LOCK_TAG = "EventManager:TrackingWakeLock"
        
        @Volatile
        private var instance: WakeLockManager? = null

        fun getInstance(context: Context): WakeLockManager {
            return instance ?: synchronized(this) {
                instance ?: WakeLockManager(context.applicationContext).also { instance = it }
            }
        }
    }

    /**
     * Enable or disable keeping the screen on
     * @param window The window to apply the flag to
     * @param enabled Whether to keep the screen on
     */
    fun setKeepScreenOn(window: Window?, enabled: <PERSON>olean) {
        if (window == null) {
            Log.e(TAG, "Window is null, cannot set keep screen on flag")
            return
        }
        
        try {
            if (enabled) {
                window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
                Log.d(TAG, "Keep screen on flag added")
            } else {
                window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
                Log.d(TAG, "Keep screen on flag cleared")
            }
            isScreenLockEnabled = enabled
        } catch (e: Exception) {
            Log.e(TAG, "Error setting keep screen on flag", e)
        }
    }

    /**
     * Acquire a partial wake lock to keep the CPU running
     */
    fun acquireWakeLock() {
        if (wakeLock?.isHeld != true) {
            try {
                val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
                wakeLock = powerManager.newWakeLock(
                    PowerManager.PARTIAL_WAKE_LOCK,
                    WAKE_LOCK_TAG
                ).apply {
                    setReferenceCounted(false)
                    acquire(30 * 60 * 1000L) // 30 minutes timeout
                }
                Log.d(TAG, "Wake lock acquired")
            } catch (e: Exception) {
                Log.e(TAG, "Error acquiring wake lock", e)
            }
        }
    }

    /**
     * Release the wake lock
     */
    fun releaseWakeLock() {
        try {
            if (wakeLock?.isHeld == true) {
                wakeLock?.release()
                Log.d(TAG, "Wake lock released")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error releasing wake lock", e)
        }
    }

    /**
     * Check if screen lock is enabled
     */
    fun isScreenLockEnabled(): Boolean {
        return isScreenLockEnabled
    }

    /**
     * Clean up resources
     */
    fun cleanup() {
        releaseWakeLock()
        wakeLock = null
        instance = null
    }
}
