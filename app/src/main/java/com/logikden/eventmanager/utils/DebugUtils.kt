package com.logikden.eventmanager.utils

import android.content.Context
import android.content.Intent
import com.logikden.eventmanager.ui.debug.ActivityRecognitionDebugActivity

object DebugUtils {

    /**
     * Launch the Activity Recognition debug activity
     */
    fun launchActivityRecognitionDebug(context: Context) {
        val intent = Intent(context, ActivityRecognitionDebugActivity::class.java)
        context.startActivity(intent)
    }

    /**
     * Check if debug mode should be enabled
     * You can modify this to enable debug mode based on build variant or settings
     */
    fun isDebugModeEnabled(): Boolean {
        return true // Set to false for production
    }

    /**
     * Quick debug method to log location status
     */
    fun quickLocationDebug(context: Context) {
        LocationDebugUtils.logLocationDebugInfo(context)
    }
}
