package com.logikden.eventmanager.utils

import android.content.Context
import android.location.Location
import android.util.Log
import com.logikden.eventmanager.data.EventRemoteReport
import com.logikden.eventmanager.data.dto.GroupedEvent
import com.logikden.eventmanager.services.LocationService
import kotlinx.coroutines.*

/**
 * Manages voice announcements for the Events Fragment
 * Provides distance-based announcements for nearby events and event discovery
 */
class EventsVoiceAnnouncementManager private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "EventsVoiceAnnouncementManager"
        
        @Volatile
        private var INSTANCE: EventsVoiceAnnouncementManager? = null
        
        fun getInstance(context: Context): EventsVoiceAnnouncementManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: EventsVoiceAnnouncementManager(context.applicationContext).also { INSTANCE = it }
            }
        }
        
        // Distance thresholds for announcements (same as VoiceNavigationManager)
        private const val ARRIVAL_THRESHOLD = 200f // 200 meters - "Approaching reported location"
        private const val CLOSE_THRESHOLD_1 = 500f // 500 meters
        private const val FAR_THRESHOLD = 1000f // 1 km - switch to km announcements
        private const val KM_ANNOUNCEMENT_INTERVAL = 1000f // 1 km intervals
        
        // GPS accuracy tolerance for direction detection
        private const val GPS_ACCURACY_TOLERANCE = 20f // 20 meters tolerance for GPS fluctuations
        private const val DIRECTION_SAMPLES = 3 // Number of samples to determine direction trend
        
        // Announcement cooldowns
        private const val EVENT_DISCOVERY_COOLDOWN = 30000L // 30 seconds between discovery announcements
        private const val DISTANCE_ANNOUNCEMENT_COOLDOWN = 10000L // 10 seconds between distance announcements
        private const val MAX_DISCOVERY_ANNOUNCEMENTS_PER_SESSION = 5 // Limit discovery announcements
    }
    
    private val settingsManager = SettingsManager.getInstance(context)
    private val textToSpeechManager = TextToSpeechManager.getInstance(context)
    private val speedBasedUIManager = SpeedBasedUIManager.getInstance()
    
    // Event tracking state
    private var isActive = false
    private var currentLocation: Location? = null
    private var currentEvents = listOf<GroupedEvent>()
    
    // Announcement tracking
    private val announcedEvents = mutableMapOf<Int, EventAnnouncementState>()
    private val discoveredEventIds = mutableSetOf<Int>()
    private var lastDiscoveryAnnouncementTime = 0L
    private var discoveryAnnouncementsThisSession = 0
    
    // Distance tracking for direction logic (per event)
    private val eventDistanceHistory = mutableMapOf<Int, MutableList<Float>>()
    
    // Coroutine scope for announcement processing
    private var announcementJob: Job? = null
    
    /**
     * Data class to track announcement state for each event
     */
    private data class EventAnnouncementState(
        var lastAnnouncedDistance: Int = -1,
        var lastAnnouncementTime: Long = 0,
        var isMovingTowards: Boolean = true,
        var hasAnnouncedArrival: Boolean = false,
        var announcedDistances: MutableSet<Int> = mutableSetOf()
    )
    
    /**
     * Start voice announcements for events
     */
    fun startAnnouncements() {
        if (!settingsManager.isVoiceAnnouncementsEnabled()) {
            Log.d(TAG, "Voice announcements disabled in settings")
            return
        }
        
        isActive = true
        textToSpeechManager.initialize()
        
        // Reset session counters
        discoveryAnnouncementsThisSession = 0
        discoveredEventIds.clear()
        
        Log.d(TAG, "Started events voice announcements")
    }
    
    /**
     * Stop voice announcements
     */
    fun stopAnnouncements() {
        isActive = false
        announcementJob?.cancel()
        announcementJob = null
        
        // Clear state
        announcedEvents.clear()
        eventDistanceHistory.clear()
        discoveredEventIds.clear()
        
        Log.d(TAG, "Stopped events voice announcements")
    }
    
    /**
     * Update current location and check for announcements
     */
    fun updateLocation(location: Location) {
        if (!isActive || !settingsManager.isVoiceAnnouncementsEnabled()) {
            return
        }
        
        currentLocation = location
        
        // Process announcements for all current events
        processEventAnnouncements()
    }
    
    /**
     * Update current events list and check for new discoveries
     */
    fun updateEvents(groupedEvents: List<GroupedEvent>) {
        if (!isActive || !settingsManager.isVoiceAnnouncementsEnabled()) {
            return
        }
        
        val previousEvents = currentEvents
        currentEvents = groupedEvents
        
        // Check for newly discovered events
        checkForNewEventDiscoveries(previousEvents, groupedEvents)
        
        // Process announcements for updated events
        processEventAnnouncements()
    }
    
    /**
     * Check for newly discovered events and announce them
     */
    private fun checkForNewEventDiscoveries(previousEvents: List<GroupedEvent>, newEvents: List<GroupedEvent>) {
        // Check if event discovery announcements are enabled
        if (!settingsManager.isEventDiscoveryAnnouncementsEnabled()) {
            return
        }

        if (discoveryAnnouncementsThisSession >= MAX_DISCOVERY_ANNOUNCEMENTS_PER_SESSION) {
            Log.v(TAG, "Max discovery announcements reached for this session")
            return
        }
        
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastDiscoveryAnnouncementTime < EVENT_DISCOVERY_COOLDOWN) {
            Log.v(TAG, "Discovery announcement cooldown active")
            return
        }
        
        // Get all event IDs from previous events
        val previousEventIds = previousEvents.flatMap { group ->
            group.events.map { it.reportId }
        }.toSet()
        
        // Find new events that weren't in the previous list
        val newlyDiscoveredEvents = newEvents.flatMap { group ->
            group.events.filter { event ->
                !previousEventIds.contains(event.reportId) && 
                !discoveredEventIds.contains(event.reportId)
            }
        }
        
        if (newlyDiscoveredEvents.isNotEmpty()) {
            announceNewEventDiscoveries(newlyDiscoveredEvents)
        }
    }
    
    /**
     * Announce newly discovered events
     */
    private fun announceNewEventDiscoveries(newEvents: List<EventRemoteReport>) {
        val location = currentLocation ?: return
        
        // Sort events by distance (closest first)
        val eventsWithDistance = newEvents.mapNotNull { event ->
            val distance = calculateDistance(location, event.latitude, event.longitude)
            if (distance != null) {
                Pair(event, distance)
            } else null
        }.sortedBy { it.second }
        
        // Announce only the closest new event to avoid spam
        val closestNewEvent = eventsWithDistance.firstOrNull() ?: return
        val (event, distance) = closestNewEvent
        
        // Only announce if within reasonable range (5km)
        if (distance <= 5000f) {
            announceEventDiscovery(event, distance)
            
            // Mark as discovered
            discoveredEventIds.add(event.reportId)
            lastDiscoveryAnnouncementTime = System.currentTimeMillis()
            discoveryAnnouncementsThisSession++
        }
    }
    
    /**
     * Announce discovery of a new event
     */
    private fun announceEventDiscovery(event: EventRemoteReport, distance: Float) {
        val eventType = getEventTypeName(event.reportName)
        val distanceText = formatDistanceForAnnouncement(distance)
        
        val message = "New $eventType reported $distanceText away"
        textToSpeechManager.speak(message, android.speech.tts.TextToSpeech.QUEUE_ADD)
        
        Log.d(TAG, "Announced discovery: $message")
    }
    
    /**
     * Process distance-based announcements for all current events
     */
    private fun processEventAnnouncements() {
        val location = currentLocation ?: return
        
        currentEvents.forEach { groupedEvent ->
            groupedEvent.events.forEach { event ->
                processEventDistanceAnnouncement(location, event)
            }
        }
    }
    
    /**
     * Process distance-based announcement for a specific event
     */
    private fun processEventDistanceAnnouncement(location: Location, event: EventRemoteReport) {
        val distance = calculateDistance(location, event.latitude, event.longitude) ?: return
        val eventId = event.reportId
        
        // Get or create announcement state for this event
        val state = announcedEvents.getOrPut(eventId) { EventAnnouncementState() }
        
        // Update direction tracking
        updateEventDirectionTracking(eventId, distance)
        state.isMovingTowards = isMovingTowardsEvent(eventId)
        
        // Don't announce if moving away from event
        if (!state.isMovingTowards) {
            return
        }
        
        // Check for arrival announcement
        if (distance <= ARRIVAL_THRESHOLD && !state.hasAnnouncedArrival) {
            announceEventArrival(event)
            state.hasAnnouncedArrival = true
            return
        }
        
        // Don't make other announcements if already announced arrival
        if (state.hasAnnouncedArrival) {
            return
        }
        
        // Check for distance-based announcements
        val announcementDistance = getAnnouncementDistance(distance)
        if (announcementDistance != null && shouldAnnounceEventDistance(state, announcementDistance, distance)) {
            announceEventDistance(event, announcementDistance)
            state.announcedDistances.add(announcementDistance)
            state.lastAnnouncedDistance = announcementDistance
            state.lastAnnouncementTime = System.currentTimeMillis()
        }
    }
    
    /**
     * Update direction tracking for a specific event
     */
    private fun updateEventDirectionTracking(eventId: Int, currentDistance: Float) {
        val history = eventDistanceHistory.getOrPut(eventId) { mutableListOf() }
        
        // Add current distance to history
        history.add(currentDistance)
        
        // Keep only recent samples
        if (history.size > DIRECTION_SAMPLES) {
            history.removeAt(0)
        }
    }
    
    /**
     * Check if user is moving towards a specific event
     */
    private fun isMovingTowardsEvent(eventId: Int): Boolean {
        val history = eventDistanceHistory[eventId] ?: return true
        
        if (history.size < DIRECTION_SAMPLES) {
            return true // Assume moving towards if not enough data
        }
        
        val oldestDistance = history.first()
        val newestDistance = history.last()
        val distanceChange = oldestDistance - newestDistance
        
        // Only update direction if change is significant (accounts for GPS accuracy)
        return if (kotlin.math.abs(distanceChange) > GPS_ACCURACY_TOLERANCE) {
            distanceChange > 0 // Positive means getting closer
        } else {
            true // Assume moving towards if change is within GPS tolerance
        }
    }
    
    /**
     * Calculate distance between current location and event
     */
    private fun calculateDistance(location: Location, eventLat: Double, eventLng: Double): Float? {
        return try {
            val results = FloatArray(1)
            Location.distanceBetween(
                location.latitude, location.longitude,
                eventLat, eventLng,
                results
            )
            results[0]
        } catch (e: Exception) {
            Log.e(TAG, "Error calculating distance to event", e)
            null
        }
    }
    
    /**
     * Get appropriate announcement distance based on current distance
     */
    private fun getAnnouncementDistance(currentDistance: Float): Int? {
        return when {
            currentDistance <= ARRIVAL_THRESHOLD -> null // Handle arrival separately
            currentDistance <= CLOSE_THRESHOLD_1 -> 500 // 500m announcement
            currentDistance < FAR_THRESHOLD -> null // No announcements between 500m and 1km
            else -> {
                // For distances >= 1km, announce every 1km
                val kmDistance = (currentDistance / KM_ANNOUNCEMENT_INTERVAL).toInt()
                if (kmDistance >= 1) kmDistance * 1000 else null
            }
        }
    }
    
    /**
     * Check if we should announce this distance for this event
     */
    private fun shouldAnnounceEventDistance(state: EventAnnouncementState, announcementDistance: Int, currentDistance: Float): Boolean {
        // Don't announce if we've already announced this distance
        if (state.announcedDistances.contains(announcementDistance)) {
            return false
        }
        
        // Check cooldown
        val currentTime = System.currentTimeMillis()
        if (currentTime - state.lastAnnouncementTime < DISTANCE_ANNOUNCEMENT_COOLDOWN) {
            return false
        }
        
        // For km announcements, only announce when we cross the threshold
        if (announcementDistance >= 1000) {
            val kmThreshold = announcementDistance.toFloat()
            return currentDistance <= kmThreshold + 50f && currentDistance >= kmThreshold - 50f
        }
        
        // For meter announcements, announce when we're close to the threshold
        return currentDistance <= announcementDistance + 25f && currentDistance >= announcementDistance - 25f
    }
    
    /**
     * Announce distance to event
     */
    private fun announceEventDistance(event: EventRemoteReport, distanceMeters: Int) {
        val eventType = getEventTypeName(event.reportName)
        val distanceText = formatDistanceForAnnouncement(distanceMeters.toFloat())
        
        val message = "$eventType $distanceText away"
        textToSpeechManager.speak(message, android.speech.tts.TextToSpeech.QUEUE_ADD)
        
        Log.d(TAG, "Announced distance: $message")
    }
    
    /**
     * Announce arrival at event location
     */
    private fun announceEventArrival(event: EventRemoteReport) {
        val eventType = getEventTypeName(event.reportName)
        val message = "Approaching reported $eventType location"
        
        textToSpeechManager.speak(message, android.speech.tts.TextToSpeech.QUEUE_ADD)
        
        Log.d(TAG, "Announced arrival: $message")
    }
    
    /**
     * Extract event type name from report name
     */
    private fun getEventTypeName(reportName: String): String {
        // Extract the event type from the report name
        // Example: "Traffic Incident - Main St" -> "traffic incident"
        return reportName.split(" - ").firstOrNull()?.lowercase() ?: "event"
    }
    
    /**
     * Format distance for voice announcement
     */
    private fun formatDistanceForAnnouncement(distance: Float): String {
        return if (distance >= 1000) {
            val km = (distance / 1000).toInt()
            "${km} kilometer${if (km > 1) "s" else ""}"
        } else {
            "${distance.toInt()} meters"
        }
    }
    
    /**
     * Check if voice announcements are currently active
     */
    fun isActive(): Boolean = isActive
    
    /**
     * Get announcement statistics for debugging
     */
    fun getAnnouncementStatistics(): String {
        return "Active: $isActive | " +
               "Tracked events: ${announcedEvents.size} | " +
               "Discovered this session: $discoveryAnnouncementsThisSession | " +
               "Current events: ${currentEvents.sumOf { it.events.size }}"
    }
}
