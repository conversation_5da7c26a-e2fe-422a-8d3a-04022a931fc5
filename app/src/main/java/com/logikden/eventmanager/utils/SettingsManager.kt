package com.logikden.eventmanager.utils

import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.os.Build
import android.util.Log
import com.logikden.eventmanager.data.dto.EventSettings

/**
 * Singleton manager for app settings that provides centralized access and change notifications
 */
class SettingsManager private constructor(private val context: Context) {
    private val sharedPreferences: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    private val listeners = mutableListOf<OnSettingsChangedListener>()

    companion object {
        const val PREFS_NAME = "event_manager_prefs"

        // Setting keys
        const val KEY_UPDATE_RADIUS = "update_radius"
        const val KEY_MOVE_UPDATE = "move_update"
        const val KEY_CHECK_FREQUENCY = "check_frequency"
        const val KEY_EVENT_EXPIRY = "event_expiry"
        const val KEY_PROXIMITY_THRESHOLD = "proximity_threshold"
        const val KEY_NOTIFICATION_COOLDOWN = "notification_cooldown"
        const val KEY_REPORT_NOTIFICATION_PREFIX = "report_notification_"

        // New feature keys
        const val KEY_REALTIME_DISTANCE_UPDATES = "realtime_distance_updates"
        const val KEY_KEEP_SCREEN_ON = "keep_screen_on"
        const val KEY_VOICE_ANNOUNCEMENTS = "voice_announcements"
        const val KEY_VOICE_ANNOUNCEMENT_INTERVAL = "voice_announcement_interval"
        const val KEY_EVENT_DISCOVERY_ANNOUNCEMENTS = "event_discovery_announcements"
        const val KEY_ACTIVITY_RECOGNITION_ENABLED = "activity_recognition_enabled"
        const val KEY_SPEED_BASED_FREQUENCY_ENABLED = "speed_based_frequency_enabled"

        // Background sync keys
        const val KEY_BACKGROUND_SYNC_ENABLED = "background_sync_enabled"
        const val KEY_BACKGROUND_SYNC_INTERVAL = "background_sync_interval"
        const val KEY_WIFI_ONLY_SYNC = "wifi_only_sync"
        const val KEY_BATTERY_OPTIMIZATION_ENABLED = "battery_optimization_enabled"
        const val KEY_LAST_CACHED_LATITUDE = "last_cached_latitude"
        const val KEY_LAST_CACHED_LONGITUDE = "last_cached_longitude"
        const val KEY_LAST_LOCATION_CACHE_TIME = "last_location_cache_time"

        // Default values
        const val DEFAULT_RADIUS = 5 // 5 km
        const val DEFAULT_FREQUENCY = 1500 // 15 minutes
        const val DEFAULT_EXPIRY = 24 // 24 hours
        const val DEFAULT_DISTANCE = 5
        const val DEFAULT_PROXIMITY_THRESHOLD = 1.0f // 1.0 km
        const val DEFAULT_NOTIFICATION_COOLDOWN = 60000L // 1 minute in milliseconds
        const val MIN_NOTIFICATION_COOLDOWN = 30000L // 30 seconds
        const val MAX_NOTIFICATION_COOLDOWN = 300000L // 5 minutes

        // New feature default values
        const val DEFAULT_REALTIME_DISTANCE_UPDATES = true
        const val DEFAULT_KEEP_SCREEN_ON = false
        const val DEFAULT_VOICE_ANNOUNCEMENTS = true
        const val DEFAULT_VOICE_ANNOUNCEMENT_INTERVAL = 100 // 100 meters
        const val DEFAULT_EVENT_DISCOVERY_ANNOUNCEMENTS = true
        const val DEFAULT_ACTIVITY_RECOGNITION_ENABLED = true
        const val DEFAULT_SPEED_BASED_FREQUENCY_ENABLED = true

        // Background sync default values
        const val DEFAULT_BACKGROUND_SYNC_ENABLED = true
        const val DEFAULT_BACKGROUND_SYNC_INTERVAL = 30 // 30 minutes
        const val DEFAULT_WIFI_ONLY_SYNC = false
        const val DEFAULT_BATTERY_OPTIMIZATION_ENABLED = true

        // Broadcast actions
        const val ACTION_SETTINGS_CHANGED = "com.logikden.eventmanager.ACTION_SETTINGS_CHANGED"
        const val EXTRA_SETTING_KEY = "setting_key"

        @Volatile
        private var instance: SettingsManager? = null

        fun getInstance(context: Context): SettingsManager {
            return instance ?: synchronized(this) {
                instance ?: SettingsManager(context.applicationContext).also { instance = it }
            }
        }
    }

    interface OnSettingsChangedListener {
        fun onSettingChanged(key: String, value: Any?)
    }

    fun addListener(listener: OnSettingsChangedListener) {
        if (!listeners.contains(listener)) {
            listeners.add(listener)
        }
    }

    fun removeListener(listener: OnSettingsChangedListener) {
        listeners.remove(listener)
    }

    // Get all settings as a data class
    fun getEventSettings(): EventSettings {
        return EventSettings(
            updateRadius = getUpdateRadius(),
            checkFrequency = getCheckFrequency(),
            eventExpiry = getEventExpiry(),
            movedUpdate = getMoveUpdate(),
            realtimeDistanceUpdates = isRealtimeDistanceUpdatesEnabled(),
            keepScreenOn = isKeepScreenOnEnabled(),
            voiceAnnouncements = isVoiceAnnouncementsEnabled(),
            voiceAnnouncementInterval = getVoiceAnnouncementInterval(),
            eventDiscoveryAnnouncements = isEventDiscoveryAnnouncementsEnabled(),
            activityRecognitionEnabled = isActivityRecognitionEnabled()
        )
    }

    // Individual getters
    fun getUpdateRadius(): Int = sharedPreferences.getInt(KEY_UPDATE_RADIUS, DEFAULT_RADIUS)

    fun getMoveUpdate(): Int = sharedPreferences.getInt(KEY_MOVE_UPDATE, DEFAULT_DISTANCE)

    fun getCheckFrequency(): Int = sharedPreferences.getInt(KEY_CHECK_FREQUENCY, DEFAULT_FREQUENCY)

    fun getEventExpiry(): Int = sharedPreferences.getInt(KEY_EVENT_EXPIRY, DEFAULT_EXPIRY)

    fun getProximityThreshold(): Float {
        return sharedPreferences.getString(KEY_PROXIMITY_THRESHOLD, DEFAULT_PROXIMITY_THRESHOLD.toString())
            ?.toFloatOrNull() ?: DEFAULT_PROXIMITY_THRESHOLD
    }

    fun getNotificationCooldown(): Long = sharedPreferences.getLong(KEY_NOTIFICATION_COOLDOWN, DEFAULT_NOTIFICATION_COOLDOWN)

    // New feature getters
    fun isRealtimeDistanceUpdatesEnabled(): Boolean =
        sharedPreferences.getBoolean(KEY_REALTIME_DISTANCE_UPDATES, DEFAULT_REALTIME_DISTANCE_UPDATES)

    fun isKeepScreenOnEnabled(): Boolean =
        sharedPreferences.getBoolean(KEY_KEEP_SCREEN_ON, DEFAULT_KEEP_SCREEN_ON)

    fun isVoiceAnnouncementsEnabled(): Boolean =
        sharedPreferences.getBoolean(KEY_VOICE_ANNOUNCEMENTS, DEFAULT_VOICE_ANNOUNCEMENTS)

    fun getVoiceAnnouncementInterval(): Int =
        sharedPreferences.getInt(KEY_VOICE_ANNOUNCEMENT_INTERVAL, DEFAULT_VOICE_ANNOUNCEMENT_INTERVAL)

    fun isEventDiscoveryAnnouncementsEnabled(): Boolean =
        sharedPreferences.getBoolean(KEY_EVENT_DISCOVERY_ANNOUNCEMENTS, DEFAULT_EVENT_DISCOVERY_ANNOUNCEMENTS)

    fun isActivityRecognitionEnabled(): Boolean =
        sharedPreferences.getBoolean(KEY_ACTIVITY_RECOGNITION_ENABLED, DEFAULT_ACTIVITY_RECOGNITION_ENABLED)

    fun isSpeedBasedFrequencyEnabled(): Boolean =
        sharedPreferences.getBoolean(KEY_SPEED_BASED_FREQUENCY_ENABLED, DEFAULT_SPEED_BASED_FREQUENCY_ENABLED)

    // Background sync getters
    fun isBackgroundSyncEnabled(): Boolean =
        sharedPreferences.getBoolean(KEY_BACKGROUND_SYNC_ENABLED, DEFAULT_BACKGROUND_SYNC_ENABLED)

    fun getBackgroundSyncInterval(): Int =
        sharedPreferences.getInt(KEY_BACKGROUND_SYNC_INTERVAL, DEFAULT_BACKGROUND_SYNC_INTERVAL)

    fun isWifiOnlySyncEnabled(): Boolean =
        sharedPreferences.getBoolean(KEY_WIFI_ONLY_SYNC, DEFAULT_WIFI_ONLY_SYNC)

    fun isBatteryOptimizationEnabled(): Boolean =
        sharedPreferences.getBoolean(KEY_BATTERY_OPTIMIZATION_ENABLED, DEFAULT_BATTERY_OPTIMIZATION_ENABLED)

    // Location cache getters
    fun getLastCachedLatitude(): Double =
        sharedPreferences.getString(KEY_LAST_CACHED_LATITUDE, "0.0")?.toDoubleOrNull() ?: 0.0

    fun getLastCachedLongitude(): Double =
        sharedPreferences.getString(KEY_LAST_CACHED_LONGITUDE, "0.0")?.toDoubleOrNull() ?: 0.0

    fun getLastLocationCacheTime(): Long =
        sharedPreferences.getLong(KEY_LAST_LOCATION_CACHE_TIME, 0L)

    fun hasValidCachedLocation(): Boolean {
        val lat = getLastCachedLatitude()
        val lng = getLastCachedLongitude()
        val time = getLastLocationCacheTime()
        val currentTime = System.currentTimeMillis()
        val maxAge = 24 * 60 * 60 * 1000L // 24 hours

        return lat != 0.0 && lng != 0.0 && (currentTime - time) < maxAge
    }

    // Generic getter for boolean values
    fun getBoolean(key: String, defaultValue: Boolean): Boolean =
        sharedPreferences.getBoolean(key, defaultValue)

    // Setters with notification
    fun setUpdateRadius(value: Int) {
        sharedPreferences.edit().putInt(KEY_UPDATE_RADIUS, value).apply()
        notifySettingChanged(KEY_UPDATE_RADIUS, value)
    }

    fun setMoveUpdate(value: Int) {
        sharedPreferences.edit().putInt(KEY_MOVE_UPDATE, value).apply()
        notifySettingChanged(KEY_MOVE_UPDATE, value)
    }

    fun setCheckFrequency(value: Int) {
        sharedPreferences.edit().putInt(KEY_CHECK_FREQUENCY, value).apply()
        notifySettingChanged(KEY_CHECK_FREQUENCY, value)
    }

    fun setEventExpiry(value: Int) {
        sharedPreferences.edit().putInt(KEY_EVENT_EXPIRY, value).apply()
        notifySettingChanged(KEY_EVENT_EXPIRY, value)
    }

    fun setProximityThreshold(value: Float) {
        sharedPreferences.edit().putString(KEY_PROXIMITY_THRESHOLD, value.toString()).apply()
        notifySettingChanged(KEY_PROXIMITY_THRESHOLD, value)
    }

    fun setNotificationCooldown(value: Long) {
        sharedPreferences.edit().putLong(KEY_NOTIFICATION_COOLDOWN, value).apply()
        notifySettingChanged(KEY_NOTIFICATION_COOLDOWN, value)
    }

    // New feature setters
    fun setRealtimeDistanceUpdatesEnabled(enabled: Boolean) {
        sharedPreferences.edit().putBoolean(KEY_REALTIME_DISTANCE_UPDATES, enabled).apply()
        notifySettingChanged(KEY_REALTIME_DISTANCE_UPDATES, enabled)
    }

    fun setKeepScreenOnEnabled(enabled: Boolean) {
        sharedPreferences.edit().putBoolean(KEY_KEEP_SCREEN_ON, enabled).apply()
        notifySettingChanged(KEY_KEEP_SCREEN_ON, enabled)
    }

    fun setVoiceAnnouncementsEnabled(enabled: Boolean) {
        sharedPreferences.edit().putBoolean(KEY_VOICE_ANNOUNCEMENTS, enabled).apply()
        notifySettingChanged(KEY_VOICE_ANNOUNCEMENTS, enabled)
    }

    fun setVoiceAnnouncementInterval(value: Int) {
        sharedPreferences.edit().putInt(KEY_VOICE_ANNOUNCEMENT_INTERVAL, value).apply()
        notifySettingChanged(KEY_VOICE_ANNOUNCEMENT_INTERVAL, value)
    }

    fun setEventDiscoveryAnnouncementsEnabled(enabled: Boolean) {
        sharedPreferences.edit().putBoolean(KEY_EVENT_DISCOVERY_ANNOUNCEMENTS, enabled).apply()
        notifySettingChanged(KEY_EVENT_DISCOVERY_ANNOUNCEMENTS, enabled)
    }

    fun setActivityRecognitionEnabled(enabled: Boolean) {
        sharedPreferences.edit().putBoolean(KEY_ACTIVITY_RECOGNITION_ENABLED, enabled).apply()
        notifySettingChanged(KEY_ACTIVITY_RECOGNITION_ENABLED, enabled)
    }

    fun setSpeedBasedFrequencyEnabled(enabled: Boolean) {
        sharedPreferences.edit().putBoolean(KEY_SPEED_BASED_FREQUENCY_ENABLED, enabled).apply()
        notifySettingChanged(KEY_SPEED_BASED_FREQUENCY_ENABLED, enabled)
    }

    // Background sync setters
    fun setBackgroundSyncEnabled(enabled: Boolean) {
        sharedPreferences.edit().putBoolean(KEY_BACKGROUND_SYNC_ENABLED, enabled).apply()
        notifySettingChanged(KEY_BACKGROUND_SYNC_ENABLED, enabled)
    }

    fun setBackgroundSyncInterval(interval: Int) {
        sharedPreferences.edit().putInt(KEY_BACKGROUND_SYNC_INTERVAL, interval).apply()
        notifySettingChanged(KEY_BACKGROUND_SYNC_INTERVAL, interval)
    }

    fun setWifiOnlySyncEnabled(enabled: Boolean) {
        sharedPreferences.edit().putBoolean(KEY_WIFI_ONLY_SYNC, enabled).apply()
        notifySettingChanged(KEY_WIFI_ONLY_SYNC, enabled)
    }

    fun setBatteryOptimizationEnabled(enabled: Boolean) {
        sharedPreferences.edit().putBoolean(KEY_BATTERY_OPTIMIZATION_ENABLED, enabled).apply()
        notifySettingChanged(KEY_BATTERY_OPTIMIZATION_ENABLED, enabled)
    }

    // Location cache setters
    fun cacheLocation(latitude: Double, longitude: Double) {
        val currentTime = System.currentTimeMillis()
        sharedPreferences.edit()
            .putString(KEY_LAST_CACHED_LATITUDE, latitude.toString())
            .putString(KEY_LAST_CACHED_LONGITUDE, longitude.toString())
            .putLong(KEY_LAST_LOCATION_CACHE_TIME, currentTime)
            .apply()
        Log.d("SettingsManager", "Cached location: $latitude, $longitude at $currentTime")
    }

    fun clearCachedLocation() {
        sharedPreferences.edit()
            .remove(KEY_LAST_CACHED_LATITUDE)
            .remove(KEY_LAST_CACHED_LONGITUDE)
            .remove(KEY_LAST_LOCATION_CACHE_TIME)
            .apply()
        Log.d("SettingsManager", "Cleared cached location")
    }

    // Generic setter for boolean values
    fun setBoolean(key: String, value: Boolean) {
        sharedPreferences.edit().putBoolean(key, value).apply()
        notifySettingChanged(key, value)
    }

    // Report type notification preferences
    fun isReportTypeNotificationEnabled(reportTypeId: Int): Boolean {
        return sharedPreferences.getBoolean(KEY_REPORT_NOTIFICATION_PREFIX + reportTypeId, true)
    }

    fun setReportTypeNotificationEnabled(reportTypeId: Int, enabled: Boolean) {
        sharedPreferences.edit().putBoolean(KEY_REPORT_NOTIFICATION_PREFIX + reportTypeId, enabled).apply()
        notifySettingChanged(KEY_REPORT_NOTIFICATION_PREFIX + reportTypeId, enabled)
    }

    private fun notifySettingChanged(key: String, value: Any?) {
        // Notify local listeners
        listeners.forEach { it.onSettingChanged(key, value) }

        // Broadcast to services and other components
        val intent = Intent(ACTION_SETTINGS_CHANGED).apply {
            putExtra(EXTRA_SETTING_KEY, key)
        }

        try {
            // For Android 14 (API 34) and above
            if (Build.VERSION.SDK_INT >= 34) { // UPSIDE_DOWN_CAKE
                // Use the new method that takes a receiver flag
                intent.addFlags(Intent.FLAG_RECEIVER_FOREGROUND)
                context.sendBroadcast(intent)
                Log.d("SettingsManager", "Sent broadcast with Android 14+ flags")
            }
            // For Android 13 (API 33)
            else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                // Use the new method that takes a receiver flag
                intent.addFlags(Intent.FLAG_RECEIVER_FOREGROUND)
                context.sendBroadcast(intent)
                Log.d("SettingsManager", "Sent broadcast with Android 13 flags")
            } else {
                // For older Android versions
                context.sendBroadcast(intent)
                Log.d("SettingsManager", "Sent broadcast without flags")
            }
        } catch (e: Exception) {
            Log.e("SettingsManager", "Error sending broadcast: ${e.message}", e)
        }
    }
}
