package com.logikden.eventmanager.utils

import kotlinx.coroutines.CancellationException

/**
 * Utility class for converting technical exception messages to user-friendly error messages
 * This prevents technical details like "job was cancelled" from being shown to users
 */
object ErrorMessageUtils {

    /**
     * Converts technical exception messages to user-friendly error messages
     * @param exception The exception to convert
     * @return A user-friendly error message
     */
    fun getUserFriendlyErrorMessage(exception: Exception): String {
        return when {
            exception is CancellationException -> {
                // Don't show cancellation exceptions to users - they're internal
                "Operation was interrupted"
            }
            exception.message?.contains("job was cancelled", ignoreCase = true) == true -> {
                "Operation was interrupted"
            }
            exception.message?.contains("coroutine", ignoreCase = true) == true -> {
                "Operation was interrupted"
            }
            exception.message?.contains("network", ignoreCase = true) == true ||
            exception.message?.contains("connection", ignoreCase = true) == true ||
            exception.message?.contains("timeout", ignoreCase = true) == true ||
            exception.message?.contains("internet", ignoreCase = true) == true -> {
                "Network connection error. Please check your internet connection."
            }
            exception.message?.contains("permission", ignoreCase = true) == true -> {
                "Permission required to access this feature"
            }
            exception.message?.contains("location", ignoreCase = true) == true -> {
                "Unable to access location services"
            }
            exception.message?.contains("database", ignoreCase = true) == true ||
            exception.message?.contains("sql", ignoreCase = true) == true -> {
                "Database error. Please try again."
            }
            exception.message?.contains("navigation", ignoreCase = true) == true ||
            exception.message?.contains("navcontroller", ignoreCase = true) == true -> {
                "Navigation error. Please try again."
            }
            exception.message?.contains("subscription", ignoreCase = true) == true -> {
                "Unable to update subscription. Please try again."
            }
            exception.message?.contains("sync", ignoreCase = true) == true -> {
                "Sync error. Please try again."
            }
            else -> {
                // For any other technical errors, show a generic message
                "Something went wrong. Please try again."
            }
        }
    }

    /**
     * Checks if an exception should be shown to the user
     * Some exceptions like CancellationException are internal and shouldn't be displayed
     * @param exception The exception to check
     * @return true if the exception should be shown to the user, false otherwise
     */
    fun shouldShowToUser(exception: Exception): Boolean {
        return when {
            exception is CancellationException -> false
            exception.message?.contains("job was cancelled", ignoreCase = true) == true -> false
            exception.message?.contains("coroutine", ignoreCase = true) == true -> false
            exception.message?.contains("navcontroller", ignoreCase = true) == true -> false
            else -> true
        }
    }

    /**
     * Gets a user-friendly error message only if the exception should be shown to the user
     * @param exception The exception to process
     * @return A user-friendly error message if it should be shown, null otherwise
     */
    fun getUserFriendlyErrorMessageIfShouldShow(exception: Exception): String? {
        return if (shouldShowToUser(exception)) {
            getUserFriendlyErrorMessage(exception)
        } else {
            null
        }
    }
}
