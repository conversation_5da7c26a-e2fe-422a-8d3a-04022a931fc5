package com.logikden.eventmanager.utils

import android.content.Context
import android.util.Log
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey

/**
 * Utility class for managing subscriber key operations
 * Provides secure storage and retrieval of subscriber ID
 */
object SubscriberKeyManager {
    private const val TAG = "SubscriberKeyManager"
    private const val PREFS_NAME = "secure_prefs"
    private const val KEY_SUBSCRIBER_ID = "subscriber_id"

    /**
     * Get subscriber ID from secure storage
     * @param context Application context
     * @return Subscriber ID if available, null otherwise
     */
    fun getSubscriberId(context: Context): String? {
        return try {
            val masterKey = MasterKey.Builder(context)
                .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
                .build()

            val sharedPreferences = EncryptedSharedPreferences.create(
                context,
                PREFS_NAME,
                masterKey,
                EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
                EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
            )

            val subscriberId = sharedPreferences.getString(KEY_SUBSCRIBER_ID, null)
            Log.d(TAG, "Retrieved subscriber ID: ${if (subscriberId != null) "***" else "null"}")
            subscriberId
        } catch (e: Exception) {
            Log.e(TAG, "Error retrieving subscriber ID: ${e.message}")
            null
        }
    }

    /**
     * Save subscriber ID to secure storage
     * @param context Application context
     * @param subscriberId Subscriber ID to save
     */
    fun saveSubscriberId(context: Context, subscriberId: String) {
        try {
            val masterKey = MasterKey.Builder(context)
                .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
                .build()

            val sharedPreferences = EncryptedSharedPreferences.create(
                context,
                PREFS_NAME,
                masterKey,
                EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
                EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
            )

            sharedPreferences.edit().putString(KEY_SUBSCRIBER_ID, subscriberId).apply()
            Log.d(TAG, "Saved subscriber ID successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error saving subscriber ID: ${e.message}")
        }
    }

    /**
     * Clear subscriber ID from secure storage
     * @param context Application context
     */
    fun clearSubscriberId(context: Context) {
        try {
            val masterKey = MasterKey.Builder(context)
                .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
                .build()

            val sharedPreferences = EncryptedSharedPreferences.create(
                context,
                PREFS_NAME,
                masterKey,
                EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
                EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
            )

            sharedPreferences.edit().remove(KEY_SUBSCRIBER_ID).apply()
            Log.d(TAG, "Cleared subscriber ID successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error clearing subscriber ID: ${e.message}")
        }
    }

    /**
     * Check if subscriber ID exists
     * @param context Application context
     * @return true if subscriber ID exists, false otherwise
     */
    fun hasSubscriberId(context: Context): Boolean {
        return getSubscriberId(context) != null
    }
}
