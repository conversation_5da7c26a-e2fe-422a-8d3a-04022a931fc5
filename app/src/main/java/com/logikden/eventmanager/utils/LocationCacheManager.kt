package com.logikden.eventmanager.utils

import android.content.Context
import android.location.Location
import android.util.Log

/**
 * Manager for caching location data for background sync operations
 */
class LocationCacheManager private constructor(private val context: Context) {
    
    private val settingsManager = SettingsManager.getInstance(context)
    
    companion object {
        private const val TAG = "LocationCacheManager"
        private const val MIN_CACHE_INTERVAL = 5 * 60 * 1000L // 5 minutes
        private const val MIN_DISTANCE_CHANGE = 100.0 // 100 meters
        
        @Volatile
        private var instance: LocationCacheManager? = null
        
        fun getInstance(context: Context): LocationCacheManager {
            return instance ?: synchronized(this) {
                instance ?: LocationCacheManager(context.applicationContext).also { instance = it }
            }
        }
    }
    
    /**
     * Cache location if it meets the criteria for caching
     * @param location The location to cache
     * @return true if location was cached, false otherwise
     */
    fun cacheLocationIfNeeded(location: Location): Boolean {
        return try {
            // Check if we should cache this location
            if (shouldCacheLocation(location)) {
                settingsManager.cacheLocation(location.latitude, location.longitude)
                Log.d(TAG, "Cached location: ${location.latitude}, ${location.longitude}")
                true
            } else {
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error caching location", e)
            false
        }
    }
    
    /**
     * Force cache the current location regardless of criteria
     * @param location The location to cache
     */
    fun forceCacheLocation(location: Location) {
        try {
            settingsManager.cacheLocation(location.latitude, location.longitude)
            Log.d(TAG, "Force cached location: ${location.latitude}, ${location.longitude}")
        } catch (e: Exception) {
            Log.e(TAG, "Error force caching location", e)
        }
    }
    
    /**
     * Get the cached location if it's valid
     * @return Cached location or null if not valid
     */
    fun getCachedLocation(): Location? {
        return try {
            if (settingsManager.hasValidCachedLocation()) {
                val lat = settingsManager.getLastCachedLatitude()
                val lng = settingsManager.getLastCachedLongitude()
                var time = settingsManager.getLastLocationCacheTime()
                
                Location("cached").apply {
                    latitude = lat
                    longitude = lng
                    time = time
                }
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting cached location", e)
            null
        }
    }
    
    /**
     * Clear the cached location
     */
    fun clearCache() {
        try {
            settingsManager.clearCachedLocation()
            Log.d(TAG, "Cleared location cache")
        } catch (e: Exception) {
            Log.e(TAG, "Error clearing location cache", e)
        }
    }
    
    /**
     * Check if location should be cached based on time and distance criteria
     */
    private fun shouldCacheLocation(newLocation: Location): Boolean {
        val currentTime = System.currentTimeMillis()
        val lastCacheTime = settingsManager.getLastLocationCacheTime()
        
        // Always cache if no previous cache exists
        if (lastCacheTime == 0L) {
            Log.d(TAG, "No previous cache, caching location")
            return true
        }
        
        // Check time interval
        val timeSinceLastCache = currentTime - lastCacheTime
        if (timeSinceLastCache < MIN_CACHE_INTERVAL) {
            Log.d(TAG, "Too soon to cache again (${timeSinceLastCache}ms < ${MIN_CACHE_INTERVAL}ms)")
            return false
        }
        
        // Check distance change
        val lastLat = settingsManager.getLastCachedLatitude()
        val lastLng = settingsManager.getLastCachedLongitude()
        
        if (lastLat != 0.0 && lastLng != 0.0) {
            val distance = LocationUtils.calculateDistance(
                lastLat, lastLng,
                newLocation.latitude, newLocation.longitude
            ) * 1000 // Convert to meters
            
            if (distance < MIN_DISTANCE_CHANGE) {
                Log.d(TAG, "Distance change too small (${distance}m < ${MIN_DISTANCE_CHANGE}m)")
                return false
            }
        }
        
        Log.d(TAG, "Location meets caching criteria")
        return true
    }
    
    /**
     * Get cache statistics for debugging
     */
    fun getCacheInfo(): CacheInfo {
        return CacheInfo(
            hasValidCache = settingsManager.hasValidCachedLocation(),
            latitude = settingsManager.getLastCachedLatitude(),
            longitude = settingsManager.getLastCachedLongitude(),
            cacheTime = settingsManager.getLastLocationCacheTime(),
            ageInMinutes = if (settingsManager.getLastLocationCacheTime() > 0) {
                (System.currentTimeMillis() - settingsManager.getLastLocationCacheTime()) / (60 * 1000)
            } else 0
        )
    }
    
    /**
     * Data class for cache information
     */
    data class CacheInfo(
        val hasValidCache: Boolean,
        val latitude: Double,
        val longitude: Double,
        val cacheTime: Long,
        val ageInMinutes: Long
    )
}
