package com.logikden.eventmanager.helper
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.TimeZone
import java.util.concurrent.TimeUnit
import android.util.Log

class DateHelper {
    companion object {
        private const val ISO_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss"
        private const val ISO_DATE_FORMAT_WITH_ZONE = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
        private const val SIMPLE_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss"
        private const val DISPLAY_DATE_FORMAT = "MMM dd, yyyy HH:mm"

        /**
         * Get a human-readable string representing time passed since the given time
         * Assumes the input time is already in local time zone
         */
        fun getTimePassed(reportTime: String): String {
            try {
                val dateFormat = SimpleDateFormat(ISO_DATE_FORMAT, Locale.getDefault())
                val eventDate = dateFormat.parse(reportTime) ?: return "N/A" // Parse reportTime
                val currentDate = Date() // Current time
                val diffInMillis = currentDate.time - eventDate.time // Difference in milliseconds

                // Convert milliseconds to hours and minutes
                val hours = TimeUnit.MILLISECONDS.toHours(diffInMillis)
                val minutes = TimeUnit.MILLISECONDS.toMinutes(diffInMillis) % 60

                return when {
                    hours > 24 -> "${hours / 24} day${if (hours / 24 > 1) "s" else ""} ago"
                    hours > 0 -> "$hours hour${if (hours > 1) "s" else ""} ago"
                    minutes > 0 -> "$minutes minute${if (minutes > 1) "s" else ""} ago"
                    else -> "Just now"
                }
            } catch (e: Exception) {
                Log.e("DateHelper", "Error in getTimePassed: ${e.message}")
                return "N/A"
            }
        }

        /**
         * Calculate hours passed since the given time
         * Assumes the input time is already in local time zone
         */
        fun getTimePassedInHours(reportTime: String): Int {
            try {
                val dateFormat = SimpleDateFormat(ISO_DATE_FORMAT, Locale.getDefault())
                val eventDate = dateFormat.parse(reportTime) ?: return 0 // Parse reportTime
                val currentDate = Date() // Current time
                val diffInMillis = currentDate.time - eventDate.time // Difference in milliseconds
                // Convert milliseconds to hours
                return TimeUnit.MILLISECONDS.toHours(diffInMillis).toInt()
            } catch (e: Exception) {
                Log.e("DateHelper", "Error in getTimePassedInHours: ${e.message}")
                return 0
            }
        }

        fun getCurrentUtcTime(): String {
            val dateFormat = SimpleDateFormat(SIMPLE_DATE_FORMAT, Locale.getDefault())
            dateFormat.timeZone = TimeZone.getTimeZone("UTC")
            return dateFormat.format(Date())
        }

        /**
         * Get a human-readable string representing time passed since the given UTC time
         * Converts the UTC time to local time before calculating time passed
         */
        fun getTimePassedFromUtc(utcTimeString: String?): String {
            if (utcTimeString == null) return "Unknown time"

            return try {
                // First convert UTC time to local time
                val localTimeString = convertUtcToLocalTimeString(utcTimeString)
                Log.d("DateHelper", "UTC time: $utcTimeString, converted to local: $localTimeString")

                // Then calculate time passed
                getTimePassed(localTimeString)
            } catch (e: Exception) {
                Log.e("DateHelper", "Error parsing UTC time: $utcTimeString", e)
                "Unknown time"
            }
        }

        // Convert local timestamp to UTC timestamp
        fun localToUtcTimestamp(localTimestamp: Long): Long {
            val localTimeZone = TimeZone.getDefault()
            return localTimestamp - localTimeZone.getOffset(localTimestamp)
        }

        // Convert UTC timestamp to local timestamp
        fun utcToLocalTimestamp(utcTimestamp: Long): Long {
            val localTimeZone = TimeZone.getDefault()
            return utcTimestamp + localTimeZone.getOffset(utcTimestamp)
        }

        // Convert local timestamp to UTC ISO string
        fun localTimestampToUtcIsoString(localTimestamp: Long): String {
            val utcTimestamp = localToUtcTimestamp(localTimestamp)
            val dateFormat = SimpleDateFormat(ISO_DATE_FORMAT, Locale.getDefault())
            dateFormat.timeZone = TimeZone.getTimeZone("UTC")
            return dateFormat.format(Date(utcTimestamp))
        }

        // Convert UTC ISO string to local timestamp
        fun utcIsoStringToLocalTimestamp(utcIsoString: String): Long {
            try {
                val dateFormat = SimpleDateFormat(ISO_DATE_FORMAT, Locale.getDefault())
                dateFormat.timeZone = TimeZone.getTimeZone("UTC")
                val utcDate = dateFormat.parse(utcIsoString) ?: return 0
                return utcToLocalTimestamp(utcDate.time)
            } catch (e: Exception) {
                Log.e("DateHelper", "Error converting UTC ISO string to local timestamp: $utcIsoString", e)
                return System.currentTimeMillis() // Return current time as fallback
            }
        }

        /**
         * Convert UTC ISO string to local time ISO string
         */
        fun convertUtcToLocalTimeString(utcIsoString: String): String {
            try {
                val dateFormat = SimpleDateFormat(ISO_DATE_FORMAT, Locale.getDefault())
                dateFormat.timeZone = TimeZone.getTimeZone("UTC")
                val utcDate = dateFormat.parse(utcIsoString) ?: return utcIsoString

                // Convert to local time
                val localTimestamp = utcToLocalTimestamp(utcDate.time)

                // Format as ISO string in local time zone
                val localDateFormat = SimpleDateFormat(ISO_DATE_FORMAT, Locale.getDefault())
                return localDateFormat.format(Date(localTimestamp))
            } catch (e: Exception) {
                Log.e("DateHelper", "Error converting UTC to local time string: $utcIsoString", e)
                return utcIsoString // Return original string as fallback
            }
        }

        /**
         * Format a UTC ISO string as a human-readable date in local time zone
         */
        fun formatUtcTimeForDisplay(utcIsoString: String): String {
            try {
                // Parse the UTC time
                val dateFormat = SimpleDateFormat(ISO_DATE_FORMAT, Locale.getDefault())
                dateFormat.timeZone = TimeZone.getTimeZone("UTC")
                val utcDate = dateFormat.parse(utcIsoString) ?: return "Unknown date"

                // Convert to local time
                val localTimestamp = utcToLocalTimestamp(utcDate.time)

                // Format for display
                val displayFormat = SimpleDateFormat(DISPLAY_DATE_FORMAT, Locale.getDefault())
                return displayFormat.format(Date(localTimestamp))
            } catch (e: Exception) {
                Log.e("DateHelper", "Error formatting UTC time for display: $utcIsoString", e)
                return "Unknown date"
            }
        }
    }
}



