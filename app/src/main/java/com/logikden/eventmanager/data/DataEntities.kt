package com.logikden.eventmanager.data
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverter
import com.google.gson.Gson
import com.logikden.eventmanager.data.dto.EventReportRequest

@Entity(tableName = "tiles")
data class Tile(
    @PrimaryKey(autoGenerate = true) val id: Int = 0,
    val title: String,
    val iconName: String,
    var order: Int,
    var isActive: Boolean = true,
    var favourite: Boolean = true,
    var notificationsEnabled: Boolean = true
)

@Entity(tableName = "local_event_reports")
data class EventLocalReport(
    @PrimaryKey(autoGenerate = true) var localId: Long = 0,
    val reportTypeId: Int,
    val latitude: Double,
    val longitude: Double,
    val description: String,
    val extra: String,
    val owner: String? = null,
    val isReached: Boolean = false,
    val createdAt: Long = System.currentTimeMillis(),
    val isSynced: Boolean = false,
    val dateTime: Long
)

@Entity(tableName = "remote_event_reports")
data class EventRemoteReport(
    @PrimaryKey(autoGenerate = false)
    val reportId: Int,
    val reportTypeId: Int,
    val latitude: Double,
    val longitude: Double,
    val reportTime: String?,
    val reportName: String,
    val isReached: Boolean = false,
    val mergedReportsCount: Int = 1
)

@Entity(tableName = "locations")
data class LocationEntity(
    @PrimaryKey(autoGenerate = true) val id: Long = 0,
    val name: String,
    val latitude: Double,
    val longitude: Double,
    val reportType: String,
    val timestamp: Long = System.currentTimeMillis()
)

class Converters {
    @TypeConverter
    fun fromEventReportRequest(value: EventReportRequest): String {
        return Gson().toJson(value)
    }

    @TypeConverter
    fun toEventReportRequest(value: String): EventReportRequest {
        return Gson().fromJson(value, EventReportRequest::class.java)
    }
}