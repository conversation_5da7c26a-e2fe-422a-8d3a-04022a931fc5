package com.logikden.eventmanager.data

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import java.io.File

@Database(entities = [Tile::class,EventLocalReport::class,LocationEntity::class,EventRemoteReport::class], version = 6)
@TypeConverters(Converters::class)
abstract class AppDatabase : RoomDatabase() {
    abstract fun tileDao(): TileDao
    abstract fun pendingEventReportDao(): EventReportDao
    abstract fun locationDao(): LocationDao
    abstract fun remoteEventReportDao(): EventRemoteReportDao

    companion object {
        @Volatile
        private var INSTANCE: AppDatabase? = null

        // Migration from version 4 to 5: Add notificationsEnabled column to tiles table
        private val MIGRATION_4_5 = object : Migration(4, 5) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // Add the notificationsEnabled column with a default value of 1 (true)
                database.execSQL("ALTER TABLE tiles ADD COLUMN notificationsEnabled INTEGER NOT NULL DEFAULT 1")
            }
        }

        // Migration from version 5 to 6: Add mergedReportsCount column to remote_event_reports table
        private val MIGRATION_5_6 = object : Migration(5, 6) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // Add the mergedReportsCount column with a default value of 1
                database.execSQL("ALTER TABLE remote_event_reports ADD COLUMN mergedReportsCount INTEGER NOT NULL DEFAULT 1")
            }
        }

        fun getDatabase(context: Context, scope: CoroutineScope): AppDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    AppDatabase::class.java,
                    "event_manager"
                )
                    .addCallback(AppDatabaseCallback(scope))
                    .addMigrations(MIGRATION_4_5, MIGRATION_5_6) // Add the migrations
                    .build()
                INSTANCE = instance
                instance
            }
        }
    }

    fun isDatabaseCreated(context: Context): Boolean {
        return context.getDatabasePath("event_manager_db").exists()
    }

    fun clearRoomDatabase(context: Context) {
        val dbPath = context.getDatabasePath("event_manager_db").path
        if (File(dbPath).delete()) {
            INSTANCE = null
        }
    }
}

class AppDatabaseCallback(private val scope: CoroutineScope) : RoomDatabase.Callback() {
    override fun onCreate(db: SupportSQLiteDatabase) {
        super.onCreate(db)
        scope.launch {
            // Example: Perform database population or background operations
        }
    }
}
