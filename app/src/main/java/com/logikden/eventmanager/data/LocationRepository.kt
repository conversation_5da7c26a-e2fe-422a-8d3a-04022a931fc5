package com.logikden.eventmanager.data

import androidx.lifecycle.LiveData
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class LocationRepository(private val locationDao: LocationDao) {

    val allLocations: LiveData<List<LocationEntity>> = locationDao.getAllLocations()

    fun insertLocation(location: LocationEntity) {
        CoroutineScope(Dispatchers.IO).launch {
            locationDao.insertLocation(location)
        }
    }
}