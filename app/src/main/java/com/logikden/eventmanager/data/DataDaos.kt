package com.logikden.eventmanager.data

import androidx.lifecycle.LiveData
import androidx.room.Dao
import androidx.room.Query
import androidx.room.Insert
import androidx.room.Update
import androidx.room.Delete
import androidx.room.OnConflictStrategy
import kotlinx.coroutines.flow.Flow

@Dao
interface TileDao {
    @Query("SELECT * FROM tiles ORDER BY `order` ASC")
    fun getAll(): List<Tile>

    @Query("SELECT * FROM tiles WHERE isActive = 1 ORDER BY `order` ASC")
    fun getAllActive(): List<Tile>

    @Query("SELECT * FROM tiles WHERE isActive = 1 ORDER BY `order` ASC LIMIT 6")
    fun get6Active(): List<Tile>

    @Query("SELECT COUNT(id) FROM tiles WHERE isActive = 1")
    fun getCount(): Int

    @Query("SELECT * FROM tiles WHERE id =:id")
    fun getById(id:Int): Tile

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(tile: Tile)

    @Update
    suspend fun update(tile: Tile)

    @Update
    suspend fun updateAll(tiles: List<Tile>)

    @Delete
    suspend fun delete(tile: Tile)

    @Query("DELETE FROM tiles")
    suspend fun deleteAll()

    @Insert
    fun insertAll(tiles: List<Tile>)
}

@Dao
interface EventReportDao {
    @Query("SELECT * FROM local_event_reports")
    suspend fun getAllEventReports(): List<EventLocalReport>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAll(reports: List<EventLocalReport>)

    @Query("""
    SELECT * FROM local_event_reports
    WHERE dateTime BETWEEN :newEventTime - 7200000 AND :newEventTime  AND description=:eventType
""")
    fun findTheEventHappenedRecent(newEventTime: Long,eventType:String): List<EventLocalReport>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(event: EventLocalReport):Long

    @Query("SELECT * FROM local_event_reports WHERE localId = :id")
    fun getById(id: Long): EventLocalReport?

    @Query("SELECT * FROM local_event_reports ORDER BY dateTime ASC")
    suspend fun getAllEvents(): List<EventLocalReport>

    @Query("SELECT * FROM local_event_reports WHERE isSynced = 0 ORDER BY dateTime ASC")
    suspend fun getUnsyncedReports(): List<EventLocalReport>

    @Query("UPDATE local_event_reports SET isSynced = 1 WHERE localId = :localId")
    suspend fun markAsSynced(localId: Long)

    @Query("SELECT COUNT(*) FROM local_event_reports WHERE isSynced = 0")
    fun getUnsyncedCount(): Flow<Int>

    @Query("SELECT * FROM local_event_reports")
    fun getAllReports(): List<EventLocalReport>

    @Query("SELECT * FROM local_event_reports WHERE reportTypeId = :reportTypeId AND dateTime >= :minTime")
    fun getRecentReportsByType(reportTypeId: Int, minTime: Long): List<EventLocalReport>

    @Query("DELETE FROM local_event_reports WHERE localId=:id")
    suspend fun deleteById(id:Int)

    @Query("DELETE FROM local_event_reports")
    suspend fun deleteAll()
}

@Dao
interface EventRemoteReportDao {
    // Insert a new report
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertReport(report: EventRemoteReport)

    // Insert multiple reports
    @Insert(onConflict = OnConflictStrategy.IGNORE)
    suspend fun insertReports(reports: List<EventRemoteReport>)

    // Delete all reports
    @Query("DELETE FROM remote_event_reports WHERE reportId = :localId")
    suspend fun deleteReport(localId: Long)

    @Query("DELETE FROM remote_event_reports")
    suspend fun deleteAllReports()


    // Get all reports
    @Query("SELECT * FROM remote_event_reports")
    fun getAllReports(): Flow<List<EventRemoteReport>>

    @Query("SELECT * FROM remote_event_reports WHERE  reportName= :name")
    suspend fun findByName(name: String): EventRemoteReport?

    // Get a report by its local ID
    @Query("SELECT * FROM remote_event_reports WHERE reportId = :localId")
    suspend fun getReportById(localId: Long): EventRemoteReport?

    // Get reports by report type ID
    @Query("SELECT * FROM remote_event_reports WHERE reportTypeId = :reportTypeId")
    fun getReportsByType(reportTypeId: Int): Flow<List<EventRemoteReport>>

    // Get reports that have not been reached
    @Query("SELECT * FROM remote_event_reports WHERE isReached = 0")
    fun getUnreachedReports(): Flow<List<EventRemoteReport>>

    // Mark a report as reached by its local ID
    @Query("UPDATE remote_event_reports SET isReached = 1 WHERE reportId = :localId")
    suspend fun markReportAsReached(localId: Long)
}


@Dao
interface LocationDao {
    @Insert
    suspend fun insertLocation(location: LocationEntity)

    @Query("SELECT * FROM locations  WHERE reportType!='Road Point' AND reportType!='Auto Collect' ORDER BY timestamp DESC")
    fun getAllLocations(): LiveData<List<LocationEntity>>
}

