package com.logikden.eventmanager.data.dto
import android.util.Log
import com.google.gson.annotations.SerializedName
import com.logikden.eventmanager.data.EventRemoteReport
import com.logikden.eventmanager.helper.DateHelper


data class ReportType(
    @SerializedName("id") val id: Int,
    @SerializedName("reportDescription") val reportDescription: String,
    @SerializedName("dateCreated") val dateCreated: String,
    @SerializedName("reportStatus") val reportStatus: Int,
    @SerializedName("statusChangeDate") val statusChangeDate: String,
    @SerializedName("owner") val owner: String,
    @SerializedName("custom") val custom: Boolean,
    @SerializedName("isSubscribed") val isSubscribed: Boolean,
    @SerializedName("typeOrder") val typeOrder: Int,
    @SerializedName("reportName") val reportName: String,
    @SerializedName("timeStamp") val timeStamp: String
)

data class SubscriptionRequest(
    @SerializedName("subscriptions")
    val subscriptions: List<SubscriptionItem>
)

data class SubscriptionItem(
    @SerializedName("ReportTypeId") val reportTypeId: Int
)

data class SubscriptionResponse(
    val success: Boolean
)

data class SubscriberResponse(
    @SerializedName("subscriberId") val subscriberId: String
)

data class EventReportRequest(
    @SerializedName("reportTypeId") val reportTypeId: Int,
    @SerializedName("latitude") val latitude: Double,
    @SerializedName("longitude") val longitude: Double,
    @SerializedName("description") val description: String,
    @SerializedName("subscriberId") val subscriberId: Int,
    @SerializedName("extra") val extra: String
)

data class GpsLocation(
    val latitude: Double,
    val longitude: Double
)

data class EventReportResponse(
    val gpsLocation: GpsLocation,
    val reportTime: String?,
    val reportTypeId: Int,
    val reportName:String,
    val reportId: Int
)

fun EventReportResponse.toEntity(): EventRemoteReport {
    return EventRemoteReport(
        reportTypeId = this.reportTypeId,
        latitude = this.gpsLocation.latitude,
        longitude = this.gpsLocation.longitude,
        reportTime = if (this.reportTime != null) {
            // Convert UTC time to local time and store as ISO string
            try {
                val localTimeString = DateHelper.convertUtcToLocalTimeString(this.reportTime)
                Log.d("Dtos", "Converting UTC time ${this.reportTime} to local time: $localTimeString")
                localTimeString
            } catch (e: Exception) {
                Log.e("Dtos", "Error converting UTC time to local: ${e.message}")
                this.reportTime // Fallback to original UTC time if conversion fails
            }
        } else null,
        reportName = this.reportName,
        reportId = this.reportId,
        mergedReportsCount = 1 // Default to 1 for new reports
    )
}

data class EventSettings(
    val updateRadius: Int,
    val checkFrequency: Int,
    val eventExpiry: Int,
    val movedUpdate: Int,
    val realtimeDistanceUpdates: Boolean = true,
    val keepScreenOn: Boolean = false,
    val voiceAnnouncements: Boolean = false,
    val voiceAnnouncementInterval: Int = 100,
    val eventDiscoveryAnnouncements: Boolean = true,
    val activityRecognitionEnabled: Boolean = true
)

data class GroupedEvent(
    val eventType: String,
    val events: List<EventRemoteReport>,
    var isExpanded: Boolean = false
)

