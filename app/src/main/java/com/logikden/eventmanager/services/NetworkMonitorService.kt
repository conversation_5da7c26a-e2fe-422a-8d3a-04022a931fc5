package com.logikden.eventmanager.services

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import android.os.Build
import androidx.annotation.RequiresApi
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

// Define an interface for syncable repositories
interface ISyncableRepository {
    suspend fun syncPendingData()
}

class NetworkMonitor(
    context: Context,
    private val lifecycleScope: CoroutineScope?,
    private val repository: ISyncableRepository
) {
    private val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    private var lastSyncAttempt = 0L
    private val syncDebounceMs = 10000L // 10 seconds

    private val networkCallback = @RequiresApi(Build.VERSION_CODES.LOLLIPOP)
    object : ConnectivityManager.NetworkCallback() {
        override fun onAvailable(network: Network) {
            super.onAvailable(network)

            val currentTime = System.currentTimeMillis()
            if (currentTime - lastSyncAttempt > syncDebounceMs) {
                lastSyncAttempt = currentTime
                lifecycleScope?.launch {
                    repository.syncPendingData()
                }
            }
        }
    }

    // Start monitoring network changes
    fun startMonitoring() {
        val networkRequest = NetworkRequest.Builder()
            .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            .build()

        connectivityManager.registerNetworkCallback(networkRequest, networkCallback)
    }

    // Stop monitoring
    fun stopMonitoring() {
        connectivityManager.unregisterNetworkCallback(networkCallback)
    }

    // Check if network is currently available
    fun isNetworkAvailable(): Boolean {
        val network = connectivityManager.activeNetwork
        val capabilities = connectivityManager.getNetworkCapabilities(network)
        return capabilities?.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) == true
    }
}
