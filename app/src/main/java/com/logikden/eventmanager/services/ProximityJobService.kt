package com.logikden.eventmanager.services

import android.app.job.JobParameters
import android.app.job.JobService
import android.os.Build
import androidx.annotation.RequiresApi

class ProximityJobService : JobService() {
    override fun onStartJob(params: JobParameters?): <PERSON><PERSON>an {
        ProximityService.startService(this)
        return true
    }

    override fun onStopJob(params: JobParameters?): <PERSON><PERSON><PERSON> {
        return true
    }
}