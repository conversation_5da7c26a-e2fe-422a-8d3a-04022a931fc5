package com.logikden.eventmanager.services.routing

import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.Path

/**
 * API interface for OpenRouteService
 */
interface OpenRouteServiceApi {
    @POST("v2/directions/{profile}/geojson")
    suspend fun getDirections(
        @Header("Authorization") apiKey: String,
        @Path("profile") profile: String,
        @Body requestBody: DirectionsRequest
    ): Response<DirectionsResponse>

    @POST("v2/directions/{profile}/json")
    suspend fun getDirectionsJson(
        @Header("Authorization") apiKey: String,
        @Path("profile") profile: String,
        @Body requestBody: DirectionsRequest
    ): Response<DirectionsResponse>
}

/**
 * Request body for directions API
 */
data class DirectionsRequest(
    val coordinates: List<List<Double>>,
    val radiuses: List<Double>? = null,
    val instructions: Boolean = true,
    val language: String = "en",
    val units: String = "km",
    val geometry: Boolean = true
)

/**
 * Response from directions API
 */
data class DirectionsResponse(
    val type: String,
    val features: List<Feature>,
    val bbox: List<Double>,
    val metadata: Metadata
)

data class Feature(
    val type: String,
    val properties: Properties,
    val geometry: Geometry
)

data class Properties(
    val segments: List<Segment>,
    val summary: Summary,
    val way_points: List<Int>
)

data class Segment(
    val distance: Double,
    val duration: Double,
    val steps: List<Step>
)

data class Step(
    val distance: Double,
    val duration: Double,
    val type: Int,
    val instruction: String,
    val name: String,
    val way_points: List<Int>
)

data class Summary(
    val distance: Double,
    val duration: Double
)

data class Geometry(
    val coordinates: List<List<Double>>,
    val type: String
)

data class Metadata(
    val attribution: String,
    val service: String,
    val timestamp: Long,
    val query: QueryInfo,
    val engine: Engine
)

// Renamed from Query to QueryInfo to avoid conflict with Retrofit's Query annotation
data class QueryInfo(
    val coordinates: List<List<Double>>,
    val profile: String,
    val format: String
)

data class Engine(
    val version: String,
    val build_date: String,
    val graph_date: String
)
