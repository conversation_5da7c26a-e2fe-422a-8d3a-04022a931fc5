package com.logikden.eventmanager.services

import android.Manifest
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.util.Log
import androidx.core.app.ActivityCompat
import com.google.android.gms.location.ActivityRecognition
import com.google.android.gms.location.ActivityRecognitionClient
import com.google.android.gms.location.ActivityTransition
import com.google.android.gms.location.ActivityTransitionRequest
import com.google.android.gms.location.DetectedActivity
import com.logikden.eventmanager.utils.SettingsManager

/**
 * Manages Activity Recognition API to detect when user is driving
 * Only starts location tracking when IN_VEHICLE activity is detected
 */
class ActivityRecognitionManager private constructor(private val context: Context) {

    companion object {
        private const val TAG = "ActivityRecognitionManager"
        private const val ACTIVITY_RECOGNITION_REQUEST_CODE = 1001

        @Volatile
        private var INSTANCE: ActivityRecognitionManager? = null

        fun getInstance(context: Context): ActivityRecognitionManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ActivityRecognitionManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }

    private val activityRecognitionClient: ActivityRecognitionClient = ActivityRecognition.getClient(context)
    private val settingsManager = SettingsManager.getInstance(context)

    // Callback interface for activity changes
    interface ActivityRecognitionCallback {
        fun onDrivingStarted()
        fun onDrivingStopped()
        fun onStationaryDetected()
        fun onMovementDetected()
        fun onActivityChanged(activity: DetectedActivity)
    }

    private val callbacks = mutableSetOf<ActivityRecognitionCallback>()

    // Current driving state
    private var isDriving = false
    private var isActivityRecognitionActive = false
    private var isStationary = false
    private var lastActivityTime = System.currentTimeMillis()

    /**
     * Add callback for activity recognition events
     */
    fun addCallback(callback: ActivityRecognitionCallback) {
        callbacks.add(callback)
    }

    /**
     * Remove callback
     */
    fun removeCallback(callback: ActivityRecognitionCallback) {
        callbacks.remove(callback)
    }

    /**
     * Check if activity recognition optimization is enabled in settings
     */
    private fun isActivityRecognitionEnabled(): Boolean {
        return settingsManager.isActivityRecognitionEnabled()
    }

    /**
     * Check if we have activity recognition permission
     */
    fun hasActivityRecognitionPermission(): Boolean {
        return ActivityCompat.checkSelfPermission(
            context,
            Manifest.permission.ACTIVITY_RECOGNITION
        ) == PackageManager.PERMISSION_GRANTED
    }

    /**
     * Start activity recognition
     */
    fun startActivityRecognition() {
        if (!isActivityRecognitionEnabled()) {
            Log.d(TAG, "Activity recognition is disabled in settings")
            return
        }

        if (!hasActivityRecognitionPermission()) {
            Log.w(TAG, "Activity recognition permission not granted - location tracking will work normally")
            // Notify callbacks that we can't use activity recognition
            notifyCallbacks { it.onActivityChanged(DetectedActivity(DetectedActivity.UNKNOWN, 0)) }
            return
        }

        if (isActivityRecognitionActive) {
            Log.d(TAG, "Activity recognition already active")
            return
        }

        try {
            val transitions = mutableListOf<ActivityTransition>()

            // Add transitions for IN_VEHICLE activity
            transitions.add(
                ActivityTransition.Builder()
                    .setActivityType(DetectedActivity.IN_VEHICLE)
                    .setActivityTransition(ActivityTransition.ACTIVITY_TRANSITION_ENTER)
                    .build()
            )

            transitions.add(
                ActivityTransition.Builder()
                    .setActivityType(DetectedActivity.IN_VEHICLE)
                    .setActivityTransition(ActivityTransition.ACTIVITY_TRANSITION_EXIT)
                    .build()
            )

            // Also monitor STILL and ON_FOOT to better detect when driving stops
            transitions.add(
                ActivityTransition.Builder()
                    .setActivityType(DetectedActivity.STILL)
                    .setActivityTransition(ActivityTransition.ACTIVITY_TRANSITION_ENTER)
                    .build()
            )

            transitions.add(
                ActivityTransition.Builder()
                    .setActivityType(DetectedActivity.ON_FOOT)
                    .setActivityTransition(ActivityTransition.ACTIVITY_TRANSITION_ENTER)
                    .build()
            )

            // Add WALKING and RUNNING for better motion detection
            transitions.add(
                ActivityTransition.Builder()
                    .setActivityType(DetectedActivity.WALKING)
                    .setActivityTransition(ActivityTransition.ACTIVITY_TRANSITION_ENTER)
                    .build()
            )

            transitions.add(
                ActivityTransition.Builder()
                    .setActivityType(DetectedActivity.RUNNING)
                    .setActivityTransition(ActivityTransition.ACTIVITY_TRANSITION_ENTER)
                    .build()
            )

            val request = ActivityTransitionRequest(transitions)
            val pendingIntent = getActivityRecognitionPendingIntent()

            activityRecognitionClient.requestActivityTransitionUpdates(request, pendingIntent)
                .addOnSuccessListener {
                    isActivityRecognitionActive = true
                    Log.d(TAG, "Activity recognition started successfully")
                }
                .addOnFailureListener { e ->
                    Log.e(TAG, "Failed to start activity recognition", e)
                }

        } catch (e: SecurityException) {
            Log.e(TAG, "Security exception starting activity recognition", e)
        }
    }

    /**
     * Stop activity recognition
     */
    fun stopActivityRecognition() {
        if (!isActivityRecognitionActive) {
            Log.d(TAG, "Activity recognition not active")
            return
        }

        try {
            val pendingIntent = getActivityRecognitionPendingIntent()
            activityRecognitionClient.removeActivityTransitionUpdates(pendingIntent)
                .addOnSuccessListener {
                    isActivityRecognitionActive = false
                    Log.d(TAG, "Activity recognition stopped successfully")
                }
                .addOnFailureListener { e ->
                    Log.e(TAG, "Failed to stop activity recognition", e)
                }
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping activity recognition", e)
        }
    }

    /**
     * Get PendingIntent for activity recognition
     */
    private fun getActivityRecognitionPendingIntent(): PendingIntent {
        val intent = Intent(context, ActivityRecognitionReceiver::class.java)
        return PendingIntent.getBroadcast(
            context,
            ACTIVITY_RECOGNITION_REQUEST_CODE,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
    }

    /**
     * Handle activity transition result
     */
    fun handleActivityTransition(activityType: Int, transitionType: Int) {
        Log.d(TAG, "Activity transition: ${getActivityString(activityType)} - ${getTransitionString(transitionType)}")

        when (activityType) {
            DetectedActivity.IN_VEHICLE -> {
                when (transitionType) {
                    ActivityTransition.ACTIVITY_TRANSITION_ENTER -> {
                        if (!isDriving) {
                            isDriving = true
                            Log.i(TAG, "Driving started - enabling location tracking")
                            notifyCallbacks { it.onDrivingStarted() }
                        }
                    }
                    ActivityTransition.ACTIVITY_TRANSITION_EXIT -> {
                        if (isDriving) {
                            isDriving = false
                            Log.i(TAG, "Driving stopped - disabling location tracking")
                            notifyCallbacks { it.onDrivingStopped() }
                        }
                    }
                }
            }
            DetectedActivity.STILL -> {
                if (transitionType == ActivityTransition.ACTIVITY_TRANSITION_ENTER) {
                    if (isDriving) {
                        isDriving = false
                        Log.i(TAG, "User stopped driving - disabling location tracking")
                        notifyCallbacks { it.onDrivingStopped() }
                    }
                    if (!isStationary) {
                        isStationary = true
                        Log.i(TAG, "User is now stationary - reducing location update frequency")
                        notifyCallbacks { it.onStationaryDetected() }
                    }
                }
            }
            DetectedActivity.ON_FOOT, DetectedActivity.WALKING, DetectedActivity.RUNNING -> {
                if (transitionType == ActivityTransition.ACTIVITY_TRANSITION_ENTER) {
                    if (isDriving) {
                        isDriving = false
                        Log.i(TAG, "User stopped driving and is now walking - disabling location tracking")
                        notifyCallbacks { it.onDrivingStopped() }
                    }
                    if (isStationary) {
                        isStationary = false
                        Log.i(TAG, "User started moving - increasing location update frequency")
                        notifyCallbacks { it.onMovementDetected() }
                    }
                }
            }
        }
    }

    /**
     * Get current driving state
     */
    fun isDriving(): Boolean = isDriving

    /**
     * Get current stationary state
     */
    fun isStationary(): Boolean = isStationary

    /**
     * Get activity recognition status for debugging
     */
    fun getActivityRecognitionStatus(): String {
        return buildString {
            appendLine("Activity Recognition Status:")
            appendLine("- Enabled in settings: ${isActivityRecognitionEnabled()}")
            appendLine("- Permission granted: ${hasActivityRecognitionPermission()}")
            appendLine("- Currently active: $isActivityRecognitionActive")
            appendLine("- Currently driving: $isDriving")
            appendLine("- Currently stationary: $isStationary")
            appendLine("- Last activity time: ${System.currentTimeMillis() - lastActivityTime}ms ago")
            appendLine("- Registered callbacks: ${callbacks.size}")
        }
    }

    /**
     * Force start location tracking (for debugging)
     */
    fun forceStartLocationTracking() {
        Log.i(TAG, "Force starting location tracking - simulating driving state")
        if (!isDriving) {
            isDriving = true
            notifyCallbacks { it.onDrivingStarted() }
        }
    }

    /**
     * Notify all callbacks safely
     */
    private fun notifyCallbacks(action: (ActivityRecognitionCallback) -> Unit) {
        callbacks.forEach { callback ->
            try {
                action(callback)
            } catch (e: Exception) {
                Log.e(TAG, "Error notifying activity recognition callback", e)
            }
        }
    }

    /**
     * Get human-readable activity string
     */
    private fun getActivityString(activityType: Int): String {
        return when (activityType) {
            DetectedActivity.IN_VEHICLE -> "IN_VEHICLE"
            DetectedActivity.ON_BICYCLE -> "ON_BICYCLE"
            DetectedActivity.ON_FOOT -> "ON_FOOT"
            DetectedActivity.RUNNING -> "RUNNING"
            DetectedActivity.STILL -> "STILL"
            DetectedActivity.TILTING -> "TILTING"
            DetectedActivity.UNKNOWN -> "UNKNOWN"
            DetectedActivity.WALKING -> "WALKING"
            else -> "UNRECOGNIZED($activityType)"
        }
    }

    /**
     * Get human-readable transition string
     */
    private fun getTransitionString(transitionType: Int): String {
        return when (transitionType) {
            ActivityTransition.ACTIVITY_TRANSITION_ENTER -> "ENTER"
            ActivityTransition.ACTIVITY_TRANSITION_EXIT -> "EXIT"
            else -> "UNKNOWN($transitionType)"
        }
    }
}
