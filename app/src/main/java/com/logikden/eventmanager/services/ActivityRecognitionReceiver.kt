package com.logikden.eventmanager.services

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import com.google.android.gms.location.ActivityTransitionResult

/**
 * BroadcastReceiver that handles Activity Recognition API results
 * Receives activity transition events and forwards them to ActivityRecognitionManager
 */
class ActivityRecognitionReceiver : BroadcastReceiver() {
    
    companion object {
        private const val TAG = "ActivityRecognitionReceiver"
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        Log.d(TAG, "Received activity recognition broadcast")
        
        try {
            if (ActivityTransitionResult.hasResult(intent)) {
                val result = ActivityTransitionResult.extractResult(intent)
                
                if (result != null) {
                    Log.d(TAG, "Processing ${result.transitionEvents.size} activity transition events")
                    
                    val activityRecognitionManager = ActivityRecognitionManager.getInstance(context)
                    
                    // Process each transition event
                    for (event in result.transitionEvents) {
                        Log.d(TAG, "Activity transition event: " +
                                "Activity=${event.activityType}, " +
                                "Transition=${event.transitionType}, " +
                                "Elapsed=${event.elapsedRealTimeNanos}")
                        
                        // Forward to ActivityRecognitionManager
                        activityRecognitionManager.handleActivityTransition(
                            event.activityType,
                            event.transitionType
                        )
                    }
                } else {
                    Log.w(TAG, "ActivityTransitionResult was null")
                }
            } else {
                Log.w(TAG, "Intent does not contain activity transition result")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error processing activity recognition result", e)
        }
    }
}
