package com.logikden.eventmanager.services.routing

import android.content.Context
import android.location.Location
import android.util.Log
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import org.osmdroid.util.GeoPoint
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

/**
 * Service for calculating routes and distances using OpenRouteService
 */
class RoutingService private constructor(private val context: Context) {
    private val TAG = "RoutingService"

    // OpenRouteService API key
    // If this key doesn't work, get a new one from https://openrouteservice.org/dev/#/signup
    private var API_KEY = "5b3ce3597851110001cf624839c9c3e38277471a979035e5966fbbc3"

    // Cache for routes to minimize API calls
    private val routeCache = mutableMapOf<String, RouteInfo>()

    private val api: OpenRouteServiceApi by lazy {
        val loggingInterceptor = HttpLoggingInterceptor().apply {
            level = HttpLoggingInterceptor.Level.BODY
        }

        val client = OkHttpClient.Builder()
            .addInterceptor(loggingInterceptor)
            .connectTimeout(15, TimeUnit.SECONDS)
            .readTimeout(15, TimeUnit.SECONDS)
            .build()

        // Create a custom Gson instance that can handle our model classes properly
        val gson = GsonBuilder()
            .setLenient()
            .create()

        val retrofit = Retrofit.Builder()
            .baseUrl("https://api.openrouteservice.org/")
            .client(client)
            .addConverterFactory(GsonConverterFactory.create(gson))
            .build()

        retrofit.create(OpenRouteServiceApi::class.java)
    }

    /**
     * Calculate route between two points
     * @param startLat Starting latitude
     * @param startLng Starting longitude
     * @param endLat Ending latitude
     * @param endLng Ending longitude
     * @param profile Routing profile (driving-car, foot-walking, cycling-regular)
     * @return RouteInfo containing distance, duration, and route points
     */
    suspend fun calculateRoute(
        startLat: Double,
        startLng: Double,
        endLat: Double,
        endLng: Double,
        profile: String = "driving-car"
    ): RouteInfo? = withContext(Dispatchers.IO) {
        try {
            // Check cache first
            val cacheKey = "$startLat,$startLng,$endLat,$endLng,$profile"
            routeCache[cacheKey]?.let {
                Log.d(TAG, "Route found in cache")
                return@withContext it
            }

            // Create request body
            val requestBody = DirectionsRequest(
                coordinates = listOf(
                    listOf(startLng, startLat),
                    listOf(endLng, endLat)
                )
            )

            // Make API call with proper API key format
            val response = api.getDirections("Bearer $API_KEY", profile, requestBody)

            Log.d(TAG, "API call made with profile: $profile")

            if (response.isSuccessful) {
                val directionsResponse = response.body()

                if (directionsResponse != null && directionsResponse.features.isNotEmpty()) {
                    val feature = directionsResponse.features[0]
                    // OpenRouteService returns distance in meters, convert to kilometers
                    val distance = feature.properties.summary.distance
                    val duration = feature.properties.summary.duration

                    Log.d(TAG, "API returned distance: ${feature.properties.summary.distance}m, converted to $distance km")
                    Log.d(TAG, "API returned duration: ${feature.properties.summary.duration}s, which is ${duration / 60} minutes")

                    // Convert coordinates to GeoPoints (note: API returns [lng, lat] but GeoPoint uses [lat, lng])
                    val routePoints = feature.geometry.coordinates.map { coord ->
                        GeoPoint(coord[1], coord[0])
                    }

                    val routeInfo = RouteInfo(
                        distanceKm = distance,
                        durationSeconds = duration,
                        routePoints = routePoints
                    )

                    // Cache the result
                    routeCache[cacheKey] = routeInfo

                    return@withContext routeInfo
                }
            } else {
                val responseCode = response.code()
                Log.e(TAG, "GeoJSON API call failed: $responseCode ${response.message()}")

                // If we get a 401 Unauthorized error, the API key might be invalid or expired
                if (responseCode == 401) {
                    Log.e(TAG, "API key unauthorized (401). Please check your API key or get a new one from openrouteservice.org")
                    // Fall through to use the fallback distance calculation
                } else {
                    // Try the JSON endpoint as a fallback for other errors
                    try {
                        Log.d(TAG, "Trying JSON endpoint as fallback")
                        val jsonResponse = api.getDirectionsJson("Bearer $API_KEY", profile, requestBody)

                        if (jsonResponse.isSuccessful) {
                            val directionsResponse = jsonResponse.body()

                            if (directionsResponse != null && directionsResponse.features.isNotEmpty()) {
                                val feature = directionsResponse.features[0]
                                val distance = feature.properties.summary.distance / 1000.0
                                val duration = feature.properties.summary.duration

                                Log.d(TAG, "JSON API returned distance: ${feature.properties.summary.distance}m, converted to $distance km")

                                val routePoints = feature.geometry.coordinates.map { coord ->
                                    GeoPoint(coord[1], coord[0])
                                }

                                val routeInfo = RouteInfo(
                                    distanceKm = distance,
                                    durationSeconds = duration,
                                    routePoints = routePoints
                                )

                                routeCache[cacheKey] = routeInfo
                                return@withContext routeInfo
                            }
                        } else {
                            Log.e(TAG, "JSON API call also failed: ${jsonResponse.code()} ${jsonResponse.message()}")
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error with JSON fallback", e)
                    }
                }
            }

            // Fallback to straight-line distance if API call fails
            // LocationUtils.calculateDistance already returns distance in kilometers
            val fallbackDistance = LocationUtils.calculateDistance(startLat, startLng, endLat, endLng)
            // Estimate duration based on average speed (50 km/h)
            val fallbackDuration = (fallbackDistance / 50.0) * 3600 // Convert to seconds

            Log.d(TAG, "Using fallback distance: $fallbackDistance km, duration: $fallbackDuration seconds")

            return@withContext RouteInfo(
                distanceKm = fallbackDistance.toDouble(),
                durationSeconds = fallbackDuration,
                routePoints = listOf(
                    GeoPoint(startLat, startLng),
                    GeoPoint(endLat, endLng)
                ),
                isFallback = true
            )
        } catch (e: Exception) {
            // Check for the specific Gson error related to Query annotation
            if (e.message?.contains("retrofit2.http.Query") == true) {
                Log.e(TAG, "Gson error with Query class. This should be fixed by renaming Query to QueryInfo", e)
            } else {
                Log.e(TAG, "Error calculating route", e)
            }

            // Fallback to straight-line distance
            // LocationUtils.calculateDistance already returns distance in kilometers
            val fallbackDistance = LocationUtils.calculateDistance(startLat, startLng, endLat, endLng)
            // Estimate duration based on average speed (50 km/h)
            val fallbackDuration = (fallbackDistance / 50.0) * 3600 // Convert to seconds

            Log.d(TAG, "Using fallback distance (exception): $fallbackDistance km, duration: $fallbackDuration seconds")

            return@withContext RouteInfo(
                distanceKm = fallbackDistance.toDouble(),
                durationSeconds = fallbackDuration,
                routePoints = listOf(
                    GeoPoint(startLat, startLng),
                    GeoPoint(endLat, endLng)
                ),
                isFallback = true
            )
        }
    }

    /**
     * Calculate route between two locations
     */
    suspend fun calculateRoute(
        startLocation: Location,
        endLat: Double,
        endLng: Double,
        profile: String = "driving-car"
    ): RouteInfo? {
        return calculateRoute(
            startLocation.latitude,
            startLocation.longitude,
            endLat,
            endLng,
            profile
        )
    }

    /**
     * Clear the route cache
     */
    fun clearCache() {
        routeCache.clear()
        Log.d(TAG, "Route cache cleared")
    }

    /**
     * Update the API key
     * Call this if you get 401 Unauthorized errors
     * @param newApiKey The new API key from OpenRouteService
     */
    fun updateApiKey(newApiKey: String) {
        API_KEY = newApiKey
        Log.d(TAG, "API key updated")
        // Clear the cache to ensure we retry with the new key
        clearCache()
    }

    companion object {
        @Volatile
        private var instance: RoutingService? = null

        fun getInstance(context: Context): RoutingService {
            return instance ?: synchronized(this) {
                instance ?: RoutingService(context.applicationContext).also { instance = it }
            }
        }
    }
}

/**
 * Data class to hold route information
 */
data class RouteInfo(
    var distanceKm: Double,
    val durationSeconds: Double,
    val routePoints: List<GeoPoint>,
    val isFallback: Boolean = false
) {
    /**
     * Get formatted distance string
     * Always displays in kilometers for consistency
     */
    fun getFormattedDistance(): String {
        return when {
            distanceKm < 0.1 -> "0.1 km" // Minimum display value for very short distances
            distanceKm < 10 -> "%.1f km".format(distanceKm)
            else -> "${distanceKm.toInt()} km"
        }
    }

    /**
     * Get formatted duration string
     */
    fun getFormattedDuration(): String {
        // Ensure duration is not negative
        val positiveDuration = durationSeconds.coerceAtLeast(0.0)

        val hours = (positiveDuration / 3600).toInt()
        val minutes = ((positiveDuration % 3600) / 60).toInt()

        // Ensure we show at least 1 minute even for very short durations
        val adjustedMinutes = if (hours == 0 && minutes == 0) 1 else minutes

        return when {
            hours > 0 -> "$hours h $adjustedMinutes min"
            else -> "$adjustedMinutes min"
        }
    }
}
