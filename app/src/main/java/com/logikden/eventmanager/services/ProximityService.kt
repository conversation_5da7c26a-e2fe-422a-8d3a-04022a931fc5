package com.logikden.eventmanager.services

import android.Manifest
import android.app.*
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Context.RECEIVER_NOT_EXPORTED
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.location.Location
import android.media.AudioAttributes
import android.media.AudioManager
import android.media.MediaPlayer
import android.media.Ringtone
import android.media.RingtoneManager
import android.media.ToneGenerator
import android.os.*
import androidx.lifecycle.LifecycleService
import com.logikden.eventmanager.MainActivity
import com.logikden.eventmanager.R
import android.app.job.JobScheduler
import androidx.annotation.RequiresPermission
import android.util.Log
import kotlinx.coroutines.*
import com.logikden.eventmanager.data.AppDatabase
import android.content.ContextWrapper
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import androidx.core.content.ContextCompat
import com.logikden.eventmanager.utils.LocationConstants
import com.logikden.eventmanager.utils.SettingsManager
import com.logikden.eventmanager.utils.LocationCacheManager
import com.logikden.eventmanager.utils.SubscriberKeyManager


class ProximityService : LifecycleService() {
    companion object {
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "proximity_notification_channel"
        private const val CHANNEL_GROUP_ID = "proximity_group"
        private var currentRingtone: Ringtone? = null

        // Intent action for notification clicks
        const val ACTION_OPEN_EVENT_TRACKING = "com.logikden.eventmanager.ACTION_OPEN_EVENT_TRACKING"

        // Intent extras
        const val EXTRA_EVENT_NAME = "event_name"
        const val EXTRA_EVENT_LAT = "event_lat"
        const val EXTRA_EVENT_LNG = "event_lng"

        fun startService(context: Context) {
            val intent = Intent(context, ProximityService::class.java)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }
    }

    private data class Destination(
        val latitude: Double,
        val longitude: Double,
        val name: String,
        var lastNotifiedDistance: Float? = null,
        var isWithinThreshold: Boolean = false
    )

    private lateinit var locationService: LocationService
    private lateinit var settingsManager: SettingsManager
    private lateinit var locationCacheManager: LocationCacheManager
    private var activityRecognitionManager: ActivityRecognitionManager? = null

    // Use a thread-safe list for destinations to avoid ConcurrentModificationException
    private val destinationsLock = Object()
    private var destinations = mutableListOf<Destination>()

    private var proximityThresholdMeters: Float = 1000f
    private var wakeLock: PowerManager.WakeLock? = null
    private var jobScheduler: JobScheduler? = null
    private var settingsReceiver: BroadcastReceiver? = null

    private val locationCallback = object : LocationService.LocationUpdateCallback {
        @RequiresPermission(Manifest.permission.POST_NOTIFICATIONS)
        override fun onLocationReceived(location: Location) {
            // Cache location for background sync
            locationCacheManager.cacheLocationIfNeeded(location)
            checkProximity()
            releaseWakeLock()
        }

        override fun onLocationPermissionDenied() {
            stopSelf()
        }

        @RequiresPermission(Manifest.permission.POST_NOTIFICATIONS)
        override fun onLocationDisabled() {
            notifyLocationDisabled()
        }
    }

    @RequiresPermission(Manifest.permission.RECEIVE_BOOT_COMPLETED)
    override fun onCreate() {
        super.onCreate()
        locationService = LocationService.getInstance(this)
        settingsManager = SettingsManager.getInstance(this)
        locationCacheManager = LocationCacheManager.getInstance(this)
        createNotificationChannel()

        try {
            // Start foreground service with proper notification
            startForeground(NOTIFICATION_ID, createServiceNotification("Tracking events", "The app tracks only your subscribed events"))
            Log.d("ProximityService", "Started foreground service successfully")
        } catch (e: Exception) {
            // Handle potential SecurityException on Android 14
            Log.e("ProximityService", "Error starting foreground service: ${e.message}")
            // Try to recover by stopping the service
            stopSelf()
            return
        }

        loadSettings()
        setupWorkManager()
        setupDataSyncWorker()
        registerSettingsReceiver()
        initializeActivityRecognition()
    }

    private fun registerSettingsReceiver() {
        // Create and register broadcast receiver for settings changes
        if (settingsReceiver == null) {
            settingsReceiver = object : BroadcastReceiver() {
                override fun onReceive(context: Context, intent: Intent) {
                    if (intent.action == SettingsManager.ACTION_SETTINGS_CHANGED) {
                        val key = intent.getStringExtra(SettingsManager.EXTRA_SETTING_KEY)
                        Log.d("ProximityService", "Settings changed: $key")

                        // Handle specific settings changes
                        when (key) {
                            SettingsManager.KEY_ACTIVITY_RECOGNITION_ENABLED -> {
                                Log.d("ProximityService", "Activity recognition setting changed - restarting")
                                restartActivityRecognition()
                            }
                            SettingsManager.KEY_BACKGROUND_SYNC_ENABLED,
                            SettingsManager.KEY_BACKGROUND_SYNC_INTERVAL,
                            SettingsManager.KEY_WIFI_ONLY_SYNC,
                            SettingsManager.KEY_BATTERY_OPTIMIZATION_ENABLED -> {
                                Log.d("ProximityService", "Background sync setting changed - updating worker")
                                setupDataSyncWorker()
                            }
                            else -> {
                                loadSettings()
                            }
                        }
                    }
                }
            }
        }

        val filter = IntentFilter(SettingsManager.ACTION_SETTINGS_CHANGED)

        try {
            // Use the appropriate flag for Android 14 (API 34) and above
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                registerReceiver(settingsReceiver, filter, RECEIVER_NOT_EXPORTED)
                Log.d("ProximityService", "Registered receiver with Android 14+ flags")
            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                // For Android 13
                registerReceiver(settingsReceiver, filter, RECEIVER_NOT_EXPORTED)
                Log.d("ProximityService", "Registered receiver with Android 13 flags")
            } else {
                // For older Android versions
                @Suppress("UnspecifiedRegisterReceiverFlag")
                registerReceiver(settingsReceiver, filter)
                Log.d("ProximityService", "Registered receiver without flags")
            }
        } catch (e: Exception) {
            Log.e("ProximityService", "Error registering settings receiver: ${e.message}", e)
        }
    }

    /**
     * Initialize activity recognition for battery optimization
     */
    private fun initializeActivityRecognition() {
        if (settingsManager.isActivityRecognitionEnabled()) {
            activityRecognitionManager = ActivityRecognitionManager.getInstance(this)
            activityRecognitionManager?.addCallback(activityRecognitionCallback)
            activityRecognitionManager?.startActivityRecognition()
            Log.d("ProximityService", "Activity recognition initialized for ProximityService")
        } else {
            Log.d("ProximityService", "Activity recognition disabled in settings")
        }
    }

    /**
     * Restart activity recognition when settings change
     */
    private fun restartActivityRecognition() {
        // Clean up existing activity recognition
        activityRecognitionManager?.removeCallback(activityRecognitionCallback)
        activityRecognitionManager?.stopActivityRecognition()

        // Reinitialize with new settings
        initializeActivityRecognition()

        // Also restart activity recognition in LocationService
        locationService.restartActivityRecognition()

        // Update location service settings to reflect the change
        updateLocationServiceSettings()

        Log.d("ProximityService", "Activity recognition restarted with new settings")
    }

    // Activity recognition callback for ProximityService
    private val activityRecognitionCallback = object : ActivityRecognitionManager.ActivityRecognitionCallback {
        override fun onDrivingStarted() {
            Log.i("ProximityService", "Driving detected - proximity service will track more actively")
            // The LocationService will handle starting location updates
            // ProximityService just needs to be aware of the driving state
        }

        override fun onDrivingStopped() {
            Log.i("ProximityService", "Driving stopped - proximity service will reduce activity")
            // The LocationService will handle stopping location updates
            // ProximityService just needs to be aware of the non-driving state
        }

        override fun onStationaryDetected() {
            Log.i("ProximityService", "User stationary - reducing proximity check frequency")
            // Could implement reduced proximity checking when stationary
        }

        override fun onMovementDetected() {
            Log.i("ProximityService", "Movement detected - resuming normal proximity checks")
            // Could implement normal proximity checking when moving
        }

        override fun onActivityChanged(activity: com.google.android.gms.location.DetectedActivity) {
            Log.d("ProximityService", "Activity changed in ProximityService: ${activity.type} (confidence: ${activity.confidence}%)")
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        super.onStartCommand(intent, flags, startId)
        if (intent?.action == "RESTART_SERVICE") {
            loadSettings()
        }
        acquireWakeLock()
        locationService.addLocationCallback(locationCallback)
        updateLocationServiceSettings()
        return START_STICKY
    }

    override fun onDestroy() {
        super.onDestroy()
        locationService.removeLocationCallback(locationCallback)
        locationService.stopLocationUpdates()
        releaseWakeLock()
        ProximityWorker.stopWork(applicationContext)
        currentRingtone?.stop()
        currentRingtone = null

        // Clean up activity recognition
        activityRecognitionManager?.removeCallback(activityRecognitionCallback)
        activityRecognitionManager?.stopActivityRecognition()

        // Unregister the settings receiver
        if (settingsReceiver != null) {
            try {
                unregisterReceiver(settingsReceiver)
                Log.d("ProximityService", "Successfully unregistered settings receiver")
            } catch (e: IllegalArgumentException) {
                // This exception occurs if the receiver was not registered
                Log.w("ProximityService", "Receiver was not registered: ${e.message}")
            } catch (e: Exception) {
                Log.e("ProximityService", "Error unregistering receiver: ${e.message}", e)
            } finally {
                settingsReceiver = null
            }
        } else {
            Log.d("ProximityService", "No receiver to unregister")
        }
    }

    override fun onBind(intent: Intent): IBinder? {
        return super.onBind(intent)
    }

    private fun loadSettings() {
        Log.d("ProximityService", "Loading settings from SettingsManager")

        // Load destinations - safely clear the list first
        synchronized(destinationsLock) {
            destinations.clear()
        }

        CoroutineScope(Dispatchers.IO).launch {
            try {
                val database = AppDatabase.getDatabase(ContextWrapper(applicationContext), this)
                val eventDao = database.remoteEventReportDao()
                eventDao.getUnreachedReports().collect { reports ->
                    // Create a new list of destinations from the reports
                    val newDestinations = reports.map { report ->
                        Destination(
                            latitude = report.latitude,
                            longitude = report.longitude,
                            name = report.reportName
                        )
                    }

                    // Safely update the destinations list
                    synchronized(destinationsLock) {
                        destinations.clear()
                        destinations.addAll(newDestinations)
                    }

                    Log.d("ProximityService", "Updated ${newDestinations.size} destinations from flow")
                }
            } catch (e: Exception) {
                Log.e("ProximityService", "Error collecting destinations flow", e)
            }
        }

        // Load proximity threshold from SettingsManager
        val thresholdKm = settingsManager.getProximityThreshold()
        proximityThresholdMeters = thresholdKm * 1000
        Log.d("ProximityService", "Proximity threshold set to ${thresholdKm} km (${proximityThresholdMeters} meters)")

        // Load notification cooldown setting
        notificationCooldown = settingsManager.getNotificationCooldown()
        Log.d("ProximityService", "Notification cooldown set to ${notificationCooldown} ms")

        // Update location service settings
        updateLocationServiceSettings()
    }

    private fun updateLocationServiceSettings() {
        // Apply location update settings
        locationService.requestLocationUpdates(
            LocationConstants.UPDATE_INTERVAL,
            LocationConstants.FASTEST_INTERVAL,
            LocationConstants.MIN_DISTANCE_CHANGE
        )
    }

    @RequiresPermission(Manifest.permission.POST_NOTIFICATIONS)
    private fun checkProximity() {
        // Safely check if destinations is empty and create a copy for processing
        val destinationsCopy = synchronized(destinationsLock) {
            if (destinations.isEmpty()) {
                emptyList<Destination>() // Return empty list if destinations is empty
            } else {
                destinations.toList() // Create a thread-safe copy
            }
        }

        // Early return if no destinations to process
        if (destinationsCopy.isEmpty()) return

        // Use a coroutine to handle the suspending route distance calculations
        CoroutineScope(Dispatchers.IO).launch {
            destinationsCopy.forEach { destination ->
                try {
                    // Try to get route-based distance first
                    val routeDistance = locationService.calculateRouteDistanceTo(
                        destination.latitude,
                        destination.longitude
                    )

                    // If route distance is available, use it
                    if (routeDistance != null) {
                        processDestinationDistance(destination, routeDistance)
                    } else {
                        // Fall back to straight-line distance if route calculation fails
                        val fallbackDistance = locationService.calculateDistanceTo(
                            destination.latitude,
                            destination.longitude
                        )

                        if (fallbackDistance != null) {
                            Log.d("ProximityService", "Using fallback straight-line distance for ${destination.name}: $fallbackDistance meters")
                            processDestinationDistance(destination, fallbackDistance)
                        }
                    }
                } catch (e: Exception) {
                    Log.e("ProximityService", "Error checking proximity for ${destination.name}", e)
                }
            }
        }
    }

    /**
     * Process a destination's distance and trigger notifications if needed
     */
    @RequiresPermission(Manifest.permission.POST_NOTIFICATIONS)
    private fun processDestinationDistance(destination: Destination, distance: Float) {
        val wasWithinThreshold = destination.isWithinThreshold
        val isNowWithinThreshold = distance <= proximityThresholdMeters
        val significantChange = destination.lastNotifiedDistance?.let {
            Math.abs(distance - it) > 50f || Math.abs(distance - it) / it > 0.1
        } ?: true

        // Create a local copy of the destination with updated values
        val updatedDestination = destination.copy(
            isWithinThreshold = isNowWithinThreshold,
            lastNotifiedDistance = if (isNowWithinThreshold && (!wasWithinThreshold || significantChange)) {
                distance
            } else if (!isNowWithinThreshold && wasWithinThreshold) {
                null
            } else {
                destination.lastNotifiedDistance
            }
        )

        // Safely update the destination in the original list
        synchronized(destinationsLock) {
            val index = destinations.indexOfFirst { it.name == destination.name &&
                                                it.latitude == destination.latitude &&
                                                it.longitude == destination.longitude }
            if (index >= 0) {
                destinations[index] = updatedDestination
            }
        }

        // Trigger notifications based on the updated state
        if (isNowWithinThreshold && (!wasWithinThreshold || significantChange)) {
            notifyProximity(distance, destination.name)
        } else if (!isNowWithinThreshold && wasWithinThreshold) {
            updateServiceNotification("Tracking multiple events...", "")
        }
    }

    @RequiresPermission(Manifest.permission.POST_NOTIFICATIONS)
    private fun notifyProximity(distanceInMeters: Float, destinationName: String) {
        Log.d("ProximityService", "Checking proximity notification for: $destinationName at distance: $distanceInMeters meters")

        // Safely find the destination to get its coordinates
        val destination = synchronized(destinationsLock) {
            destinations.find { it.name == destinationName }
        }
        if (destination == null) {
            Log.e("ProximityService", "Destination not found: $destinationName")
            return
        }

        // Find the report type ID for this destination
        val reportTypeId = findReportTypeIdForDestination(destinationName)

        // Check if notifications are enabled for this report type
        if (reportTypeId != null) {
            // First check the Tile entity's notificationsEnabled property
            val tileNotificationsEnabled = runBlocking(Dispatchers.IO) {
                val database = AppDatabase.getDatabase(ContextWrapper(applicationContext), this)
                val tileDao = database.tileDao()
                val tile = tileDao.getById(reportTypeId)
                tile?.notificationsEnabled ?: true
            }

            if (!tileNotificationsEnabled) {
                Log.d("ProximityService", "Notifications disabled in Tile entity for report type: $destinationName (ID: $reportTypeId)")
                return
            }

            // Then check the SettingsManager preference as a backup
            val settingsEnabled = settingsManager.isReportTypeNotificationEnabled(reportTypeId)
            if (!settingsEnabled) {
                Log.d("ProximityService", "Notifications disabled in settings for report type: $destinationName (ID: $reportTypeId)")
                return
            }

            // Check if this report was submitted by the current user
            val isUserSubmittedReport = checkIfUserSubmittedReport(destination.latitude, destination.longitude, reportTypeId)
            if (isUserSubmittedReport) {
                Log.d("ProximityService", "Suppressing notification for user-submitted report: $destinationName (ID: $reportTypeId)")
                return
            }

            Log.d("ProximityService", "Notifications enabled for report type: $destinationName (ID: $reportTypeId)")
        } else {
            Log.d("ProximityService", "No report type ID found for: $destinationName, proceeding with notification")
        }

        // Always display distances in kilometers for consistency
        val distanceKm = distanceInMeters / 1000f
        val distanceMessage = when {
            distanceKm < 0.1 -> "$destinationName: 0.1 km away" // Minimum display value
            distanceKm < 10 -> "$destinationName: ${"%,.1f".format(distanceKm)} km away"
            else -> "$destinationName: ${distanceKm.toInt()} km away"
        }
        playAlertSound()

        val notification = createProximityNotification(
            "Approaching $destinationName",
            distanceMessage,
            destinationName,
            destination.latitude,
            destination.longitude
        )
        try {
            // Check notification permission for Android 13+
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                if (ContextCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS) != PackageManager.PERMISSION_GRANTED) {
                    Log.d("ProximityService", "Cannot show notification - permission not granted")
                    return
                }
            }

            with(NotificationManagerCompat.from(this)) {
                notify(NOTIFICATION_ID + destinationName.hashCode(), notification)
            }
            updateServiceNotification("Tracking multiple events", distanceMessage)
        } catch (e: SecurityException) {
            Log.e("ProximityService", "Failed to show notification: ${e.message}")
        }
    }

    private fun findReportTypeIdForDestination(destinationName: String): Int? {
        // This is a simple implementation - in a real app, you might need to query the database
        // to find the report type ID based on the destination name
        return try {
            // Use runBlocking directly instead of inside a coroutine
            val reportTypeIdAndNotificationStatus = runBlocking(Dispatchers.IO) {
                val database = AppDatabase.getDatabase(ContextWrapper(applicationContext), this)
                val reportDao = database.remoteEventReportDao()
                val report = reportDao.findByName(destinationName)
                val reportTypeId = report?.reportTypeId

                // If we found a report, check if notifications are enabled in the Tile entity
                var notificationsEnabled = true
                if (reportTypeId != null) {
                    val tileDao = database.tileDao()
                    val tile = tileDao.getById(reportTypeId)
                    notificationsEnabled = tile?.notificationsEnabled ?: true
                }

                Pair(reportTypeId, notificationsEnabled)
            }

            val reportTypeId = reportTypeIdAndNotificationStatus.first
            val notificationsEnabled = reportTypeIdAndNotificationStatus.second

            Log.d("ProximityService", "Found report type ID for $destinationName: $reportTypeId")

            // If we found a report type ID, check notification status
            if (reportTypeId != null) {
                // First check the Tile entity's notificationsEnabled property
                if (!notificationsEnabled) {
                    Log.d("ProximityService", "Notifications disabled in Tile entity for report type $reportTypeId ($destinationName)")
                    return reportTypeId
                }

                // Then check the SettingsManager preference as a backup
                val settingsEnabled = settingsManager.isReportTypeNotificationEnabled(reportTypeId)
                Log.d("ProximityService", "Notifications for report type $reportTypeId ($destinationName) are ${if (settingsEnabled) "enabled" else "disabled"}")
            }

            reportTypeId
        } catch (e: Exception) {
            Log.e("ProximityService", "Error finding report type ID for $destinationName", e)
            null
        }
    }

    @RequiresPermission(Manifest.permission.POST_NOTIFICATIONS)
    private fun notifyLocationDisabled() {
        // Location disabled notification is currently disabled
        // Could be re-enabled if needed in the future
    }

    /**
     * Check if a report at the given coordinates and with the given report type ID was submitted by the current user
     * @param latitude The latitude of the report
     * @param longitude The longitude of the report
     * @param reportTypeId The report type ID
     * @return True if the report was submitted by the current user, false otherwise
     */
    private fun checkIfUserSubmittedReport(latitude: Double, longitude: Double, reportTypeId: Int): Boolean {
        return try {
            // Use a reasonable radius (1 km) and a longer time window (2 hours) to find user-submitted reports
            val radiusKm = 1.0 // 1 kilometer
            val timeWindowMs = 2 * 60 * 60 * 1000L // 2 hours in milliseconds
            val currentTime = System.currentTimeMillis()
            val minTime = currentTime - timeWindowMs

            // Get the current user's subscriber ID for additional verification
            val currentSubscriberId = SubscriberKeyManager.getSubscriberId(applicationContext)

            Log.d("ProximityService", "Checking user-submitted reports for location ($latitude, $longitude) " +
                    "within ${radiusKm}km and ${timeWindowMs / (60 * 1000)}min window")

            // Query the database for recent reports by this user with the same report type
            val userReports = runBlocking(Dispatchers.IO) {
                val database = AppDatabase.getDatabase(ContextWrapper(applicationContext), this)
                val reportDao = database.pendingEventReportDao()

                // Get recent reports by type
                val recentReports = reportDao.getRecentReportsByType(reportTypeId, minTime)

                // Filter for reports owned by "me" (the current user)
                // This is the primary method of identifying user reports
                recentReports.filter { it.owner == "me" }
            }

            // Check if any of the user's reports are within the radius of this report
            val hasNearbyUserReport = userReports.any { userReport ->
                val distance = LocationUtils.calculateDistance(
                    latitude, longitude,
                    userReport.latitude, userReport.longitude
                )
                val isWithinRadius = distance <= radiusKm

                if (isWithinRadius) {
                    Log.d("ProximityService", "Found user-submitted report within ${distance}km " +
                            "at (${userReport.latitude}, ${userReport.longitude}) - suppressing notification")
                }

                isWithinRadius
            }

            Log.d("ProximityService", "User report check result: $hasNearbyUserReport " +
                    "(found ${userReports.size} user reports of type $reportTypeId)")

            hasNearbyUserReport
        } catch (e: Exception) {
            Log.e("ProximityService", "Error checking if report was submitted by user", e)
            false
        }
    }

    /**
     * Extension function to format Double to specified decimal places
     */
    private fun Double.format(digits: Int) = "%.${digits}f".format(this)

    private fun createServiceNotification(title: String, content: String): Notification {
        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            Intent(this, MainActivity::class.java),
            PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
        )
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(title)
            .setContentText(content)
            .setSmallIcon(R.drawable.ic_destination_marker)
            .setContentIntent(pendingIntent)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setOngoing(true)
            .setOnlyAlertOnce(true)
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .build()
    }

    @RequiresPermission(Manifest.permission.POST_NOTIFICATIONS)
    private fun updateServiceNotification(title: String, content: String) {
        try {
            // Check notification permission for Android 13+
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                if (ContextCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS) != PackageManager.PERMISSION_GRANTED) {
                    Log.d("ProximityService", "Cannot update notification - permission not granted")
                    return
                }
            }

            val notification = createServiceNotification(title, content)
            NotificationManagerCompat.from(this).notify(NOTIFICATION_ID, notification)
        } catch (e: SecurityException) {
            Log.e("ProximityService", "Failed to update notification: ${e.message}")
        }
    }

    private fun createProximityNotification(
        title: String,
        content: String,
        eventName: String,
        latitude: Double,
        longitude: Double
    ): Notification {
        // Create an intent with specific event data
        val intent = Intent(this, MainActivity::class.java).apply {
            action = ACTION_OPEN_EVENT_TRACKING
            putExtra(EXTRA_EVENT_NAME, eventName)
            putExtra(EXTRA_EVENT_LAT, latitude.toFloat())
            putExtra(EXTRA_EVENT_LNG, longitude.toFloat())
            flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
        }

        // Create a unique request code based on the event name to ensure different PendingIntents
        val requestCode = eventName.hashCode()

        val pendingIntent = PendingIntent.getActivity(
            this,
            requestCode,
            intent,
            PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(title)
            .setContentText(content)
            .setSmallIcon(R.drawable.ic_destination_marker)
            .setContentIntent(pendingIntent)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setVibrate(longArrayOf(0, 500, 250, 500))
            .setAutoCancel(true)
            .setCategory(NotificationCompat.CATEGORY_ALARM)
            .build()
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = NotificationManagerCompat.from(this)

            // Create channel group
            val group = NotificationChannelGroup(
                CHANNEL_GROUP_ID,
                getString(R.string.notification_channel_name)
            )
            notificationManager.createNotificationChannelGroup(group)

            // Create notification channel
            val channel = NotificationChannel(
                CHANNEL_ID,
                getString(R.string.notification_channel_name),
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = getString(R.string.notification_channel_description)
                setGroup(CHANNEL_GROUP_ID)
                setShowBadge(true)
                enableVibration(true)
                vibrationPattern = longArrayOf(0, 500, 250, 500)
            }
            notificationManager.createNotificationChannel(channel)
        }
    }

    @RequiresPermission(Manifest.permission.VIBRATE)
    private fun playAlertSound() {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastNotificationTime < notificationCooldown) {
            return
        }
        lastNotificationTime = currentTime

        // First try using Ringtone which is more reliable on Android 14
        try {
            val notification = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
            val ringtone = RingtoneManager.getRingtone(applicationContext, notification)
            ringtone.play()
            // Schedule release after playing
            Handler(Looper.getMainLooper()).postDelayed({
                try {
                    if (ringtone.isPlaying) {
                        ringtone.stop()
                    }
                } catch (e: Exception) {
                    Log.e("ProximityService", "Error stopping ringtone", e)
                }
            }, 3000) // 3 seconds should be enough for most notification sounds
            return
        } catch (e: Exception) {
            Log.e("ProximityService", "Ringtone failed", e)
        }

        // Fall back to MediaPlayer if Ringtone fails
        try {
            val mediaPlayer = MediaPlayer().apply {
                setAudioAttributes(
                    AudioAttributes.Builder()
                        .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                        .setUsage(AudioAttributes.USAGE_NOTIFICATION)
                        .build()
                )
                setDataSource(
                    applicationContext,
                    RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
                )
                prepare()
                setOnCompletionListener {
                    it.release()
                }
            }
            mediaPlayer.start()
            return
        } catch (e: Exception) {
            Log.e("ProximityService", "MediaPlayer failed", e)
        }

        // If MediaPlayer fails, try ToneGenerator
        try {
            val toneGenerator = ToneGenerator(AudioManager.STREAM_NOTIFICATION, 100)
            toneGenerator.startTone(ToneGenerator.TONE_PROP_BEEP)
            Handler(Looper.getMainLooper()).postDelayed({
                try {
                    toneGenerator.release()
                } catch (e: Exception) {
                    Log.e("ProximityService", "Error releasing ToneGenerator", e)
                }
            }, 1000)
            return
        } catch (e: Exception) {
            Log.e("ProximityService", "ToneGenerator failed", e)
        }

        // If all sound attempts fail, fall back to vibration
        val vibratorManager = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            getSystemService(Context.VIBRATOR_MANAGER_SERVICE) as VibratorManager
        } else {
            null
        }

        val vibrator = when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.S ->
                vibratorManager?.defaultVibrator
            else ->
                @Suppress("DEPRECATION")
                getSystemService(VIBRATOR_SERVICE) as Vibrator
        }

        vibrator?.let {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                it.vibrate(VibrationEffect.createOneShot(500, VibrationEffect.DEFAULT_AMPLITUDE))
            } else {
                @Suppress("DEPRECATION")
                it.vibrate(500)
            }
        }
    }

    private fun acquireWakeLock() {
        if (wakeLock?.isHeld != true) {
            val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
            wakeLock = powerManager.newWakeLock(
                PowerManager.PARTIAL_WAKE_LOCK,
                "ProximityService:ProximityWakeLock"
            ).apply {
                setReferenceCounted(false)
                acquire(10 * 60 * 1000L) // 10 minutes timeout
            }
        }
    }

    private fun releaseWakeLock() {
        wakeLock?.let {
            if (it.isHeld) {
                it.release()
            }
        }
    }

    private fun setupWorkManager() {
        ProximityWorker.startPeriodicWork(applicationContext)
    }

    private fun setupDataSyncWorker() {
        // Start background data sync worker if enabled
        if (settingsManager.isBackgroundSyncEnabled()) {
            DataSyncWorker.startPeriodicSync(applicationContext)
            Log.d("ProximityService", "Started background data sync worker")
        } else {
            // Stop data sync worker if disabled
            DataSyncWorker.stopPeriodicSync(applicationContext)
            Log.d("ProximityService", "Background data sync is disabled")
        }
    }

    private var notificationCooldown: Long = 3000L // Default value
    private var lastNotificationTime = 0L
}
