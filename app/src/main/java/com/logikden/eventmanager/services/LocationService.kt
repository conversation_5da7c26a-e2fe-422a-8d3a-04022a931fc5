package com.logikden.eventmanager.services

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.location.Location
import android.location.LocationManager
import android.provider.Settings
import androidx.appcompat.app.AlertDialog
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.Fragment
import com.google.android.gms.location.*
import com.logikden.eventmanager.utils.LocationConstants
import com.logikden.eventmanager.utils.SettingsManager
import com.logikden.eventmanager.utils.SpeedBasedUIManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import android.os.Looper
import androidx.core.app.ActivityCompat
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class LocationService private constructor(private val context: Context) {
    private var fusedLocationClient: FusedLocationProviderClient =
        LocationServices.getFusedLocationProviderClient(context)

    private val _locationFlow = MutableStateFlow<Location?>(null)
    val locationFlow: StateFlow<Location?> = _locationFlow.asStateFlow()

    // Add a flow for throttled location updates
    private val _throttledLocationFlow = MutableStateFlow<Location?>(null)
    val throttledLocationFlow: StateFlow<Location?> = _throttledLocationFlow

    // Current update frequency settings
    private var currentUpdateInterval = LocationConstants.UPDATE_INTERVAL
    private var currentFastestInterval = LocationConstants.FASTEST_INTERVAL
    private var currentMinDistance = LocationConstants.MIN_DISTANCE_CHANGE

    // Speed-based frequency adjustment
    private val speedReadings = mutableListOf<Pair<Float, Long>>() // speed in m/s and timestamp
    private var lastLocation: Location? = null
    private var isSpeedBasedFrequencyEnabled = true
    private var currentSpeedCategory = SpeedCategory.STATIONARY

    // Throttling job
    private var throttlingJob: Job? = null

    enum class SpeedCategory {
        STATIONARY, SLOW, MEDIUM, HIGH
    }

    private val _locationState = MutableStateFlow<LocationState>(LocationState.Unavailable)
    val locationState: StateFlow<LocationState> = _locationState.asStateFlow()

    private var currentLocation: Location? = null
    private val settingsManager = SettingsManager.getInstance(context)
    private var activityRecognitionManager: ActivityRecognitionManager? = null
    private var isLocationUpdatesActive = false

    companion object {
        // Singleton instance
        @Volatile
        private var INSTANCE: LocationService? = null

        // Get the singleton instance, creating it if necessary
        fun getInstance(context: Context): LocationService {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: LocationService(context.applicationContext).also {
                    INSTANCE = it
                    it.initializeActivityRecognition()
                }
            }
        }

        // Helper function to create location permission request in fragments
        fun createLocationPermissionRequest(fragment: Fragment, onPermissionResult: (Boolean) -> Unit): ActivityResultLauncher<Array<String>> {
            return fragment.registerForActivityResult(
                ActivityResultContracts.RequestMultiplePermissions()
            ) { permissions ->
                val granted = permissions.getOrDefault(Manifest.permission.ACCESS_FINE_LOCATION, false) ||
                        permissions.getOrDefault(Manifest.permission.ACCESS_COARSE_LOCATION, false)
                onPermissionResult(granted)
            }
        }

        fun requestLocationPermissions(permissionLauncher: ActivityResultLauncher<Array<String>>) {
            val permissions = mutableListOf(
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.ACCESS_COARSE_LOCATION
            )

            // Add background location permission for Android 10+ (API 29+)
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.Q) {
                permissions.add(Manifest.permission.ACCESS_BACKGROUND_LOCATION)
            }

            permissionLauncher.launch(permissions.toTypedArray())
        }

        // Location state definitions
        sealed class LocationState {
            data class Available(val location: Location) : LocationState()
            object Unavailable : LocationState()
            object Disabled : LocationState()
            object PermissionDenied : LocationState()
        }
    }

    // Keep the callback interface for backward compatibility
    interface LocationUpdateCallback {
        fun onLocationReceived(location: Location)
        fun onLocationPermissionDenied()
        fun onLocationDisabled()
    }

    internal val locationCallbacks = mutableListOf<LocationUpdateCallback>()

    // Activity recognition callback
    private val activityRecognitionCallback = object : ActivityRecognitionManager.ActivityRecognitionCallback {
        override fun onDrivingStarted() {
            android.util.Log.i("LocationService", "Driving detected - starting high-frequency location updates")
            startLocationUpdatesInternal(
                LocationConstants.UPDATE_INTERVAL,
                LocationConstants.FASTEST_INTERVAL,
                LocationConstants.MIN_DISTANCE_CHANGE
            )
        }

        override fun onDrivingStopped() {
            android.util.Log.i("LocationService", "Driving stopped - stopping location updates")
            stopLocationUpdatesInternal()
        }

        override fun onStationaryDetected() {
            android.util.Log.i("LocationService", "User stationary - switching to low-frequency updates")
            if (isLocationUpdatesActive) {
                startLocationUpdatesInternal(
                    LocationConstants.STATIONARY_UPDATE_INTERVAL,
                    LocationConstants.STATIONARY_FASTEST_INTERVAL,
                    LocationConstants.STATIONARY_MIN_DISTANCE_CHANGE
                )
            }
        }

        override fun onMovementDetected() {
            android.util.Log.i("LocationService", "Movement detected - switching to normal frequency updates")
            if (isLocationUpdatesActive && !activityRecognitionManager?.isDriving()!!) {
                startLocationUpdatesInternal(
                    LocationConstants.BATTERY_SAVING_UPDATE_INTERVAL,
                    LocationConstants.BATTERY_SAVING_FASTEST_INTERVAL,
                    LocationConstants.BATTERY_SAVING_MIN_DISTANCE_CHANGE
                )
            }
        }

        override fun onActivityChanged(activity: com.google.android.gms.location.DetectedActivity) {
            android.util.Log.d("LocationService", "Activity changed: ${activity.type} (confidence: ${activity.confidence}%)")
        }
    }

    private val locationCallback = object : LocationCallback() {
        override fun onLocationResult(result: LocationResult) {
            result.lastLocation?.let { location ->
                // Only use locations with acceptable accuracy
                if (location.accuracy <= LocationConstants.LOCATION_ACCURACY) {
                    currentLocation = location
                    _locationFlow.value = location
                    _locationState.value = LocationState.Available(location)

                    // Update speed-based frequency
                    updateSpeedBasedFrequency(location)

                    // Notify existing callbacks safely
                    safelyNotifyCallbacks { callback -> callback.onLocationReceived(location) }
                    android.util.Log.d("LocationService", "Location update: ${location.latitude}, ${location.longitude}, accuracy: ${location.accuracy}m, speed: ${getCurrentAverageSpeed()} km/h")
                } else {
                    // Log that we're ignoring a low-accuracy location
                    android.util.Log.d("LocationService", "Ignoring low accuracy location: ${location.accuracy} meters")
                }
            }
        }

        override fun onLocationAvailability(availability: LocationAvailability) {
            if (!availability.isLocationAvailable) {
                _locationState.value = LocationState.Disabled
                // Notify existing callbacks safely
                safelyNotifyCallbacks { callback -> callback.onLocationDisabled() }
            }
        }
    }

    fun addLocationCallback(callback: LocationUpdateCallback) {
        synchronized(locationCallbacks) {
            locationCallbacks.add(callback)
        }
        // Immediately notify of current location if available
        currentLocation?.let { location ->
            try {
                callback.onLocationReceived(location)
            } catch (e: Exception) {
                android.util.Log.e("LocationService", "Error in initial callback notification: ${e.message}")
            }
        }
    }

    fun removeLocationCallback(callback: LocationUpdateCallback) {
        synchronized(locationCallbacks) {
            locationCallbacks.remove(callback)
        }
    }

    /**
     * Safely notifies all callbacks, catching and logging any exceptions
     * to prevent crashes if a callback uses a null context
     */
    private fun safelyNotifyCallbacks(action: (LocationUpdateCallback) -> Unit) {
        // Create a copy of the callbacks list to avoid ConcurrentModificationException
        val callbacksCopy = synchronized(locationCallbacks) {
            locationCallbacks.toList()
        }

        // Iterate over the copy
        for (callback in callbacksCopy) {
            try {
                action(callback)
            } catch (e: Exception) {
                // Log the error but don't crash
                android.util.Log.e("LocationService", "Error notifying callback: ${e.message}")
            }
        }
    }

    fun getCurrentLocation(): Location? = currentLocation

    /**
     * Initialize activity recognition if enabled in settings
     */
    private fun initializeActivityRecognition() {
        if (settingsManager.isActivityRecognitionEnabled()) {
            activityRecognitionManager = ActivityRecognitionManager.getInstance(context)
            activityRecognitionManager?.addCallback(activityRecognitionCallback)
            activityRecognitionManager?.startActivityRecognition()
            android.util.Log.d("LocationService", "Activity recognition initialized and started")
        } else {
            android.util.Log.d("LocationService", "Activity recognition disabled in settings")
        }

        // Initialize speed-based frequency setting
        isSpeedBasedFrequencyEnabled = settingsManager.isSpeedBasedFrequencyEnabled()
        android.util.Log.d("LocationService", "Speed-based frequency ${if (isSpeedBasedFrequencyEnabled) "enabled" else "disabled"}")
    }

    /**
     * Restart activity recognition when settings change
     */
    fun restartActivityRecognition() {
        android.util.Log.d("LocationService", "Restarting activity recognition due to settings change")

        // Clean up existing activity recognition
        activityRecognitionManager?.removeCallback(activityRecognitionCallback)
        activityRecognitionManager?.stopActivityRecognition()

        // Reinitialize with new settings
        initializeActivityRecognition()

        // Update speed-based frequency setting
        val newSpeedBasedSetting = settingsManager.isSpeedBasedFrequencyEnabled()
        if (newSpeedBasedSetting != isSpeedBasedFrequencyEnabled) {
            setSpeedBasedFrequencyEnabled(newSpeedBasedSetting)
        }

        // If activity recognition is now disabled, ensure location updates are running
        if (!settingsManager.isActivityRecognitionEnabled() && !isLocationUpdatesActive) {
            android.util.Log.d("LocationService", "Activity recognition disabled - starting location updates")
            requestLocationUpdates()
        }
    }

    /**
     * Check if activity recognition is enabled and if we should track location
     */
    private fun shouldTrackLocation(): Boolean {
        return if (settingsManager.isActivityRecognitionEnabled()) {
            // Check if activity recognition is actually working
            val hasPermission = activityRecognitionManager?.hasActivityRecognitionPermission() ?: false
            if (!hasPermission) {
                android.util.Log.w("LocationService", "Activity recognition enabled but permission denied - allowing location tracking")
                return true // Allow location tracking if permission is denied
            }
            // Only track if driving or if activity recognition manager is not available
            activityRecognitionManager?.isDriving() ?: true
        } else {
            // Always track if activity recognition is disabled
            // With speed-based frequency, we can track continuously with smart battery optimization
            true
        }
    }

    /**
     * Request location updates with specific parameters
     */
    fun requestLocationUpdates(
        interval: Long = LocationConstants.UPDATE_INTERVAL,
        fastestInterval: Long = LocationConstants.FASTEST_INTERVAL,
        minDistance: Float = LocationConstants.MIN_DISTANCE_CHANGE
    ) {
        android.util.Log.d("LocationService", "requestLocationUpdates called - interval: $interval, fastestInterval: $fastestInterval, minDistance: $minDistance")

        // Store current update frequency settings
        currentUpdateInterval = interval
        currentFastestInterval = fastestInterval
        currentMinDistance = minDistance

        // Update throttling job with new interval
        setupThrottlingJob(fastestInterval)

        // If activity recognition is enabled, only start if we should track location
        if (settingsManager.isActivityRecognitionEnabled() && !shouldTrackLocation()) {
            android.util.Log.w("LocationService", "Activity recognition enabled but not driving - skipping location updates")
            android.util.Log.d("LocationService", "Activity recognition manager state: ${activityRecognitionManager?.getActivityRecognitionStatus()}")
            return
        }

        startLocationUpdatesInternal(interval, fastestInterval, minDistance)
    }

    /**
     * Set up throttling job to respect the update frequency
     */
    private fun setupThrottlingJob(interval: Long) {
        // Cancel existing job
        throttlingJob?.cancel()

        // Create new throttling job
        throttlingJob = CoroutineScope(Dispatchers.Default).launch {
            _locationFlow.collect { location ->
                location?.let {
                    // Update the throttled flow at the specified interval
                    _throttledLocationFlow.value = it
                    delay(interval) // This creates the throttling effect
                }
            }
        }
    }

    /**
     * Calculate speed and update frequency based on current speed
     */
    private fun updateSpeedBasedFrequency(newLocation: Location) {
        if (!isSpeedBasedFrequencyEnabled) return

        val currentTime = System.currentTimeMillis()

        // Calculate speed if we have a previous location
        lastLocation?.let { prevLocation ->
            val distance = prevLocation.distanceTo(newLocation) // in meters
            val timeDiff = (newLocation.time - prevLocation.time) / 1000f // in seconds

            if (timeDiff > 0) {
                val speed = distance / timeDiff // m/s

                // Add speed reading with timestamp
                speedReadings.add(Pair(speed, currentTime))

                // Clean old readings
                cleanOldSpeedReadings(currentTime)

                // Calculate average speed and determine category
                val avgSpeed = calculateAverageSpeed()
                val newSpeedCategory = determineSpeedCategory(avgSpeed)

                // Update frequency if speed category changed
                if (newSpeedCategory != currentSpeedCategory) {
                    currentSpeedCategory = newSpeedCategory
                    applySpeedBasedFrequency(newSpeedCategory)
                    android.util.Log.d("LocationService", "Speed category changed to $newSpeedCategory (avg speed: ${avgSpeed * 3.6f} km/h)")
                }
            }
        }

        lastLocation = newLocation
    }

    /**
     * Clean old speed readings
     */
    private fun cleanOldSpeedReadings(currentTime: Long) {
        speedReadings.removeAll { (_, timestamp) ->
            currentTime - timestamp > LocationConstants.MAX_SPEED_READING_AGE
        }
    }

    /**
     * Calculate average speed from recent readings
     */
    private fun calculateAverageSpeed(): Float {
        if (speedReadings.size < LocationConstants.MIN_SPEED_READINGS) {
            return 0f
        }

        return speedReadings.map { it.first }.average().toFloat()
    }

    /**
     * Determine speed category based on average speed
     */
    private fun determineSpeedCategory(avgSpeed: Float): SpeedCategory {
        return when {
            avgSpeed <= LocationConstants.SPEED_THRESHOLD_STATIONARY -> SpeedCategory.STATIONARY
            avgSpeed <= LocationConstants.SPEED_THRESHOLD_SLOW -> SpeedCategory.SLOW
            avgSpeed <= LocationConstants.SPEED_THRESHOLD_MEDIUM -> SpeedCategory.MEDIUM
            else -> SpeedCategory.HIGH
        }
    }

    /**
     * Apply speed-based frequency settings
     */
    private fun applySpeedBasedFrequency(speedCategory: SpeedCategory) {
        val (interval, fastestInterval, minDistance) = when (speedCategory) {
            SpeedCategory.STATIONARY -> Triple(
                LocationConstants.SPEED_STATIONARY_UPDATE_INTERVAL,
                LocationConstants.SPEED_STATIONARY_FASTEST_INTERVAL,
                LocationConstants.SPEED_STATIONARY_MIN_DISTANCE
            )
            SpeedCategory.SLOW -> Triple(
                LocationConstants.SPEED_SLOW_UPDATE_INTERVAL,
                LocationConstants.SPEED_SLOW_FASTEST_INTERVAL,
                LocationConstants.SPEED_SLOW_MIN_DISTANCE
            )
            SpeedCategory.MEDIUM -> Triple(
                LocationConstants.SPEED_MEDIUM_UPDATE_INTERVAL,
                LocationConstants.SPEED_MEDIUM_FASTEST_INTERVAL,
                LocationConstants.SPEED_MEDIUM_MIN_DISTANCE
            )
            SpeedCategory.HIGH -> Triple(
                LocationConstants.SPEED_HIGH_UPDATE_INTERVAL,
                LocationConstants.SPEED_HIGH_FASTEST_INTERVAL,
                LocationConstants.SPEED_HIGH_MIN_DISTANCE
            )
        }

        // Only update if we're currently using location updates
        if (isLocationUpdatesActive) {
            startLocationUpdatesInternal(interval, fastestInterval, minDistance)
        }
    }

    /**
     * Enable or disable speed-based frequency adjustment
     */
    fun setSpeedBasedFrequencyEnabled(enabled: Boolean) {
        isSpeedBasedFrequencyEnabled = enabled
        if (!enabled) {
            // Reset to default frequency
            currentSpeedCategory = SpeedCategory.STATIONARY
            speedReadings.clear()
        }
        android.util.Log.d("LocationService", "Speed-based frequency ${if (enabled) "enabled" else "disabled"}")
    }

    /**
     * Get current speed category
     */
    fun getCurrentSpeedCategory(): SpeedCategory = currentSpeedCategory

    /**
     * Get current average speed in km/h
     */
    fun getCurrentAverageSpeed(): Float {
        return calculateAverageSpeed() * 3.6f // Convert m/s to km/h
    }

    /**
     * Internal method to start location updates without activity recognition checks
     */
    private fun startLocationUpdatesInternal(
        interval: Long = LocationConstants.UPDATE_INTERVAL,
        fastestInterval: Long = LocationConstants.FASTEST_INTERVAL,
        minDistance: Float = LocationConstants.MIN_DISTANCE_CHANGE
    ) {
        try {
            // Check if location is enabled
            if (!isLocationEnabled()) {
                android.util.Log.d("LocationService", "Location is disabled")
                _locationState.value = LocationState.Disabled
                safelyNotifyCallbacks { callback -> callback.onLocationDisabled() }
                return
            }

            // Check for permissions
            if (!checkLocationPermission()) {
                android.util.Log.d("LocationService", "Location permission denied")
                _locationState.value = LocationState.PermissionDenied
                safelyNotifyCallbacks { callback -> callback.onLocationPermissionDenied() }
                return
            }

            // Create an optimized location request
            val request = LocationRequest.Builder(Priority.PRIORITY_HIGH_ACCURACY, interval)
                .setMinUpdateDistanceMeters(minDistance)
                .setMinUpdateIntervalMillis(fastestInterval)
                .setWaitForAccurateLocation(true) // Wait for accurate location before first update
                .setMaxUpdateDelayMillis(interval * 2) // Max delay between location updates
                .build()

            if (ActivityCompat.checkSelfPermission(
                    context,
                    Manifest.permission.ACCESS_FINE_LOCATION
                ) == PackageManager.PERMISSION_GRANTED
            ) {
                try {
                    // First get a single high-accuracy location update
                    requestSingleUpdate()

                    // Then start continuous updates
                    fusedLocationClient.requestLocationUpdates(
                        request,
                        locationCallback,
                        Looper.getMainLooper()
                    )
                    isLocationUpdatesActive = true
                    android.util.Log.d("LocationService", "Successfully requested location updates")
                } catch (e: Exception) {
                    android.util.Log.e("LocationService", "Error requesting location updates", e)
                    _locationState.value = LocationState.Unavailable
                    safelyNotifyCallbacks { callback -> callback.onLocationDisabled() }
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("LocationService", "Unexpected error in requestLocationUpdates", e)
            _locationState.value = LocationState.Unavailable
            safelyNotifyCallbacks { callback -> callback.onLocationDisabled() }
        }
    }

    private fun requestSingleUpdate() {
        try {
            if (!checkLocationPermission()) {
                android.util.Log.d("LocationService", "Cannot request single update - permission denied")
                return
            }

            if (!isLocationEnabled()) {
                android.util.Log.d("LocationService", "Cannot request single update - location disabled")
                return
            }

            // Create a one-time high accuracy request
            val request = LocationRequest.Builder(Priority.PRIORITY_HIGH_ACCURACY, 0)
                .setMaxUpdates(1) // Only one update
                .setWaitForAccurateLocation(true)
                .setDurationMillis(10000) // Wait up to 10 seconds for a good fix
                .build()

            if (ActivityCompat.checkSelfPermission(
                    context,
                    Manifest.permission.ACCESS_FINE_LOCATION
                ) == PackageManager.PERMISSION_GRANTED
            ) {
                fusedLocationClient.requestLocationUpdates(
                    request,
                    object : LocationCallback() {
                        override fun onLocationResult(result: LocationResult) {
                            result.lastLocation?.let { location ->
                                android.util.Log.d("LocationService", "Single update received: $location, accuracy: ${location.accuracy}m")
                                if (location.accuracy <= LocationConstants.LOCATION_ACCURACY) {
                                    currentLocation = location
                                    _locationFlow.value = location
                                    _locationState.value = LocationState.Available(location)
                                    safelyNotifyCallbacks { callback -> callback.onLocationReceived(location) }
                                } else {
                                    android.util.Log.d("LocationService", "Single update location has poor accuracy: ${location.accuracy}m")
                                }
                                // Remove this callback after getting the location
                                try {
                                    fusedLocationClient.removeLocationUpdates(this)
                                } catch (e: Exception) {
                                    android.util.Log.e("LocationService", "Error removing location updates", e)
                                }
                            } ?: android.util.Log.d("LocationService", "Single update received null location")
                        }
                    },
                    Looper.getMainLooper()
                )
                android.util.Log.d("LocationService", "Successfully requested single location update")
            }
        } catch (e: Exception) {
            android.util.Log.e("LocationService", "Error in requestSingleUpdate", e)
        }
    }

    fun stopLocationUpdates() {
        stopLocationUpdatesInternal()
    }

    /**
     * Internal method to stop location updates
     */
    private fun stopLocationUpdatesInternal() {
        if (isLocationUpdatesActive) {
            fusedLocationClient.removeLocationUpdates(locationCallback)
            isLocationUpdatesActive = false
            android.util.Log.d("LocationService", "Location updates stopped")
        }
    }

    fun checkLocationPermission(): Boolean {
        return context.checkSelfPermission(Manifest.permission.ACCESS_FINE_LOCATION) ==
                PackageManager.PERMISSION_GRANTED
    }

    internal fun getLastKnownLocation() {
        try {
            if (!checkLocationPermission()) {
                android.util.Log.d("LocationService", "Cannot get last known location - permission denied")
                return
            }

            if (!isLocationEnabled()) {
                android.util.Log.d("LocationService", "Cannot get last known location - location disabled")
                return
            }

            fusedLocationClient.lastLocation
                .addOnSuccessListener { location ->
                    location?.let {
                        android.util.Log.d("LocationService", "Got last location: $it, accuracy: ${it.accuracy}m, time: ${it.time}")
                        // Only use if the location is recent (less than 2 minutes old)
                        val twoMinutesAgo = System.currentTimeMillis() - (2 * 60 * 1000)
                        if (it.time >= twoMinutesAgo && it.accuracy <= LocationConstants.LOCATION_ACCURACY) {
                            currentLocation = it
                            _locationFlow.value = it
                            _locationState.value = LocationState.Available(it)
                            safelyNotifyCallbacks { callback -> callback.onLocationReceived(it) }
                        } else {
                            android.util.Log.d("LocationService", "Last location too old or inaccurate, requesting fresh one")
                            // If last location is too old or inaccurate, request a fresh one
                            try {
                                requestSingleUpdate()
                            } catch (e: Exception) {
                                android.util.Log.e("LocationService", "Error requesting single update", e)
                            }
                        }
                    } ?: run {
                        android.util.Log.d("LocationService", "No last location available, requesting fresh one")
                        // If no last location is available, request a fresh one
                        try {
                            requestSingleUpdate()
                        } catch (e: Exception) {
                            android.util.Log.e("LocationService", "Error requesting single update", e)
                        }
                    }
                }
                .addOnFailureListener { e ->
                    android.util.Log.e("LocationService", "Failed to get last location", e)
                    // If getting last location fails, request a fresh one
                    try {
                        requestSingleUpdate()
                    } catch (e: Exception) {
                        android.util.Log.e("LocationService", "Error requesting single update", e)
                    }
                }
        } catch (e: Exception) {
            android.util.Log.e("LocationService", "Unexpected error in getLastKnownLocation", e)
        }
    }

    /**
     * Calculate distance to a point using route-based calculation when possible
     * This is a suspending function that should be called from a coroutine
     * @param latitude Destination latitude
     * @param longitude Destination longitude
     * @return Distance in meters, or null if calculation failed
     */
    suspend fun calculateRouteDistanceTo(latitude: Double, longitude: Double): Float? {
        return currentLocation?.let { userLocation ->
            com.logikden.eventmanager.utils.DistanceCalculator.calculateDistanceTo(
                context,
                userLocation,
                latitude,
                longitude
            )
        }
    }

    /**
     * Calculate straight-line distance to a point (legacy method)
     * This is a non-suspending function that can be called from anywhere
     * @param latitude Destination latitude
     * @param longitude Destination longitude
     * @return Distance in meters, or null if calculation failed
     */
    fun calculateDistanceTo(latitude: Double, longitude: Double): Float? {
        return currentLocation?.let { userLocation ->
            val eventLocation = Location("").apply {
                this.latitude = latitude
                this.longitude = longitude
            }
            userLocation.distanceTo(eventLocation) // Returns distance in meters
        }
    }

    fun isLocationEnabled(): Boolean {
        return try {
            val locationManager = context.getSystemService(Context.LOCATION_SERVICE) as LocationManager
            val gpsEnabled = locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)
            val networkEnabled = locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)

            android.util.Log.d("LocationService", "Location providers - GPS: $gpsEnabled, Network: $networkEnabled")

            gpsEnabled || networkEnabled
        } catch (e: Exception) {
            android.util.Log.e("LocationService", "Error checking location enabled status", e)
            false
        }
    }

    fun promptEnableLocation(activity: Activity) {
        AlertDialog.Builder(activity)
            .setTitle("Location Services Disabled")
            .setMessage("Please enable location services for better accuracy.")
            .setPositiveButton("Settings") { _, _ ->
                // Open location settings
                activity.startActivity(Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS))
            }
            .setNegativeButton("Cancel", null)
            .show()
    }

    /**
     * Clean up resources when the service is no longer needed
     */
    fun cleanup() {
        stopLocationUpdates()
        activityRecognitionManager?.removeCallback(activityRecognitionCallback)
        activityRecognitionManager?.stopActivityRecognition()
        android.util.Log.d("LocationService", "LocationService cleaned up")
    }

    // LocationState moved to companion object
}
