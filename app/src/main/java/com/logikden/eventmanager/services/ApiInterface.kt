package com.logikden.eventmanager.services
import com.logikden.eventmanager.data.dto.EventReportRequest
import com.logikden.eventmanager.data.dto.EventReportResponse
import com.logikden.eventmanager.data.dto.ReportType
import com.logikden.eventmanager.data.dto.SubscriberResponse
import com.logikden.eventmanager.data.dto.SubscriptionItem
import com.logikden.eventmanager.data.dto.SubscriptionRequest
import retrofit2.Call
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.HTTP
import retrofit2.http.POST
import retrofit2.http.Path

interface ApiService {
    @GET("reporttypes")
    fun getReportTypes(): Call<List<ReportType>>

    @POST("subscriptions")
    suspend fun postSubscriptions(@Body request: List<SubscriptionItem>): Response<Void>

    @GET("subscriptions")
    fun getSubscriptions(): Call<List<SubscriptionItem>>

    @POST("subscribers")
    suspend fun postSubscriber(@Body request: SubscriptionRequest): SubscriberResponse

    @POST("eventreport")
    suspend fun postEventReport(@Body request: EventReportRequest): Response<Void>

    @GET("report/{latitude}/{longitude}/{datetime}")
    suspend fun getEventReports(
        @Path("latitude") latitude: Double,
        @Path("longitude") longitude: Double,
        @Path("datetime") datatime: String
    ): List<EventReportResponse>

    @GET("report/-13.8621746/33.7746431/2025-03-01")
    suspend fun getEventReportsTest(): List<EventReportResponse>

    @DELETE("Subscribers/subscriber/{subscriberId}")
    suspend fun unsubscribesubscriber(@Path("subscriberId") subscriberId: String): Response<Void>

    @HTTP(method = "DELETE", path = "subscriptions", hasBody = true)
    suspend fun unsubscribe(@Body request: List<SubscriptionItem>): Response<Void>
}