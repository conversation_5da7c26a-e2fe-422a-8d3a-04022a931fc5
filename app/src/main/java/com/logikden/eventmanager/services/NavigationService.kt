package com.logikden.eventmanager.services

import android.Manifest
import android.app.*
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Color
import android.location.Location
import android.os.Build
import android.os.IBinder
import android.util.Log
import androidx.core.app.ActivityCompat
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.logikden.eventmanager.MainActivity
import com.logikden.eventmanager.R
import com.logikden.eventmanager.services.routing.RouteInfo
import com.logikden.eventmanager.services.routing.RoutingService
import com.logikden.eventmanager.utils.SettingsManager
import com.logikden.eventmanager.utils.VoiceNavigationManager
import kotlinx.coroutines.*
import com.logikden.eventmanager.utils.LocationConstants
import java.util.concurrent.atomic.AtomicBoolean
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collect

/**
 * Foreground service for navigation that shows a persistent notification
 * with real-time distance updates even when the screen is locked.
 */
class NavigationService : Service() {
    private val TAG = "NavigationService"
    private val NOTIFICATION_ID = 1001
    private val CHANNEL_ID = "navigation_channel"

    private lateinit var locationService: LocationService
    private lateinit var routingService: RoutingService
    private lateinit var settingsManager: SettingsManager
    private var activityRecognitionManager: ActivityRecognitionManager? = null

    private var serviceScope: CoroutineScope? = null
    private val isTracking = AtomicBoolean(false)

    // Navigation data
    private var destinationName: String = ""
    private var destinationLat: Double = 0.0
    private var destinationLng: Double = 0.0
    private var lastLocation: Location? = null
    private var lastRouteInfo: RouteInfo? = null
    private var lastDistanceMeters: Float? = null
    private var batteryOptimizationEnabled: Boolean = true

    // Update interval for the notification
    private val UPDATE_INTERVAL = 1000L // 1 second

    // Distance thresholds for battery optimization
    private val CLOSE_DISTANCE_THRESHOLD = 1000f // 1000 meters
    private val MEDIUM_DISTANCE_THRESHOLD = 3000f // 3 kilometers

    // Location update monitoring
    private var lastLocationUpdateTime: Long = 0
    private val LOCATION_UPDATE_TIMEOUT = 5000L // 5 seconds timeout for location updates
    private var locationUpdateChecker: Job? = null

    // Location updates state
    private val locationUpdatesFlow = MutableStateFlow<Location?>(null)
    private var locationUpdatesJob: Job? = null

    companion object {
        private const val ACTION_START_NAVIGATION = "com.logikden.eventmanager.ACTION_START_NAVIGATION"
        internal const val ACTION_STOP_NAVIGATION = "com.logikden.eventmanager.ACTION_STOP_NAVIGATION"
        private const val ACTION_UPDATE_LOCATION = "com.logikden.eventmanager.ACTION_UPDATE_LOCATION"
        private const val ACTION_TOGGLE_BATTERY_OPTIMIZATION = "com.logikden.eventmanager.ACTION_TOGGLE_BATTERY_OPTIMIZATION"

        private const val EXTRA_DESTINATION_NAME = "destination_name"
        private const val EXTRA_DESTINATION_LAT = "destination_lat"
        private const val EXTRA_DESTINATION_LNG = "destination_lng"
        private const val EXTRA_OPTIMIZATION_ENABLED = "optimization_enabled"

        /**
         * Start navigation to a destination
         */
        fun startNavigation(context: Context, destinationName: String, destinationLat: Double, destinationLng: Double) {
            val intent = Intent(context, NavigationService::class.java).apply {
                action = ACTION_START_NAVIGATION
                putExtra(EXTRA_DESTINATION_NAME, destinationName)
                putExtra(EXTRA_DESTINATION_LAT, destinationLat)
                putExtra(EXTRA_DESTINATION_LNG, destinationLng)
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }

        /**
         * Stop navigation
         */
        fun stopNavigation(context: Context) {
            // Use stopService directly instead of sending an action
            val intent = Intent(context, NavigationService::class.java)
            context.stopService(intent)
        }

        /**
         * Update location for navigation
         */
        fun updateLocation(context: Context, location: Location) {
            val intent = Intent(context, NavigationService::class.java).apply {
                action = ACTION_UPDATE_LOCATION
            }
            context.startService(intent)
        }

        /**
         * Toggle battery optimization for navigation
         * @param context The context
         * @param enabled Whether battery optimization should be enabled
         */
        fun toggleBatteryOptimization(context: Context, enabled: Boolean) {
            val intent = Intent(context, NavigationService::class.java).apply {
                action = ACTION_TOGGLE_BATTERY_OPTIMIZATION
                putExtra(EXTRA_OPTIMIZATION_ENABLED, enabled)
            }
            context.startService(intent)
        }
    }

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "NavigationService created")

        // Initialize services
        locationService = LocationService.getInstance(this)
        routingService = RoutingService.getInstance(this)
        settingsManager = SettingsManager.getInstance(this)

        // Initialize Activity Recognition if enabled
        initializeActivityRecognition()

        // Create notification channel
        createNotificationChannel()

        // Initialize coroutine scope with exception handler
        serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob() +
                CoroutineExceptionHandler { _, exception ->
                    Log.e(TAG, "Coroutine exception: ${exception.message}", exception)
                }
        )
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "onStartCommand: ${intent?.action}")

        // Start foreground immediately to avoid ForegroundServiceDidNotStartInTimeException
        if (intent?.action == ACTION_START_NAVIGATION) {
            // Get the destination name from the intent
            val name = intent.getStringExtra(EXTRA_DESTINATION_NAME) ?: "Destination"

            // Create a simple initial notification with the destination name
            val initialNotification = createNavigationNotification("Calculating...", "Calculating route to $name...")
            startForeground(NOTIFICATION_ID, initialNotification)
            Log.d(TAG, "Started foreground service with initial notification for $name")
        }

        when (intent?.action) {
            ACTION_START_NAVIGATION -> {
                val name = intent.getStringExtra(EXTRA_DESTINATION_NAME) ?: "Destination"
                val lat = intent.getDoubleExtra(EXTRA_DESTINATION_LAT, 0.0)
                val lng = intent.getDoubleExtra(EXTRA_DESTINATION_LNG, 0.0)

                // Now start the actual navigation tracking
                startNavigationTracking(name, lat, lng)
            }
            ACTION_STOP_NAVIGATION -> {
                Log.d(TAG, "Received stop navigation command")
                try {
                    // Simply stop tracking and then stop the service
                    stopNavigationTracking()
                    stopSelf()
                    Log.d(TAG, "Navigation stopped successfully")
                } catch (e: Exception) {
                    Log.e(TAG, "Error stopping navigation: ${e.message}")
                    // Try to stop the service anyway
                    stopSelf()
                }
            }
            ACTION_UPDATE_LOCATION -> {
                // This is handled by the location callback
            }
            ACTION_TOGGLE_BATTERY_OPTIMIZATION -> {
                val enabled = intent.getBooleanExtra(EXTRA_OPTIMIZATION_ENABLED, true)
                setBatteryOptimization(enabled)
            }
        }

        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    override fun onDestroy() {
        Log.d(TAG, "NavigationService onDestroy called")

        try {
            // Make sure tracking is stopped first
            if (isTracking.get()) {
                try {
                    stopNavigationTracking()
                } catch (e: Exception) {
                    Log.e(TAG, "Error stopping navigation tracking during onDestroy: ${e.message}")
                }
            }

            // Cancel all coroutine jobs
            try {
                locationUpdatesJob?.cancel()
                serviceScope?.cancel()
                serviceScope = null
                Log.d(TAG, "Canceled all coroutine jobs")
            } catch (e: Exception) {
                Log.e(TAG, "Error canceling coroutine jobs: ${e.message}")
            }

            // Make sure we remove all callbacks to prevent memory leaks
            try {
                locationService.removeLocationCallback(locationCallback)
                Log.d(TAG, "Removed location callback")
            } catch (e: Exception) {
                Log.e(TAG, "Error removing location callback: ${e.message}")
            }

            // Clean up Activity Recognition
            try {
                activityRecognitionManager?.removeCallback(activityRecognitionCallback)
                Log.d(TAG, "Removed activity recognition callback")
            } catch (e: Exception) {
                Log.e(TAG, "Error removing activity recognition callback: ${e.message}")
            }

            // Try to cancel the notification as a final safety measure
            try {
                val notificationManager = NotificationManagerCompat.from(this)
                notificationManager.cancel(NOTIFICATION_ID)
                Log.d(TAG, "Canceled notification in onDestroy")
            } catch (e: Exception) {
                Log.e(TAG, "Error canceling notification in onDestroy: ${e.message}")
            }

            Log.d(TAG, "NavigationService cleanup completed")
        } catch (e: Exception) {
            Log.e(TAG, "Error during NavigationService onDestroy: ${e.message}")
        } finally {
            super.onDestroy()
        }
    }

    /**
     * Start tracking navigation to a destination
     */
    private fun startNavigationTracking(name: String, lat: Double, lng: Double) {
        if (isTracking.get()) {
            Log.d(TAG, "Already tracking, updating destination")
        }

        // Save destination data
        destinationName = name
        destinationLat = lat
        destinationLng = lng

        // Update the notification with the calculating message
        val initialText = "Calculating..."
        updateNotification(initialText)

        // Set up location tracking
        setupLocationTracking()

        isTracking.set(true)
        Log.d(TAG, "Started navigation tracking to $name ($lat, $lng)")

        // Setup flow collection for location updates
        setupLocationUpdatesFlow()
    }

    /**
     * Setup flow collection for location updates processing
     */
    private fun setupLocationUpdatesFlow() {
        locationUpdatesJob?.cancel()
        locationUpdatesJob = serviceScope?.launch {
            locationUpdatesFlow.collect { location ->
                location?.let {
                    processLocationUpdate(it)
                }
            }
        }
    }

    /**
     * Process location update with throttling
     */
    private suspend fun processLocationUpdate(location: Location) {
        // Use a simple direct distance calculation for quick updates
        val directDistance = calculateDirectDistance(location.latitude, location.longitude, destinationLat, destinationLng)

        // Update notification with direct distance first for responsiveness
        directDistance?.let {
            val formattedDirectDistance = LocationUtils.formatNavigationDistance(it)
            updateNotification(formattedDirectDistance, "Direct distance: $formattedDirectDistance")

            lastDistanceMeters = it
            adjustLocationUpdateFrequency(it)
        }

        // Then calculate route in background
        withContext(Dispatchers.IO) {
            try {
                // Calculate route
                val routeInfo = routingService.calculateRoute(
                    location,
                    destinationLat,
                    destinationLng
                )

                if (!isActive) return@withContext

                lastRouteInfo = routeInfo

                // Update notification with new route info
                if (routeInfo != null) {
                    try {
                        val distanceStr = routeInfo.getFormattedDistance()
                        val durationStr = routeInfo.getFormattedDuration()

                        // Get the distance in meters for battery optimization
                        val distanceMeters = (routeInfo.distanceKm * 1000).toFloat()
                        lastDistanceMeters = distanceMeters

                        // Adjust location update frequency based on distance
                        adjustLocationUpdateFrequency(distanceMeters)

                        // Format the distance for display
                        val formattedDistance = LocationUtils.formatNavigationDistance(distanceMeters)

                        // Update notification with the formatted text
                        updateNotification(
                            formattedDistance,
                            "$formattedDistance\nEstimated time: $durationStr"
                        )

                        // Log if this was a fallback route
                        if (routeInfo.isFallback) {
                            Log.d(TAG, "Using fallback route calculation")
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error processing route info: ${e.message}")
                        // Fall through to the fallback calculation
                    }
                } else {
                    // Fallback to straight-line distance when route calculation fails
                    directDistance?.let { distance ->
                        val formattedDistance = LocationUtils.formatNavigationDistance(distance)
                        updateNotification(formattedDistance)
                    } ?: updateNotification("Calculating...")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error updating navigation", e)
                updateNotification("Navigating to $destinationName")
            }
        }
    }

    /**
     * Stop tracking navigation
     */
    private fun stopNavigationTracking() {
        Log.d(TAG, "Stopping navigation tracking, current tracking state: ${isTracking.get()}")
        if (!isTracking.get()) return

        // Set tracking to false first to prevent any new updates
        isTracking.set(false)

        try {
            // Cancel location updates flow job
            locationUpdatesJob?.cancel()
            Log.d(TAG, "Canceled location updates job")
        } catch (e: Exception) {
            Log.e(TAG, "Error canceling location updates job: ${e.message}")
        }

        try {
            // Stop monitoring location updates
            stopLocationUpdateMonitoring()
            Log.d(TAG, "Stopped location update monitoring")
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping location update monitoring: ${e.message}")
        }

        try {
            // Remove location callback
            locationService.removeLocationCallback(locationCallback)
            Log.d(TAG, "Removed location callback")
        } catch (e: Exception) {
            Log.e(TAG, "Error removing location callback: ${e.message}")
        }

        try {
            // Cancel the notification first
            val notificationManager = NotificationManagerCompat.from(this)
            notificationManager.cancel(NOTIFICATION_ID)
            Log.d(TAG, "Canceled navigation notification")
        } catch (e: Exception) {
            Log.e(TAG, "Error canceling notification: ${e.message}")
        }

        try {
            // Stop foreground service
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                stopForeground(STOP_FOREGROUND_REMOVE)
            } else {
                @Suppress("DEPRECATION")
                stopForeground(true)
            }
            Log.d(TAG, "Stopped foreground service")
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping foreground service: ${e.message}")
        }

        Log.d(TAG, "Stopped navigation tracking successfully")
    }

    /**
     * Initialize Activity Recognition for NavigationService
     */
    private fun initializeActivityRecognition() {
        if (settingsManager.isActivityRecognitionEnabled()) {
            activityRecognitionManager = ActivityRecognitionManager.getInstance(this)
            activityRecognitionManager?.addCallback(activityRecognitionCallback)
            Log.d(TAG, "Activity recognition initialized for NavigationService")
        } else {
            Log.d(TAG, "Activity recognition disabled in settings for NavigationService")
        }
    }

    /**
     * Activity recognition callback for NavigationService
     */
    private val activityRecognitionCallback = object : ActivityRecognitionManager.ActivityRecognitionCallback {
        override fun onDrivingStarted() {
            Log.i(TAG, "Driving detected - using high-frequency navigation updates")
            // When driving is detected, use high-frequency updates regardless of battery optimization
            requestHighFrequencyUpdates()
        }

        override fun onDrivingStopped() {
            Log.i(TAG, "Driving stopped - adjusting navigation update frequency")
            // When driving stops, respect battery optimization settings
            lastDistanceMeters?.let { distance ->
                adjustLocationUpdateFrequency(distance)
            } ?: run {
                if (batteryOptimizationEnabled) {
                    requestMediumFrequencyUpdates()
                } else {
                    requestHighFrequencyUpdates()
                }
            }
        }

        override fun onStationaryDetected() {
            Log.i(TAG, "User stationary during navigation - reducing update frequency")
            // Use lower frequency when stationary
            requestStandardFrequencyUpdates()
        }

        override fun onMovementDetected() {
            Log.i(TAG, "Movement detected during navigation - resuming normal frequency")
            // Resume normal frequency based on distance and settings
            lastDistanceMeters?.let { distance ->
                adjustLocationUpdateFrequency(distance)
            } ?: requestMediumFrequencyUpdates()
        }

        override fun onActivityChanged(activity: com.google.android.gms.location.DetectedActivity) {
            Log.d(TAG, "Activity changed in NavigationService: ${activity.type} (confidence: ${activity.confidence}%)")
        }
    }

    /**
     * Set up location tracking
     */
    private fun setupLocationTracking() {
        // Add location callback
        locationService.addLocationCallback(locationCallback)

        // Request location updates based on Activity Recognition and battery optimization
        if (locationService.checkLocationPermission()) {
            // Check if Activity Recognition is enabled and if we're driving
            val shouldUseHighFrequency = if (settingsManager.isActivityRecognitionEnabled()) {
                activityRecognitionManager?.isDriving() ?: false
            } else {
                !batteryOptimizationEnabled
            }

            if (shouldUseHighFrequency) {
                requestHighFrequencyUpdates()
            } else if (batteryOptimizationEnabled) {
                requestMediumFrequencyUpdates()
            } else {
                requestHighFrequencyUpdates()
            }

            // Start monitoring location updates
            startLocationUpdateMonitoring()
        }
    }

    /**
     * Start monitoring location updates to ensure they're continuous
     */
    private fun startLocationUpdateMonitoring() {
        // Cancel any existing job
        locationUpdateChecker?.cancel()

        // Initialize the last update time
        lastLocationUpdateTime = System.currentTimeMillis()

        // Start a new monitoring job
        locationUpdateChecker = serviceScope?.launch {
            while (isActive && isTracking.get()) {
                // Check if we've received a location update recently
                val timeSinceLastUpdate = System.currentTimeMillis() - lastLocationUpdateTime

                if (timeSinceLastUpdate > LOCATION_UPDATE_TIMEOUT) {
                    Log.w(TAG, "No location updates received for ${timeSinceLastUpdate/1000} seconds, attempting to restart")

                    // Try to restart location updates
                    if (locationService.checkLocationPermission()) {
                        // First check if location is enabled
                        if (locationService.isLocationEnabled()) {
                            // Try to restart with appropriate frequency
                            if (batteryOptimizationEnabled) {
                                requestMediumFrequencyUpdates()
                            } else {
                                requestHighFrequencyUpdates()
                            }

                            Log.d(TAG, "Restarted location updates")
                            updateNotification("Reconnecting...", "Reconnecting to location services...")
                        } else {
                            Log.w(TAG, "Location services are disabled")
                            updateNotification("Location disabled", "Please enable location services to continue navigation")
                        }
                    } else {
                        Log.w(TAG, "Location permission denied")
                        updateNotification("Permission required", "Location permission is needed for navigation")
                    }
                }

                // Check every second
                delay(1000)
            }
        }
    }

    /**
     * Stop monitoring location updates
     */
    private fun stopLocationUpdateMonitoring() {
        locationUpdateChecker?.cancel()
        locationUpdateChecker = null
    }

    /**
     * Request high-frequency location updates
     */
    private fun requestHighFrequencyUpdates() {
        if (locationService.checkLocationPermission()) {
            locationService.requestLocationUpdates(
                LocationConstants.NAVIGATION_UPDATE_INTERVAL,
                LocationConstants.NAVIGATION_FASTEST_INTERVAL,
                LocationConstants.NAVIGATION_MIN_DISTANCE_CHANGE
            )
            Log.d(TAG, "Requested high-frequency location updates for navigation")
        }
    }

    /**
     * Request medium-frequency location updates (battery saving)
     */
    private fun requestMediumFrequencyUpdates() {
        if (locationService.checkLocationPermission()) {
            locationService.requestLocationUpdates(
                LocationConstants.BATTERY_SAVING_UPDATE_INTERVAL,
                LocationConstants.BATTERY_SAVING_FASTEST_INTERVAL,
                LocationConstants.BATTERY_SAVING_MIN_DISTANCE_CHANGE
            )
            Log.d(TAG, "Requested medium-frequency location updates for battery saving")
        }
    }

    /**
     * Request standard-frequency location updates (more battery saving)
     */
    private fun requestStandardFrequencyUpdates() {
        if (locationService.checkLocationPermission()) {
            locationService.requestLocationUpdates(
                LocationConstants.UPDATE_INTERVAL,
                LocationConstants.FASTEST_INTERVAL,
                LocationConstants.MIN_DISTANCE_CHANGE
            )
            Log.d(TAG, "Requested standard-frequency location updates for maximum battery saving")
        }
    }

    /**
     * Set battery optimization state
     * @param enabled Whether battery optimization should be enabled
     */
    private fun setBatteryOptimization(enabled: Boolean) {
        batteryOptimizationEnabled = enabled
        Log.d(TAG, "Battery optimization ${if (enabled) "enabled" else "disabled"}")

        // Adjust the update frequency immediately based on the last known distance
        lastDistanceMeters?.let { distance ->
            adjustLocationUpdateFrequency(distance)
        } ?: run {
            if (enabled) {
                requestMediumFrequencyUpdates()
            } else {
                requestHighFrequencyUpdates()
            }
        }

        // Get current notification text
        val notificationText = if (lastRouteInfo != null) {
            lastRouteInfo!!.getFormattedDistance()
        } else {
            lastDistanceMeters?.let {
                LocationUtils.formatNavigationDistance(it)
            } ?: "Approaching $destinationName"
        }

        // Add battery optimization info
        val optimizationNote = if (enabled) {
            "Battery optimization enabled"
        } else {
            "Real-time updates enabled (higher battery usage)"
        }

        // Update notification
        updateNotification(notificationText, "$notificationText\n$optimizationNote")
    }

    /**
     * Adjust location update frequency based on distance to destination and Activity Recognition
     */
    private fun adjustLocationUpdateFrequency(distanceMeters: Float) {
        // If Activity Recognition is enabled and we're driving, always use high frequency
        if (settingsManager.isActivityRecognitionEnabled() && activityRecognitionManager?.isDriving() == true) {
            requestHighFrequencyUpdates()
            Log.d(TAG, "Using high-frequency updates (driving detected)")
            return
        }

        // If battery optimization is disabled, use high frequency
        if (!batteryOptimizationEnabled) {
            requestHighFrequencyUpdates()
            Log.d(TAG, "Using high-frequency updates (battery optimization disabled)")
            return
        }

        // Adjust update frequency based on distance to destination
        when {
            distanceMeters < CLOSE_DISTANCE_THRESHOLD -> {
                requestHighFrequencyUpdates()
                Log.d(TAG, "Using high-frequency updates (close to destination: ${distanceMeters}m)")
            }
            distanceMeters < MEDIUM_DISTANCE_THRESHOLD -> {
                requestMediumFrequencyUpdates()
                Log.d(TAG, "Using medium-frequency updates (medium distance: ${distanceMeters}m)")
            }
            else -> {
                requestStandardFrequencyUpdates()
                Log.d(TAG, "Using standard-frequency updates (far from destination: ${distanceMeters}m)")
            }
        }
    }

    /**
     * Location callback to update navigation
     */
    private val locationCallback = object : LocationService.LocationUpdateCallback {
        override fun onLocationReceived(location: Location) {
            // Update the last location update time
            lastLocationUpdateTime = System.currentTimeMillis()

            // Process the location update
            if (location.accuracy <= LocationConstants.LOCATION_ACCURACY) {
                lastLocation = location

                // Use flow for location updates
                locationUpdatesFlow.value = location

                Log.d(TAG, "Received location update: ${location.latitude}, ${location.longitude}, accuracy: ${location.accuracy}m")
            } else {
                Log.d(TAG, "Received location update but accuracy too low: ${location.accuracy}m > ${LocationConstants.LOCATION_ACCURACY}m")

                // Use low accuracy location if no better data is available
                if (lastLocation == null) {
                    lastLocation = location
                    locationUpdatesFlow.value = location
                    Log.d(TAG, "Using low accuracy location as fallback")
                }
            }
        }

        override fun onLocationPermissionDenied() {
            Log.d(TAG, "Location permission denied")
            updateNotification("Permission required", "Location permission is needed for navigation")
        }

        override fun onLocationDisabled() {
            Log.d(TAG, "Location disabled")
            updateNotification("Location disabled", "Please enable location services to continue navigation")
        }
    }

    /**
     * Calculate direct (straight-line) distance between two points
     */
    private fun calculateDirectDistance(lat1: Double, lon1: Double, lat2: Double, lon2: Double): Float? {
        try {
            val results = FloatArray(1)
            Location.distanceBetween(lat1, lon1, lat2, lon2, results)
            return results[0] // Distance in meters
        } catch (e: Exception) {
            Log.e(TAG, "Error calculating direct distance", e)
            return null
        }
    }

    /**
     * Create notification channel for Android O and above
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val name = "Navigation"
            val descriptionText = "Real-time navigation updates"
            val importance = NotificationManager.IMPORTANCE_HIGH
            val channel = NotificationChannel(CHANNEL_ID, name, importance).apply {
                description = descriptionText
                enableLights(true)
                lightColor = Color.BLUE
                enableVibration(true)
                vibrationPattern = longArrayOf(0, 250, 250, 250) // Vibration pattern similar to Google Maps
                setShowBadge(true)
                lockscreenVisibility = Notification.VISIBILITY_PUBLIC // Ensure full visibility on lock screen
                setBypassDnd(true) // Allow notification to bypass Do Not Disturb mode
            }

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
            Log.d(TAG, "Created high-visibility navigation notification channel")
        }
    }

    /**
     * Create navigation notification
     * @param contentText The main content text for the notification
     * @param bigText Optional expanded text for the notification
     */
    private fun createNavigationNotification(contentText: String, bigText: String? = null): Notification {
        // Create an intent to open the app when notification is tapped
        val contentIntent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        val contentPendingIntent = PendingIntent.getActivity(
            this,
            0,
            contentIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // Create an intent to stop navigation
        val stopIntent = Intent(this, NavigationService::class.java).apply {
            action = ACTION_STOP_NAVIGATION
        }

        // Use getForegroundService for Android 12+ compatibility
        val stopPendingIntent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            PendingIntent.getForegroundService(
                this,
                0,
                stopIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
        } else {
            PendingIntent.getService(
                this,
                0,
                stopIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
        }

        // Build the notification with enhanced styling for lock screen visibility
        val builder = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(destinationName)
            .setContentText(contentText)
            .setSmallIcon(R.drawable.ic_navigation)
            .setContentIntent(contentPendingIntent)
            .addAction(R.drawable.ic_close, "Exit Navigation", stopPendingIntent)
            .setOngoing(true)
            .setCategory(NotificationCompat.CATEGORY_NAVIGATION)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
            .setDefaults(NotificationCompat.DEFAULT_ALL)

        // Use BigTextStyle for expanded notification
        if (bigText != null) {
            val bigTextStyle = NotificationCompat.BigTextStyle()
                .setBigContentTitle(contentText)
                .bigText(bigText)
                .setSummaryText(destinationName)
            builder.setStyle(bigTextStyle)
        } else {
            val bigTextStyle = NotificationCompat.BigTextStyle()
                .setBigContentTitle(contentText)
                .setSummaryText(destinationName)
            builder.setStyle(bigTextStyle)
        }

        return builder.build()
    }

    /**
     * Update the notification with new content
     * @param contentText The main content text for the notification
     * @param bigText Optional expanded text for the notification
     */
    private fun updateNotification(contentText: String, bigText: String? = null) {
        // Don't update notification if we're not tracking anymore
        if (!isTracking.get()) {
            Log.d(TAG, "Skipping notification update because tracking is stopped")
            return
        }

        try {
            val notification = createNavigationNotification(contentText, bigText)

            // Check if we have notification permission
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                if (ActivityCompat.checkSelfPermission(
                        this,
                        Manifest.permission.POST_NOTIFICATIONS
                    ) != PackageManager.PERMISSION_GRANTED
                ) {
                    Log.d(TAG, "Cannot update notification - permission not granted")
                    return
                }
            }

            // Update the notification with a try-catch block to handle SecurityExceptions
            try {
                val notificationManager = NotificationManagerCompat.from(this)
                notificationManager.notify(NOTIFICATION_ID, notification)
                Log.d(TAG, "Updated notification: $contentText")
            } catch (se: SecurityException) {
                // Handle security exceptions specifically (common on some devices)
                Log.e(TAG, "Security exception when updating notification: ${se.message}")
            } catch (e: Exception) {
                Log.e(TAG, "Error updating notification: ${e.message}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error creating notification", e)
        }
    }
}
