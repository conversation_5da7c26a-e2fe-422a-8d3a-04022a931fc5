package com.logikden.eventmanager.services

import android.content.Context
import android.location.Location
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import android.util.Log
import androidx.annotation.RequiresApi
import androidx.work.*
import com.logikden.eventmanager.data.AppDatabase
import com.logikden.eventmanager.services.RetrofitClient
import com.logikden.eventmanager.ui.events.RemoteEventRepository
import com.logikden.eventmanager.utils.SettingsManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import android.app.Application
import java.util.concurrent.TimeUnit

/**
 * WorkManager worker for background data synchronization
 * Fetches reports from server when app is closed
 */
class DataSyncWorker(
    context: Context,
    workerParams: WorkerParameters
) : CoroutineWorker(context, workerParams) {

    companion object {
        private const val TAG = "DataSyncWorker"
        private const val WORK_NAME = "DataSyncWork"
        private const val UNIQUE_WORK_NAME = "BackgroundDataSync"

        /**
         * Start periodic background data sync
         */
        fun startPeriodicSync(context: Context) {
            val settingsManager = SettingsManager.getInstance(context)

            // Only start if background sync is enabled
            if (!settingsManager.isBackgroundSyncEnabled()) {
                Log.d(TAG, "Background sync is disabled, not starting worker")
                return
            }

            val syncInterval = settingsManager.getBackgroundSyncInterval().toLong()
            val isWifiOnly = settingsManager.isWifiOnlySyncEnabled()
            val isBatteryOptimized = settingsManager.isBatteryOptimizationEnabled()

            val constraints = Constraints.Builder().apply {
                // Network constraints
                if (isWifiOnly) {
                    setRequiredNetworkType(NetworkType.UNMETERED) // WiFi only
                } else {
                    setRequiredNetworkType(NetworkType.CONNECTED) // Any network
                }

                // Battery constraints
                if (isBatteryOptimized) {
                    setRequiresBatteryNotLow(true)
                    setRequiresCharging(false)
                }

                // Device constraints
                setRequiresDeviceIdle(false)
                setRequiresStorageNotLow(true)
            }.build()

            val workRequest = PeriodicWorkRequestBuilder<DataSyncWorker>(
                syncInterval, TimeUnit.MINUTES,
                5, TimeUnit.MINUTES // Flex interval
            )
                .setConstraints(constraints)
                .addTag(WORK_NAME)
                .setBackoffCriteria(
                    BackoffPolicy.EXPONENTIAL,
                    WorkRequest.MIN_BACKOFF_MILLIS,
                    TimeUnit.MILLISECONDS
                )
                .build()

            WorkManager.getInstance(context)
                .enqueueUniquePeriodicWork(
                    UNIQUE_WORK_NAME,
                    ExistingPeriodicWorkPolicy.UPDATE,
                    workRequest
                )

            Log.d(TAG, "Started periodic data sync with interval: ${syncInterval} minutes, WiFi only: $isWifiOnly")
        }

        /**
         * Stop background data sync
         */
        fun stopPeriodicSync(context: Context) {
            WorkManager.getInstance(context).cancelUniqueWork(UNIQUE_WORK_NAME)
            Log.d(TAG, "Stopped periodic data sync")
        }

        /**
         * Trigger immediate one-time sync
         */
        fun triggerImmediateSync(context: Context) {
            val settingsManager = SettingsManager.getInstance(context)

            if (!settingsManager.isBackgroundSyncEnabled()) {
                Log.d(TAG, "Background sync is disabled, not triggering immediate sync")
                return
            }

            val constraints = Constraints.Builder()
                .setRequiredNetworkType(NetworkType.CONNECTED)
                .build()

            val workRequest = OneTimeWorkRequestBuilder<DataSyncWorker>()
                .setConstraints(constraints)
                .addTag("${WORK_NAME}_immediate")
                .build()

            WorkManager.getInstance(context).enqueue(workRequest)
            Log.d(TAG, "Triggered immediate data sync")
        }
    }

    @RequiresApi(Build.VERSION_CODES.O)
    override suspend fun doWork(): Result = withContext(Dispatchers.IO) {
        Log.d(TAG, "Starting background data sync")

        return@withContext try {
            val settingsManager = SettingsManager.getInstance(applicationContext)

            // Check if background sync is still enabled
            if (!settingsManager.isBackgroundSyncEnabled()) {
                Log.d(TAG, "Background sync disabled, stopping work")
                return@withContext Result.success()
            }

            // Check network availability
            if (!isNetworkAvailable()) {
                Log.d(TAG, "Network not available, retrying later")
                return@withContext Result.retry()
            }

            // Check WiFi requirement
            if (settingsManager.isWifiOnlySyncEnabled() && !isWifiConnected()) {
                Log.d(TAG, "WiFi required but not connected, retrying later")
                return@withContext Result.retry()
            }

            // Get location for sync
            val location = getCachedOrLastKnownLocation()
            if (location == null) {
                Log.d(TAG, "No location available for sync, retrying later")
                return@withContext Result.retry()
            }

            // Perform the actual sync
            val syncResult = performDataSync(location)

            if (syncResult) {
                Log.d(TAG, "Background data sync completed successfully")
                Result.success()
            } else {
                Log.d(TAG, "Background data sync failed, will retry")
                Result.retry()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error during background data sync", e)
            Result.failure()
        }
    }

    /**
     * Check if network is available
     */
    private fun isNetworkAvailable(): Boolean {
        val connectivityManager = applicationContext.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            capabilities?.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) == true
        } else {
            @Suppress("DEPRECATION")
            connectivityManager.activeNetworkInfo?.isConnected == true
        }
    }

    /**
     * Check if WiFi is connected
     */
    private fun isWifiConnected(): Boolean {
        val connectivityManager = applicationContext.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) == true
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo
            networkInfo?.type == ConnectivityManager.TYPE_WIFI && networkInfo.isConnected
        }
    }

    /**
     * Get cached location or attempt to get last known location
     */
    private fun getCachedOrLastKnownLocation(): Location? {
        val settingsManager = SettingsManager.getInstance(applicationContext)

        // Try cached location first
        if (settingsManager.hasValidCachedLocation()) {
            val lat = settingsManager.getLastCachedLatitude()
            val lng = settingsManager.getLastCachedLongitude()

            return Location("cached").apply {
                latitude = lat
                longitude = lng
            }
        }

        // If no valid cached location, we can't sync in background
        // The location will be updated when the app is opened
        Log.d(TAG, "No valid cached location available")
        return null
    }

    /**
     * Perform the actual data synchronization
     */
    @RequiresApi(Build.VERSION_CODES.O)
    private suspend fun performDataSync(location: Location): Boolean {
        return try {
            // Create a coroutine scope for database operations
            val databaseScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

            // Get database and API service
            val database = AppDatabase.getDatabase(applicationContext, databaseScope)
            val apiService = RetrofitClient.instance

            // Create repository instance
            val repository = RemoteEventRepository(
                database.remoteEventReportDao(),
                apiService,
                applicationContext as Application
            )

            // Set location and perform sync
            repository.setCurrentLocation(location)

            val result = repository.refreshReports()
            result.isSuccess
        } catch (e: Exception) {
            Log.e(TAG, "Error performing data sync", e)
            false
        }
    }
}
