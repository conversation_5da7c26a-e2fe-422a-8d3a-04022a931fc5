package com.logikden.eventmanager.services

import android.app.Activity
import android.app.Application
import android.content.Intent
import android.net.Uri
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.widget.Button
import androidx.appcompat.app.AlertDialog
import com.logikden.eventmanager.R
import okhttp3.Interceptor
import okhttp3.Response
import java.io.IOException

/**
 * Interceptor to handle authentication errors (401 Unauthorized)
 * Shows a dialog prompting the user to update the app when the API returns a 401 response
 */
class AuthInterceptor(private val application: Application) : Interceptor {

    companion object {
        private const val TAG = "AuthInterceptor"
        private var isDialogShowing = false

        // Google Play Store URL for the app
        private const val PLAY_STORE_URL = "market://details?id=com.logikden.eventmanager"
        private const val PLAY_STORE_FALLBACK_URL = "https://play.google.com/store/apps/details?id=com.logikden.eventmanager"
    }

    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val response = chain.proceed(request)

        // Check if the response code is 401 Unauthorized
        if (response.code == 401) {
            Log.e(TAG, "Received 401 Unauthorized response from the server")

            // Show the update dialog on the main thread
            if (!isDialogShowing) {
                Handler(Looper.getMainLooper()).post {
                    showUpdateDialog()
                }
            }
        }

        return response
    }

    /**
     * Shows a dialog prompting the user to update the app
     */
    private fun showUpdateDialog() {
        try {
            // Prevent multiple dialogs from showing
            if (isDialogShowing) return
            isDialogShowing = true

            // Find the current activity to show the dialog
            val currentActivity = findCurrentActivity()
            if (currentActivity == null) {
                Log.e(TAG, "No activity found to show dialog")
                isDialogShowing = false
                return
            }

            // Create and show the custom dialog
            val builder = AlertDialog.Builder(currentActivity, R.style.AlertDialogTheme)
                .setCancelable(false)

            // Inflate the custom layout
            val inflater = currentActivity.layoutInflater
            val dialogView = inflater.inflate(R.layout.dialog_update_required, null)
            builder.setView(dialogView)

            val dialog = builder.create()

            // Set up the update button click listener
            dialogView.findViewById<Button>(R.id.btnUpdate).setOnClickListener {
                // Open Google Play Store
                openPlayStore()
                dialog.dismiss()
                isDialogShowing = false
            }

            dialog.show()
        } catch (e: Exception) {
            Log.e(TAG, "Error showing update dialog: ${e.message}")
            isDialogShowing = false
        }
    }

    /**
     * Find the current activity to show the dialog
     */
    private fun findCurrentActivity(): Activity? {
        try {
            // Get the Activity Thread's current application
            val activityThreadClass = Class.forName("android.app.ActivityThread")
            val activityThread = activityThreadClass.getMethod("currentActivityThread").invoke(null)
            val activitiesField = activityThreadClass.getDeclaredField("mActivities")
            activitiesField.isAccessible = true

            // The field is a Map of IBinder to ActivityClientRecord
            val activities = activitiesField.get(activityThread) as Map<*, *>

            // Find the current resumed activity
            for (activityRecord in activities.values) {
                val activityRecordClass = activityRecord!!.javaClass
                val pausedField = activityRecordClass.getDeclaredField("paused")
                pausedField.isAccessible = true
                if (!pausedField.getBoolean(activityRecord)) {
                    val activityField = activityRecordClass.getDeclaredField("activity")
                    activityField.isAccessible = true
                    return activityField.get(activityRecord) as Activity
                }
            }

            return null
        } catch (e: Exception) {
            Log.e(TAG, "Error finding current activity: ${e.message}")
            return null
        }
    }

    /**
     * Opens the Google Play Store to update the app
     */
    private fun openPlayStore() {
        try {
            // Try to open the Play Store app first
            val intent = Intent(Intent.ACTION_VIEW, Uri.parse(PLAY_STORE_URL))
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            application.startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "Error opening Play Store app: ${e.message}")

            try {
                // Fallback to browser if Play Store app is not available
                val fallbackIntent = Intent(Intent.ACTION_VIEW, Uri.parse(PLAY_STORE_FALLBACK_URL))
                fallbackIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                application.startActivity(fallbackIntent)
            } catch (e: Exception) {
                Log.e(TAG, "Error opening Play Store in browser: ${e.message}")
            }
        }
    }
}
