package com.logikden.eventmanager.services

import android.content.Context
import android.os.Build
import android.util.Log
import androidx.annotation.RequiresApi
import androidx.work.*
import com.logikden.eventmanager.utils.SettingsManager
import java.util.concurrent.TimeUnit

class ProximityWorker(
    context: Context,
    workerParams: WorkerParameters
) : CoroutineWorker(context, workerParams) {

    @RequiresApi(Build.VERSION_CODES.O)
    override suspend fun doWork(): Result {
        Log.d(TAG, "ProximityWorker starting work")

        return try {
            val settingsManager = SettingsManager.getInstance(applicationContext)

            // Start proximity service
            ProximityService.startService(applicationContext)

            // Trigger data sync if background sync is enabled
            if (settingsManager.isBackgroundSyncEnabled()) {
                Log.d(TAG, "Triggering background data sync")
                DataSyncWorker.triggerImmediateSync(applicationContext)
            }

            Log.d(TAG, "ProximityWorker completed successfully")
            Result.success()
        } catch (e: Exception) {
            Log.e(TAG, "ProximityWorker failed", e)
            Result.retry()
        }
    }

    companion object {
        private const val TAG = "ProximityWorker"
        private const val WORK_NAME = "ProximityWork"

        fun startPeriodicWork(context: Context) {
            val constraints = Constraints.Builder()
                .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
                .setRequiresBatteryNotLow(true)
                .build()

            val workRequest = PeriodicWorkRequestBuilder<ProximityWorker>(
                15, TimeUnit.MINUTES,
                5, TimeUnit.MINUTES
            )
                .setConstraints(constraints)
                .addTag(WORK_NAME)
                .build()

            WorkManager.getInstance(context)
                .enqueueUniquePeriodicWork(
                    WORK_NAME,
                    ExistingPeriodicWorkPolicy.UPDATE,
                    workRequest
                )
        }

        fun stopWork(context: Context) {
            WorkManager.getInstance(context).cancelUniqueWork(WORK_NAME)
        }
    }
}