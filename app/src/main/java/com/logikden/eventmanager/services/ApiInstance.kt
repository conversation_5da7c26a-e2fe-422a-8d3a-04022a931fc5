package com.logikden.eventmanager.services

import android.app.Application
import android.content.Context
import android.util.Log
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.Response
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

object RetrofitClient {
    private const val BASE_URL = "http://109.75.164.217:83/api/"

    private var app: Application? = null

    // Initialize the RetrofitClient with the application context
    fun init(application: Application) {
        app = application
    }

    // Lazily initialize the Retrofit instance
    val instance: ApiService by lazy {
        if (app == null) {
            throw IllegalStateException("RetrofitClient not initialized. Call init(application) first.")
        }

        val retrofit = Retrofit.Builder()
            .baseUrl(BASE_URL)
            .client(createOkHttpClient()) // Use a function to create the client
            .addConverterFactory(GsonConverterFactory.create())
            .build()

        retrofit.create(ApiService::class.java)
    }

    private fun createOkHttpClient(): OkHttpClient {
        // Create logging interceptor
        val loggingInterceptor = HttpLoggingInterceptor().apply {
            level = HttpLoggingInterceptor.Level.BODY
        }

        return OkHttpClient.Builder()
            .addInterceptor(HeaderInterceptor(context = app!!.applicationContext))
            .addInterceptor(AuthInterceptor(app!!)) // Add the auth interceptor to handle 401 responses
            .addInterceptor(loggingInterceptor) // Add the logging interceptor
            .connectTimeout(30, TimeUnit.SECONDS) // Optional: Set timeouts
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build()
    }

    // Function to get subscriber ID from secure storage
    fun getSubscriberId(context: Context): String? {
        return try {
            val masterKey = MasterKey.Builder(context)
                .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
                .build()

            val sharedPreferences = EncryptedSharedPreferences.create(
                context,
                "secure_prefs",
                masterKey,
                EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
                EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
            )

            sharedPreferences.getString("subscriber_id", null)
        } catch (e: Exception) {
            Log.e("RetrofitClient", "Error retrieving subscriber ID: ${e.message}")
            null
        }
    }
}

// Interceptor to add headers to requests
class HeaderInterceptor(private val context: Context) : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()

        // Get the subscriber ID dynamically
        val subscriberId = RetrofitClient.getSubscriberId(context)

        val sharedPreferences = context.getSharedPreferences("event_manager_prefs", Context.MODE_PRIVATE)

        val updateRadius = sharedPreferences.getInt("update_radius", 10)

        // Add headers to the request
        val requestWithHeaders = originalRequest.newBuilder()
            .header("X-AppKey", "WERTY-UIOPA-SDFGH-JKLZX-CVBNM-ASDFG-HYTRD")
            .header("X-SubscriberKey", subscriberId ?: "")
            .header("X-Radius", updateRadius.toString())
            .method(originalRequest.method, originalRequest.body)
            .build()

        return chain.proceed(requestWithHeaders)
    }
}