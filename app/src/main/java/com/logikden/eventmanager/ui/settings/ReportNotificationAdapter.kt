package com.logikden.eventmanager.ui.settings

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.widget.SwitchCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.logikden.eventmanager.R
import com.logikden.eventmanager.data.Tile
import com.logikden.eventmanager.utils.SettingsManager

class ReportNotificationAdapter : ListAdapter<Tile, ReportNotificationAdapter.ViewHolder> {
    private val onToggleChanged: (Tile, Boolean) -> Unit
    private val context: Context?

    constructor(context: Context, onToggleChanged: (Tile, Boolean) -> Unit) : super(TileDiffCallback()) {
        this.context = context
        this.onToggleChanged = onToggleChanged
    }

    constructor(onToggleChanged: (Tile, Boolean) -> Unit) : super(TileDiffCallback()) {
        this.context = null
        this.onToggleChanged = onToggleChanged
    }

    private lateinit var settingsManager: SettingsManager

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        // Initialize settingsManager if not already initialized
        if (!::settingsManager.isInitialized) {
            // Use context from constructor if available, otherwise use parent context
            val contextToUse = context ?: parent.context
            settingsManager = SettingsManager.getInstance(contextToUse)
        }

        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_report_notification_toggle, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val tile = getItem(position)

        // Set report type name
        holder.reportTypeName?.text = tile.title

        // Set icon based on iconName
        val iconResId = getIconResourceId(tile.iconName)
        holder.reportTypeIcon?.setImageResource(iconResId)

        // Apply a color tint to make the icon visible
        val context = holder.itemView.context
        holder.reportTypeIcon?.setColorFilter(context.getColor(R.color.zatani_primary))

        // Set toggle state based on saved preference and entity property
        // First check the entity property, then fall back to the settings manager
        val isEnabled = tile.notificationsEnabled
        holder.notificationToggle?.isChecked = isEnabled

        // Set toggle listener
        holder.notificationToggle?.setOnCheckedChangeListener { _, isChecked ->
            onToggleChanged(tile, isChecked)
        }
    }

    private fun getIconResourceId(iconName: String): Int {
        return when (iconName) {
            "ic_power_out" -> R.drawable.ic_power_out
            "ic_road_blocked" -> R.drawable.ic_road_blocked
            "ic_car_crash" -> R.drawable.ic_car_crash
            "ic_warning" -> R.drawable.ic_warning
            "ic_water_outage" -> R.drawable.ic_water_outage
            else -> R.drawable.ic_default
        }
    }

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val reportTypeIcon: ImageView? = itemView.findViewById(R.id.reportTypeIcon)
        val reportTypeName: TextView? = itemView.findViewById(R.id.reportTypeName)
        val notificationToggle: SwitchCompat? = itemView.findViewById(R.id.notificationToggle)
    }

    class TileDiffCallback : DiffUtil.ItemCallback<Tile>() {
        override fun areItemsTheSame(oldItem: Tile, newItem: Tile): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Tile, newItem: Tile): Boolean {
            return oldItem == newItem
        }
    }
}
