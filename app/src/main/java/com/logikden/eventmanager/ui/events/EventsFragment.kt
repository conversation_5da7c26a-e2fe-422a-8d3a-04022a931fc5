package com.logikden.eventmanager.ui.events

import kotlinx.coroutines.delay
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Context.RECEIVER_NOT_EXPORTED
import android.content.Intent
import android.content.IntentFilter
import android.location.Location
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.TextView
import android.widget.Toast
import androidx.annotation.RequiresApi
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.logikden.eventmanager.R
import com.logikden.eventmanager.data.AppDatabase
import com.logikden.eventmanager.data.EventRemoteReport
import com.logikden.eventmanager.services.LocationService
import com.logikden.eventmanager.services.RetrofitClient
import kotlinx.coroutines.launch
import com.logikden.eventmanager.utils.LocationState
import com.logikden.eventmanager.utils.Resource
import com.logikden.eventmanager.data.dto.GroupedEvent
import com.logikden.eventmanager.utils.LocationConstants
import com.logikden.eventmanager.utils.SettingsManager
import com.logikden.eventmanager.utils.EventsVoiceAnnouncementManager
import com.logikden.eventmanager.ui.debug.SpeedDebugView
import com.logikden.eventmanager.utils.WakeLockManager
import org.osmdroid.library.BuildConfig


class EventsFragment : Fragment(R.layout.fragment_events) {
    // Argument for report type to expand
    private var expandReportType: String? = null
    private lateinit var recyclerView: RecyclerView
    private lateinit var eventTypeAdapter: EventTypeAdapter
    private lateinit var viewModel: RemoteEventViewModel
    private lateinit var locationService: LocationService
    private lateinit var eventRepository: RemoteEventRepository
    private lateinit var settingsManager: SettingsManager
    private lateinit var eventsVoiceAnnouncementManager: EventsVoiceAnnouncementManager

    private val apiService = RetrofitClient.instance
    private var currentLocation: Location? = null

    private lateinit var progressBar: View
    private lateinit var waitingForLocationView: View
    private lateinit var offlineView: View
    private lateinit var offlineMessage: View
    private lateinit var refreshButton: View
    private lateinit var speedDebugView: SpeedDebugView

    // Settings broadcast receiver
    private var settingsReceiver: BroadcastReceiver? = null

    // Move locationCallback initialization after repository setup
    private val locationCallback by lazy {
        object : LocationService.LocationUpdateCallback {
            @RequiresApi(Build.VERSION_CODES.O)
            override fun onLocationReceived(location: Location) {
                currentLocation = location
                eventRepository.setCurrentLocation(location)

                // Update voice announcements with new location
                eventsVoiceAnnouncementManager.updateLocation(location)

                // Immediately update all distances when a new location is received
                // This ensures real-time distance updates in the UI
                viewModel.updateAllDistances()
                //Log.d("EventsFragment", "Real-time location update received: ${location.latitude}, ${location.longitude}")
            }

            override fun onLocationPermissionDenied() {
                showToast("Location permissions are required")
            }

            override fun onLocationDisabled() {
                //showToast("GPS is disabled")
            }
        }
    }

    private val locationPermissionRequest by lazy {
        LocationService.createLocationPermissionRequest(this) { granted ->
            if (granted) {
                // Use high-frequency updates for real-time distance tracking
                locationService.requestLocationUpdates(
                    LocationConstants.NAVIGATION_UPDATE_INTERVAL,
                    LocationConstants.NAVIGATION_FASTEST_INTERVAL,
                    LocationConstants.NAVIGATION_MIN_DISTANCE_CHANGE
                )
                //Log.d("EventsFragment", "Requested high-frequency location updates after permission granted")
            } else {
                showToast("Location permissions are required")
            }
        }
    }

    @RequiresApi(Build.VERSION_CODES.O)
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Get arguments
        arguments?.let {
            expandReportType = it.getString("expandReportType")
            //Log.d("EventsFragment", "Received expandReportType: $expandReportType")
        }

        // Initialize views first
        progressBar = view.findViewById(R.id.progressBar)

        // Initialize all status views
        waitingForLocationView = view.findViewById(R.id.waitingForLocationView)
        offlineView = view.findViewById(R.id.offlineView)
        offlineMessage = view.findViewById(R.id.offlineMessage)
        refreshButton = view.findViewById(R.id.refreshButton)
        speedDebugView = view.findViewById(R.id.speedDebugView)

        // Initialize debug view (show only in debug builds)
        speedDebugView.setDebugVisible(BuildConfig.DEBUG)
        if (BuildConfig.DEBUG) {
            speedDebugView.startObserving(viewLifecycleOwner)
        }

        // Set up refresh button click listener
        refreshButton.setOnClickListener {
            refreshEvents()
        }

        // Setup dependencies first
        setupDependencies()

        // Then setup other components
        setupViewModel()
        setupRecyclerView(view)
        setupLocationService()

        // Now observe ViewModel states
        lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                when (state) {
                    is Resource.Loading -> showLoading()
                    is Resource.Success -> showEvents(state.data)
                    is Resource.Error -> showError(state.message)
                }
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.locationState.collect { state ->
                when (state) {
                    is LocationState.Available -> handleLocationAvailable(state.location)
                    LocationState.Unavailable -> handleLocationUnavailable()
                }
            }
        }

        // Request permissions last
        requestLocationPermissions()
    }

    private fun setupDependencies() {
        val database = AppDatabase.getDatabase(requireContext(), lifecycleScope)
        eventRepository = RemoteEventRepository(
            database.remoteEventReportDao(),
            apiService,
            requireActivity().application
        )

        // Initialize SettingsManager
        settingsManager = SettingsManager.getInstance(requireContext())

        // Initialize EventsVoiceAnnouncementManager
        eventsVoiceAnnouncementManager = EventsVoiceAnnouncementManager.getInstance(requireContext())

        // Register for settings changes
        registerSettingsReceiver()
    }

    private fun registerSettingsReceiver() {
        // Create and register broadcast receiver for settings changes
        if (settingsReceiver == null) {
            settingsReceiver = object : BroadcastReceiver() {
                @RequiresApi(Build.VERSION_CODES.O)
                override fun onReceive(context: Context, intent: Intent) {
                    if (intent.action == SettingsManager.ACTION_SETTINGS_CHANGED) {
                        val key = intent.getStringExtra(SettingsManager.EXTRA_SETTING_KEY)
                        //Log.d("EventsFragment", "Settings changed: $key")

                        // Handle specific settings changes
                        when (key) {
                            SettingsManager.KEY_UPDATE_RADIUS -> {
                                // Update radius affects which events are shown
                                refreshEvents()
                            }
                            SettingsManager.KEY_EVENT_EXPIRY -> {
                                // Event expiry affects which events are shown
                                refreshEvents()
                            }
                            SettingsManager.KEY_VOICE_ANNOUNCEMENTS -> {
                                // Voice announcements setting changed
                                if (settingsManager.isVoiceAnnouncementsEnabled()) {
                                    eventsVoiceAnnouncementManager.startAnnouncements()
                                } else {
                                    eventsVoiceAnnouncementManager.stopAnnouncements()
                                }
                            }
                            SettingsManager.KEY_EVENT_DISCOVERY_ANNOUNCEMENTS -> {
                                // Event discovery announcements setting changed
                                // No immediate action needed - manager will check setting on next update
                                Log.d("EventsFragment", "Event discovery announcements setting changed")
                            }
                        }
                    }
                }
            }
        }

        val filter = IntentFilter(SettingsManager.ACTION_SETTINGS_CHANGED)

        try {
            // Use the appropriate flag for Android 14 (API 34) and above
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                requireContext().registerReceiver(settingsReceiver, filter, RECEIVER_NOT_EXPORTED)
            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                // For Android 13
                requireContext().registerReceiver(settingsReceiver, filter, RECEIVER_NOT_EXPORTED)
            } else {
                // For older Android versions
                @Suppress("UnspecifiedRegisterReceiverFlag")
                requireContext().registerReceiver(settingsReceiver, filter)
            }
        } catch (e: Exception) {

        }
    }

    @RequiresApi(Build.VERSION_CODES.O)
    private fun refreshEvents() {
        showLoading()
        viewModel.refreshEvents()
    }

    private fun setupViewModel() {
        val factory = RemoteEventsViewModelFactory(requireActivity().application, eventRepository)
        viewModel = ViewModelProvider(this, factory)[RemoteEventViewModel::class.java]
    }

    private fun setupRecyclerView(view: View) {
        recyclerView = view.findViewById(R.id.recyclerView)
        recyclerView.layoutManager = LinearLayoutManager(requireContext())
        // Initialize with empty list - updates will come through StateFlow
        eventTypeAdapter = EventTypeAdapter(emptyList(), ::openDetails)
        recyclerView.adapter = eventTypeAdapter
    }

    private fun setupLocationService() {
        locationService = LocationService.getInstance(requireContext())
        locationService.addLocationCallback(locationCallback)

        // Request high-frequency location updates for real-time distance tracking
        if (locationService.checkLocationPermission()) {
            locationService.requestLocationUpdates(
                LocationConstants.NAVIGATION_UPDATE_INTERVAL,
                LocationConstants.NAVIGATION_FASTEST_INTERVAL,
                LocationConstants.NAVIGATION_MIN_DISTANCE_CHANGE
            )
            //Log.d("EventsFragment", "Requested high-frequency location updates for real-time distance tracking")
        }
    }

    private fun requestLocationPermissions() {
        LocationService.requestLocationPermissions(locationPermissionRequest)
    }

    private fun openDetails(holder: RecyclerView.ViewHolder, events: List<EventRemoteReport>) {
        try {
            if (holder is EventTypeAdapter.ViewHolder) {
                //Log.d("EventsFragment", "Opening details for ${events.size} events")

                // Check if the RecyclerView is still valid
                if (holder.recyclerView == null) {
                    Log.e("EventsFragment", "RecyclerView is null in ViewHolder")
                    return
                }

                // Create adapter with try-catch to handle any initialization errors
                val adapter = try {
                    EventDetailAdapter(events, locationService, requireContext())
                } catch (e: Exception) {
                    Log.e("EventsFragment", "Failed to create EventDetailAdapter", e)
                    return
                }

                // Set up the RecyclerView with error handling
                try {
                    with(holder.recyclerView) {
                        this.adapter = adapter
                        layoutManager = LinearLayoutManager(requireContext())
                    }
                    //Log.d("EventsFragment", "Successfully set up details RecyclerView")
                } catch (e: Exception) {
                    Log.e("EventsFragment", "Error setting up RecyclerView", e)
                }
            } else {
                Log.e("EventsFragment", "Invalid ViewHolder type: ${holder.javaClass.simpleName}")
            }
        } catch (e: Exception) {
            Log.e("EventsFragment", "Unexpected error in openDetails", e)
        }
    }

    private fun showToast(message: String) {
        Toast.makeText(requireContext(), message, Toast.LENGTH_LONG).show()
    }

    @RequiresApi(Build.VERSION_CODES.O)
    override fun onResume() {
        super.onResume()

        // Start voice announcements for events
        eventsVoiceAnnouncementManager.startAnnouncements()

        refreshDataIfNeeded()
    }

    @RequiresApi(Build.VERSION_CODES.O)
    private fun refreshDataIfNeeded() {
        locationService.getCurrentLocation()?.let { location ->
            if (!viewModel.hasLoadedData()) {
                eventRepository.setCurrentLocation(location)
            }
        }

        viewModel.refreshEvents()
    }

    override fun onDestroyView() {
        super.onDestroyView()

        // Stop voice announcements
        eventsVoiceAnnouncementManager.stopAnnouncements()

        cleanupLocationService()
        unregisterSettingsReceiver()

        // Cleanup any EventDetailAdapters that might still be active
        cleanupEventDetailAdapters()
    }

    private fun cleanupLocationService() {
        try {
            // First try to remove our specific callback
            locationService.removeLocationCallback(locationCallback)

            // Then try to remove any remaining callbacks (as a fallback)
            if (locationService.locationCallbacks.isNotEmpty()) {
                try {
                    locationService.removeLocationCallback(locationService.locationCallbacks.first())
                } catch (e: Exception) {
                    Log.e("EventsFragment", "Error removing first location callback", e)
                }
            }

            // Finally stop location updates
            locationService.stopLocationUpdates()
            //Log.d("EventsFragment", "Successfully cleaned up location service")
        } catch (e: Exception) {
            Log.e("EventsFragment", "Error cleaning up location service", e)
        }
    }

    private fun unregisterSettingsReceiver() {
        // Unregister the settings receiver
        if (settingsReceiver != null) {
            try {
                requireContext().unregisterReceiver(settingsReceiver)
                //Log.d("EventsFragment", "Successfully unregistered settings receiver")
            } catch (e: IllegalArgumentException) {
                // This exception occurs if the receiver was not registered
                Log.w("EventsFragment", "Receiver was not registered: ${e.message}")
            } catch (e: Exception) {
                Log.e("EventsFragment", "Error unregistering receiver: ${e.message}", e)
            } finally {
                settingsReceiver = null
            }
        } else {
            //Log.d("EventsFragment", "No receiver to unregister")
        }
    }

    private fun cleanupEventDetailAdapters() {
        try {
            // Find all EventDetailAdapters in the RecyclerView and clean them up
            for (i in 0 until recyclerView.childCount) {
                val child = recyclerView.getChildAt(i)
                val viewHolder = recyclerView.getChildViewHolder(child)
                if (viewHolder is EventTypeAdapter.ViewHolder) {
                    val detailRecyclerView = viewHolder.recyclerView
                    val adapter = detailRecyclerView.adapter
                    if (adapter is EventDetailAdapter) {
                        adapter.cleanup()
                    }
                }
            }
            //Log.d("EventsFragment", "Successfully cleaned up EventDetailAdapters")
        } catch (e: Exception) {
            Log.e("EventsFragment", "Error cleaning up EventDetailAdapters", e)
        }
    }

    private fun showLoading() {
        // Show loading indicator
        progressBar.visibility = View.VISIBLE
        waitingForLocationView.visibility = View.GONE
        recyclerView.visibility = View.GONE
    }

    private fun showEvents(groupedEvents: List<GroupedEvent>) {
        // Update our cached copy of the grouped events
        currentGroupedEvents = groupedEvents

        // Update voice announcements with new events
        eventsVoiceAnnouncementManager.updateEvents(groupedEvents)

        progressBar.visibility = View.GONE
        waitingForLocationView.visibility = View.GONE
        offlineView.visibility = View.GONE
        recyclerView.visibility = View.VISIBLE

        // Log the grouped events for debugging
        //Log.d("EventsFragment", "Showing ${groupedEvents.size} event types")
        groupedEvents.forEach { group ->
            //Log.d("EventsFragment", "Event type: ${group.eventType}, count: ${group.events.size}")
        }

        // Process the groups to expand the specified report type if needed
        val processedGroups = if (!expandReportType.isNullOrEmpty()) {
            groupedEvents.map { group ->
                if (group.eventType.equals(expandReportType, ignoreCase = true)) {
                    //Log.d("EventsFragment", "Auto-expanding report type: ${group.eventType}")
                    group.copy(isExpanded = true)
                } else {
                    group
                }
            }
        } else {
            groupedEvents
        }

        eventTypeAdapter = EventTypeAdapter(processedGroups, ::openDetails)
        recyclerView.adapter = eventTypeAdapter

        // If we have a report type to expand, find it and expand it in the UI
        if (!expandReportType.isNullOrEmpty()) {
            val position = processedGroups.indexOfFirst {
                it.eventType.equals(expandReportType, ignoreCase = true)
            }
            if (position >= 0) {
                // Scroll to the position
                recyclerView.scrollToPosition(position)

                // Get the ViewHolder and expand it
                val viewHolder = recyclerView.findViewHolderForAdapterPosition(position) as? EventTypeAdapter.ViewHolder
                viewHolder?.let {
                    // Simulate a click to expand the item
                    openDetails(it, processedGroups[position].events)
                    it.recyclerView.visibility = View.VISIBLE
                }
            }
        }
    }

    // Track the current grouped events for offline handling
    private var currentGroupedEvents = emptyList<GroupedEvent>()

    private fun showError(message: String) {
        progressBar.visibility = View.GONE
        waitingForLocationView.visibility = View.GONE

        // Check if it's a network-related error
        if (message.contains("network") || message.contains("internet") || message.contains("offline") ||
            message.contains("connection") || !eventRepository.networkMonitor.isNetworkAvailable()) {

            // Show offline view with cached data message
            val hasLocalData = currentGroupedEvents.isNotEmpty()
            if (hasLocalData) {
                // We have cached data to show
                (offlineMessage as TextView).text = "Showing cached events. Connect to the internet for the latest updates."
                offlineView.visibility = View.VISIBLE
                recyclerView.visibility = View.VISIBLE
            } else {
                // No cached data available
                (offlineMessage as TextView).text = "No cached events available. Connect to the internet to view events."
                offlineView.visibility = View.VISIBLE
                recyclerView.visibility = View.GONE
            }
        } else {
            // For other errors, just show a toast
            showToast(message)
        }
    }

    @RequiresApi(Build.VERSION_CODES.O)
    private fun handleLocationAvailable(location: Location) {
        currentLocation = location
        viewModel.setLocation(location)

        // Update voice announcements with location
        eventsVoiceAnnouncementManager.updateLocation(location)

        // Hide the waiting for location view
        waitingForLocationView.visibility = View.GONE

        if (!viewModel.hasLoadedData()) {
            viewModel.refreshData()
        } else {
            // If data is already loaded, just update the distances in real-time
            viewModel.updateAllDistances()
            //Log.d("EventsFragment", "Updated all distances in real-time for location: ${location.latitude}, ${location.longitude}")
        }
    }

    @RequiresApi(Build.VERSION_CODES.O)
    private fun handleLocationUnavailable() {
        // Check if we have cached data to show even without location
        if (currentGroupedEvents.isNotEmpty()) {
            // We have cached data, so show it with a message
            progressBar.visibility = View.GONE
            waitingForLocationView.visibility = View.GONE
            offlineView.visibility = View.VISIBLE
            recyclerView.visibility = View.VISIBLE
            (offlineMessage as TextView).text = "Showing cached events. Location services are disabled."
        } else {
            // No cached data, show the waiting for location view
            progressBar.visibility = View.GONE
            waitingForLocationView.visibility = View.VISIBLE
            recyclerView.visibility = View.GONE
        }

        // Start a timer to periodically check for location
        lifecycleScope.launch {
            try {
                var locationFound = false
                var attempts = 0
                val maxAttempts = 30 // Maximum 1 minute (30 * 2 seconds)

                while (!locationFound && isAdded && attempts < maxAttempts) {
                    delay(2000) // Check every 2 seconds, properly suspends in coroutines
                    attempts++

                    val currentLocation = locationService.getCurrentLocation()
                    if (currentLocation != null && isAdded) {
                        //Log.d("EventsFragment", "Location found during periodic check: $currentLocation")
                        handleLocationAvailable(currentLocation)
                        locationFound = true // Exit the loop once a location is found
                    } else if (attempts % 5 == 0) { // Log every 10 seconds
                        //Log.d("EventsFragment", "Still waiting for location... (attempt $attempts/$maxAttempts)")
                    }
                }

                if (!locationFound && isAdded) {
                    //Log.d("EventsFragment", "Location check timed out after $maxAttempts attempts")
                }
            } catch (e: Exception) {
                Log.e("EventsFragment", "Error in location check loop: ${e.message}")
            }
        }
    }
}
