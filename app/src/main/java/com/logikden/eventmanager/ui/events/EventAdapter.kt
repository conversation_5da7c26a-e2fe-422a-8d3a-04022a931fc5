//package com.logikden.eventmanager.ui.events
//
//import android.view.LayoutInflater
//import android.view.View
//import android.view.ViewGroup
//import android.widget.TextView
//import androidx.recyclerview.widget.RecyclerView
//import com.logikden.eventmanager.R
//import com.logikden.eventmanager.data.Event
//import java.text.SimpleDateFormat
//import java.util.Locale
//
//class EventTypeAdapter(private val eventTypes: List<GroupedEvent>, private val onClick: (String) -> Unit) :
//    RecyclerView.Adapter<EventTypeAdapter.EventTypeViewHolder>() {
//
//    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): EventTypeViewHolder {
//        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_event_type, parent, false)
//        return EventTypeViewHolder(view)
//    }
//
//    override fun onBindViewHolder(holder: EventTypeViewHolder, position: Int) {
//        val eventType = eventTypes[position]
//        holder.bind(eventType)
//    }
//
//    override fun getItemCount() = eventTypes.size
//
//    inner class EventTypeViewHolder(view: View) : RecyclerView.ViewHolder(view) {
//        private val eventTypeText: TextView = itemView.findViewById(R.id.eventTypeText)
//
//        fun bind(groupedEvent: GroupedEvent) {
//            eventTypeText.text = groupedEvent.eventType
//
//            // Handle click to show detailed events
//            itemView.setOnClickListener {
//                onClick(groupedEvent.eventType)
//            }
//        }
//    }
//}
