package com.logikden.eventmanager.ui.debug

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import android.widget.TextView
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.logikden.eventmanager.R
import com.logikden.eventmanager.services.LocationService
import com.logikden.eventmanager.utils.SpeedBasedUIManager
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.launch

/**
 * Debug view that displays current speed category and location tracking information
 */
class SpeedDebugView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    private lateinit var speedCategoryText: TextView
    private lateinit var averageSpeedText: TextView
    private lateinit var updateIntervalText: TextView
    private lateinit var statusIndicator: TextView
    
    private val speedBasedUIManager = SpeedBasedUIManager.getInstance()
    
    init {
        orientation = VERTICAL
        setupViews()
    }
    
    private fun setupViews() {
        // Inflate the debug layout
        LayoutInflater.from(context).inflate(R.layout.view_speed_debug, this, true)
        
        // Find views
        speedCategoryText = findViewById(R.id.speedCategoryText)
        averageSpeedText = findViewById(R.id.averageSpeedText)
        updateIntervalText = findViewById(R.id.updateIntervalText)
        statusIndicator = findViewById(R.id.statusIndicator)
        
        // Set initial values
        updateDisplay(LocationService.SpeedCategory.STATIONARY, 0f)
    }
    
    /**
     * Start observing speed changes (call from lifecycle-aware component)
     */
    fun startObserving(lifecycleOwner: LifecycleOwner) {
        lifecycleOwner.lifecycleScope.launch {
            speedBasedUIManager.currentSpeedCategory.collect { category ->
                val speed = speedBasedUIManager.currentAverageSpeed.value
                updateDisplay(category, speed)
            }
        }
        
        lifecycleOwner.lifecycleScope.launch {
            speedBasedUIManager.currentAverageSpeed.collect { speed ->
                val category = speedBasedUIManager.currentSpeedCategory.value
                updateDisplay(category, speed)
            }
        }
    }
    
    private fun updateDisplay(category: LocationService.SpeedCategory, averageSpeed: Float) {
        // Update speed category
        val categoryName = speedBasedUIManager.getSpeedCategoryDisplayName(category)
        speedCategoryText.text = "Speed Category: $categoryName"
        
        // Update average speed
        averageSpeedText.text = "Average Speed: ${String.format("%.1f", averageSpeed)} km/h"
        
        // Update UI update interval
        val interval = speedBasedUIManager.getCurrentUIUpdateInterval()
        updateIntervalText.text = "UI Update Interval: ${interval / 1000}s"
        
        // Update status indicator color
        val color = speedBasedUIManager.getSpeedCategoryColor(category)
        statusIndicator.setBackgroundColor(color)
        statusIndicator.text = "●"
        
        // Update text colors based on category - using consistent colors from SpeedBasedUIManager
        val textColor = speedBasedUIManager.getSpeedCategoryColor(category)
        speedCategoryText.setTextColor(textColor)
        averageSpeedText.setTextColor(textColor)
    }
    
    /**
     * Show or hide the debug view
     */
    fun setDebugVisible(visible: Boolean) {
        visibility = if (visible) VISIBLE else GONE
    }
}
