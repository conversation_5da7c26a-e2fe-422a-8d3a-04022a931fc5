package com.logikden.eventmanager.ui.events

import android.app.Application
import android.content.Context
import android.location.Location
import android.os.Build
import android.util.Log
import androidx.annotation.RequiresApi
import com.logikden.eventmanager.data.EventRemoteReport
import com.logikden.eventmanager.data.EventRemoteReportDao
import com.logikden.eventmanager.data.dto.GroupedEvent
import com.logikden.eventmanager.data.dto.toEntity
import com.logikden.eventmanager.services.ApiService
import com.logikden.eventmanager.services.ISyncableRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import com.logikden.eventmanager.helper.DateHelper
import com.logikden.eventmanager.helper.DateHelper.Companion.getTimePassedInHours
import com.logikden.eventmanager.services.NetworkMonitor
import com.logikden.eventmanager.utils.SettingsManager
import com.logikden.eventmanager.utils.LocationCacheManager
import java.util.concurrent.TimeUnit
import java.util.*
import java.text.SimpleDateFormat

private const val TAG = "RemoteEventRepository"
private const val PREFS_NAME = "event_manager_prefs"
private const val KEY_EVENT_EXPIRY = "event_expiry"
private const val KEY_LAST_SYNC_TIME = "last_sync_time"
private const val DEFAULT_EXPIRY_TIME = 10
private const val PROXIMITY_THRESHOLD_KM = 1.5f // 1.5 kilometers
private const val TIME_WINDOW_HOURS = 2 // 2 hours

class RemoteEventRepository(
    val eventRemoteReportDao: EventRemoteReportDao, // Changed to public for ViewModel access
    private val apiService: ApiService,
    private val application: Application
) : ISyncableRepository {

    private var currentLocation: Location? = null
    private val settingsManager = SettingsManager.getInstance(application)
    private val locationCacheManager = LocationCacheManager.getInstance(application)
    private val expiryTime: Int
        get() = settingsManager.getEventExpiry()
    val networkMonitor = NetworkMonitor(application, null, this) // Changed to public for fragment access

    @RequiresApi(Build.VERSION_CODES.O)
    suspend fun getGroupedReports(): Flow<List<GroupedEvent>> {
        // Check if we should attempt to refresh data from the server
        val shouldRefresh = shouldRefreshData()

        // Only refresh if network is available and we should refresh
        if (shouldRefresh && networkMonitor.isNetworkAvailable() && hasLocation()) {
            Log.d(TAG, "Attempting to refresh reports from server")
            refreshReports()
                .onSuccess { success ->
                    if (success) {
                        Log.d(TAG, "Successfully refreshed reports from server")
                    } else {
                        Log.d(TAG, "No new reports available from server")
                    }
                }
                .onFailure { exception ->
                    Log.e(TAG, "Failed to refresh reports: ${exception.message}")
                }
        } else {
            if (!networkMonitor.isNetworkAvailable()) {
                Log.d(TAG, "Network not available, using cached reports")
            } else if (!hasLocation()) {
                Log.d(TAG, "Location not available, using cached reports")
            } else if (!shouldRefresh) {
                Log.d(TAG, "Recent refresh already performed, using cached reports")
            }
        }

        // Always return data from the local database
        return eventRemoteReportDao.getAllReports()
            .map { reports ->
                Log.d(TAG, "Retrieved ${reports.size} reports from local database")
                reports
                    .filter { report ->
                        val isValid = getTimePassedInHours(report.reportTime.toString()) <= expiryTime
                        if (!isValid) {
                            Log.d(TAG, "Filtering out expired report: ${report.reportName}")
                        }
                        isValid
                    }
                    .groupBy { it.reportTypeId }
                    .map { (_, events) ->
                        GroupedEvent(
                            eventType = events.firstOrNull()?.reportName ?: "Unknown",
                            events = events,
                            isExpanded = false
                        )
                    }
                    .sortedBy { it.eventType }
            }
    }

    /**
     * Determines if we should refresh data from the server based on the last sync time
     * @return true if we should refresh, false otherwise
     */
    private fun shouldRefreshData(): Boolean {
        val lastSync = getLastSyncTime()
        val currentTime = System.currentTimeMillis()
        val timeSinceLastSync = currentTime - lastSync

        // Refresh if it's been more than 5 minutes since the last sync
        val refreshInterval = 5 * 60 * 1000L // 5 minutes in milliseconds
        val shouldRefresh = lastSync == 0L || timeSinceLastSync > refreshInterval

        Log.d(TAG, "Last sync was ${timeSinceLastSync / 1000} seconds ago, should refresh: $shouldRefresh")
        return shouldRefresh
    }

    fun setCurrentLocation(location: Location) {
        currentLocation = location
        Log.d(TAG, "Location updated: lat=${location.latitude}, lon=${location.longitude}")
    }

    @RequiresApi(Build.VERSION_CODES.O)
    suspend fun refreshReports(): Result<Boolean> {
        if (currentLocation == null) {
            Log.d(TAG, "Location not available for refresh")
            return Result.failure(IllegalStateException("Location not available"))
        }

        return try {
            val currentUtcTime = DateHelper.getCurrentUtcTime()
            // Add logging
            Log.d(TAG, "Fetching reports for location: ${currentLocation!!.latitude}, ${currentLocation!!.longitude}")

            val remoteReports = apiService.getEventReports(
                currentLocation!!.latitude,
                currentLocation!!.longitude,
                currentUtcTime.toString()
            )

            // Add logging
//            Log.d(TAG, "Received ${remoteReports.size} reports")
//            remoteReports.forEach { report ->
//                Log.d(TAG, "Report: ${report.reportName} at ${report.gpsLocation.latitude}, ${report.gpsLocation.longitude}")
//            }

            if (remoteReports.isEmpty()) {
                Log.d(TAG, "No reports found for the current location")
                return Result.success(false)
            }

            val filteredReports = filterAndProcessReports(remoteReports.map { it.toEntity() })
            updateLocalDatabase(filteredReports)
            Result.success(true)
        } catch (e: Exception) {
            Log.e(TAG, "Error refreshing reports", e)
            Result.failure(e)
        }
    }

    @RequiresApi(Build.VERSION_CODES.O)
    private fun filterAndProcessReports(remoteReports: List<EventRemoteReport>): List<EventRemoteReport> {
        // First filter out expired reports
        val validReports = remoteReports.filter { report ->
            getTimePassedInHours(report.reportTime.toString()) <= expiryTime
        }

        // Group reports by type
        val reportsByType = validReports.groupBy { it.reportTypeId }

        // Process each group to combine nearby reports
        val combinedReports = mutableListOf<EventRemoteReport>()

        reportsByType.forEach { (_, reports) ->
            // Skip processing if there's only one report of this type
            if (reports.size <= 1) {
                combinedReports.addAll(reports)
                return@forEach
            }

            // Create a mutable list to track which reports have been processed
            val processedReports = reports.toMutableList()

            while (processedReports.isNotEmpty()) {
                // Take the first report as a reference
                val referenceReport = processedReports.removeAt(0)
                val nearbyReports = mutableListOf<EventRemoteReport>()

                // Find all reports that are within the proximity and time window
                val iterator = processedReports.iterator()
                while (iterator.hasNext()) {
                    val report = iterator.next()

                    if (areReportsNearby(referenceReport, report) &&
                        areReportsWithinTimeWindow(referenceReport, report)) {
                        nearbyReports.add(report)
                        iterator.remove()
                    }
                }

                // If no nearby reports, just add the reference report
                if (nearbyReports.isEmpty()) {
                    combinedReports.add(referenceReport)
                } else {
                    // Create a new report with the merged count
                    val mergedCount = nearbyReports.size + 1 // +1 for the reference report itself
                    val mergedReport = referenceReport.copy(mergedReportsCount = mergedCount)
                    combinedReports.add(mergedReport)
                    // Log the combined reports for debugging
                    Log.d(TAG, "Combined $mergedCount reports of type ${referenceReport.reportName}")
                }
            }
        }

        return combinedReports.distinctBy { report ->
            Triple(
                report.latitude to report.longitude,
                report.reportTypeId,
                report.reportTime?.parseDateTime()?.getTwoHourWindowKey()
            )
        }
    }

    /**
     * Check if two reports are within the proximity threshold (1.5km)
     */
    private fun areReportsNearby(report1: EventRemoteReport, report2: EventRemoteReport): Boolean {
        val distance = calculateDistance(
            report1.latitude, report1.longitude,
            report2.latitude, report2.longitude
        )
        return distance <= PROXIMITY_THRESHOLD_KM
    }

    /**
     * Check if two reports are within the time window (2 hours)
     */
    private fun areReportsWithinTimeWindow(report1: EventRemoteReport, report2: EventRemoteReport): Boolean {
        val time1 = parseReportTime(report1.reportTime)
        val time2 = parseReportTime(report2.reportTime)

        if (time1 == null || time2 == null) return false

        val diffInMillis = Math.abs(time1.time - time2.time)
        val diffInHours = TimeUnit.MILLISECONDS.toHours(diffInMillis)

        return diffInHours <= TIME_WINDOW_HOURS
    }

    /**
     * Parse report time string to Date object
     */
    private fun parseReportTime(reportTime: String?): Date? {
        if (reportTime == null) return null

        return try {
            val dateFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.getDefault())
            dateFormat.parse(reportTime)
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing report time: $reportTime", e)
            null
        }
    }

    /**
     * Calculate distance between two points in kilometers
     */
    private fun calculateDistance(lat1: Double, lon1: Double, lat2: Double, lon2: Double): Float {
        val results = FloatArray(1)
        Location.distanceBetween(lat1, lon1, lat2, lon2, results)
        // Convert meters to kilometers
        return results[0] / 1000f
    }

    private suspend fun updateLocalDatabase(reports: List<EventRemoteReport>) {
        // Only delete existing reports if we have new reports to insert
        // This ensures we keep cached data when offline
        if (reports.isNotEmpty()) {
            Log.d(TAG, "Updating local database with ${reports.size} reports")
            eventRemoteReportDao.apply {
                // First delete all existing reports
                deleteAllReports()
                // Then insert the new reports
                insertReports(reports)
            }
            // Save the last successful sync time
            saveLastSyncTime()
        } else {
            Log.d(TAG, "No reports to update in local database, keeping existing cached data")
        }
    }

    private fun saveLastSyncTime() {
        val sharedPrefs = application.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        sharedPrefs.edit().putLong(KEY_LAST_SYNC_TIME, System.currentTimeMillis()).apply()
    }

    private fun getLastSyncTime(): Long {
        val sharedPrefs = application.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        return sharedPrefs.getLong(KEY_LAST_SYNC_TIME, 0)
    }

    fun hasLocation(): Boolean = currentLocation != null

    @RequiresApi(Build.VERSION_CODES.O)
    override suspend fun syncPendingData() {
        // Check network availability first
        if (!networkMonitor.isNetworkAvailable()) {
            Log.d(TAG, "Cannot sync pending data: network not available")
            return
        }

        // Try to get current location, fallback to cached location
        var locationToUse = currentLocation

        if (locationToUse == null) {
            // Try to use cached location for background sync
            locationToUse = locationCacheManager.getCachedLocation()
            if (locationToUse != null) {
                Log.d(TAG, "Using cached location for background sync: ${locationToUse.latitude}, ${locationToUse.longitude}")
                // Temporarily set the cached location for this sync operation
                val originalLocation = currentLocation
                currentLocation = locationToUse

                try {
                    refreshReports()
                        .onSuccess { success ->
                            if (success) {
                                Log.d(TAG, "Successfully synced pending data using cached location")
                            } else {
                                Log.d(TAG, "No new data to sync using cached location")
                            }
                        }
                        .onFailure { exception ->
                            Log.e(TAG, "Failed to sync pending data using cached location: ${exception.message}")
                        }
                } finally {
                    // Restore original location
                    currentLocation = originalLocation
                }
            } else {
                Log.d(TAG, "Cannot sync pending data: no current or cached location available")
            }
        } else {
            // Use current location for sync
            Log.d(TAG, "Network available, syncing pending data with current location")
            refreshReports()
                .onSuccess { success ->
                    if (success) {
                        Log.d(TAG, "Successfully synced pending data")
                    } else {
                        Log.d(TAG, "No new data to sync")
                    }
                }
                .onFailure { exception ->
                    Log.e(TAG, "Failed to sync pending data: ${exception.message}")
                }
        }
    }

    suspend fun getReportById(localId: Long): EventRemoteReport? =
        eventRemoteReportDao.getReportById(localId)

    suspend fun markReportAsReached(localId: Long) {
        eventRemoteReportDao.markReportAsReached(localId)
    }

    fun getReportsByReachedStatus(reached: Boolean): Flow<List<EventRemoteReport>> =
        eventRemoteReportDao.getUnreachedReports()

    fun getReportsByType(reportTypeId: Int): Flow<List<EventRemoteReport>> =
        eventRemoteReportDao.getReportsByType(reportTypeId)

    suspend fun clearAllReports() {
        eventRemoteReportDao.deleteAllReports()
    }

    @RequiresApi(Build.VERSION_CODES.O)
    private fun String.parseDateTime(): LocalDateTime = try {
        LocalDateTime.parse(this, DateTimeFormatter.ISO_DATE_TIME)
    } catch (e: Exception) {
        LocalDateTime.now()
    }

    @RequiresApi(Build.VERSION_CODES.O)
    private fun LocalDateTime.getTwoHourWindowKey(): LocalDateTime =
        truncatedTo(ChronoUnit.HOURS).minusHours((hour % 2).toLong())
}