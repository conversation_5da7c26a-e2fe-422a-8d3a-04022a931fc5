package com.logikden.eventmanager.ui.settings

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.util.Log
import androidx.core.app.ActivityCompat
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.SeekBar
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.widget.SwitchCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.logikden.eventmanager.R
import com.logikden.eventmanager.data.AppDatabase
import com.logikden.eventmanager.data.EventLocalReport
import com.logikden.eventmanager.data.Tile
import com.logikden.eventmanager.data.TileDao
import com.logikden.eventmanager.data.dto.EventSettings
import com.logikden.eventmanager.services.RetrofitClient
import com.logikden.eventmanager.ui.home.EventReportRepository
import com.logikden.eventmanager.services.NetworkMonitor
import com.logikden.eventmanager.utils.SettingsManager
import com.logikden.eventmanager.services.ProximityService
import com.logikden.eventmanager.MainActivity
import com.logikden.eventmanager.utils.ErrorMessageUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class SettingsFragment : Fragment() {
    private lateinit var settingsManager: SettingsManager
    private lateinit var settingsViewModel: SettingsViewModel
    private lateinit var eventRepository: EventReportRepository
    private lateinit var networkMonitor: NetworkMonitor
    private lateinit var tileDao: TileDao
    private lateinit var reportNotificationAdapter: ReportNotificationAdapter

    // UI Components
    private lateinit var radiusSeekBar: SeekBar
    private lateinit var radiusValueText: TextView
    private lateinit var checkFrequencySeekBar: SeekBar
    private lateinit var checkFrequencyValueText: TextView
    private lateinit var eventExpirySeekBar: SeekBar
    private lateinit var eventExpiryValueText: TextView
    private lateinit var movedUpdateSeekBar: SeekBar
    private lateinit var movedUpdateValueText: TextView
    private lateinit var proximityThresholdSeekBar: SeekBar
    private lateinit var proximityThresholdValueText: TextView
    private lateinit var notificationCooldownSeekBar: SeekBar
    private lateinit var notificationCooldownValueText: TextView
    private lateinit var reportNotificationRecyclerView: RecyclerView
    private lateinit var immediateUpdatesSwitch: SwitchCompat

    // Battery optimization UI components
    private lateinit var activityRecognitionSwitch: SwitchCompat
    private lateinit var speedBasedFrequencySwitch: SwitchCompat

    // New navigation feature UI components
    private lateinit var realtimeUpdatesSwitch: SwitchCompat
    private lateinit var keepScreenOnSwitch: SwitchCompat
    private lateinit var voiceAnnouncementsSwitch: SwitchCompat
    private lateinit var voiceAnnouncementIntervalSeekBar: SeekBar
    private lateinit var voiceAnnouncementIntervalValueText: TextView
    private var pendingChanges = false

    private val apiService = RetrofitClient.instance

    // Constants
    companion object {
        // Using constants from SettingsManager
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_settings, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Initialize SettingsManager
        settingsManager = SettingsManager.getInstance(requireContext())

        // Initialize ViewModel
        settingsViewModel = ViewModelProvider(this)[SettingsViewModel::class.java]

        var database = AppDatabase.getDatabase(requireContext(), lifecycleScope)

        eventRepository = EventReportRepository(database.pendingEventReportDao(), apiService, requireContext())
        tileDao = database.tileDao()

        networkMonitor = NetworkMonitor(requireContext(), lifecycleScope, eventRepository)

        // Initialize UI components
        initializeUIComponents(view)

        // Load saved settings
        loadSettings()

        // Load report types for notification settings
        loadReportTypes()

        // Set up listeners
        setupListeners()

        // Add a button to apply settings immediately
        view.findViewById<View>(R.id.apply_settings_button)?.setOnClickListener {
            applySettingsImmediately()
        }
    }

    override fun onPause() {
        super.onPause()
        // Save pending changes when the user leaves the settings screen
        if (pendingChanges && !immediateUpdatesSwitch.isChecked) {
            saveAllSettings()
            pendingChanges = false
            Log.d("SettingsFragment", "Auto-saved pending settings changes on pause")
        }
    }

    override fun onStop() {
        super.onStop()
        // Additional safety net to save settings
        if (pendingChanges && !immediateUpdatesSwitch.isChecked) {
            saveAllSettings()
            pendingChanges = false
            Log.d("SettingsFragment", "Auto-saved pending settings changes on stop")
        }
    }

    override fun onResume() {
        super.onResume()
        // Refresh SeekBar visibility when fragment resumes
        if (::radiusSeekBar.isInitialized) {
            refreshSeekBarVisibility()
        }
    }

    private fun initializeUIComponents(view: View) {
        radiusSeekBar = view.findViewById(R.id.radius_seekbar)
        radiusValueText = view.findViewById(R.id.radius_value_text)
        checkFrequencySeekBar = view.findViewById(R.id.check_frequency_seekbar)
        checkFrequencyValueText = view.findViewById(R.id.check_frequency_value_text)
        eventExpirySeekBar = view.findViewById(R.id.event_expiry_seekbar)
        eventExpiryValueText = view.findViewById(R.id.event_expiry_value_text)
        movedUpdateSeekBar = view.findViewById(R.id.moved_update_seekbar)
        movedUpdateValueText = view.findViewById(R.id.moved_update_value_text)
        proximityThresholdSeekBar = view.findViewById(R.id.proximity_threshold_seekbar)
        proximityThresholdValueText = view.findViewById(R.id.proximity_threshold_value_text)
        notificationCooldownSeekBar = view.findViewById(R.id.notification_cooldown_seekbar)
        notificationCooldownValueText = view.findViewById(R.id.notification_cooldown_value)
        reportNotificationRecyclerView = view.findViewById(R.id.report_notification_recycler_view)
        immediateUpdatesSwitch = view.findViewById(R.id.immediate_updates_switch)

        // Initialize battery optimization UI components
        activityRecognitionSwitch = view.findViewById(R.id.activity_recognition_switch)
        speedBasedFrequencySwitch = view.findViewById(R.id.speed_based_frequency_switch)

        // Initialize new navigation feature UI components
        realtimeUpdatesSwitch = view.findViewById(R.id.realtime_updates_switch)
        keepScreenOnSwitch = view.findViewById(R.id.keep_screen_on_switch)
        voiceAnnouncementsSwitch = view.findViewById(R.id.voice_announcements_switch)
        voiceAnnouncementIntervalSeekBar = view.findViewById(R.id.voice_announcement_interval_seekbar)
        voiceAnnouncementIntervalValueText = view.findViewById(R.id.voice_announcement_interval_value_text)

        // Show/Hide save button based on immediate updates preference
        val saveButton = view.findViewById<Button>(R.id.save_settings_button)

        // Load the immediate updates preference state
        val immediateUpdatesEnabled = settingsManager.getBoolean("immediate_updates_enabled", true)
        immediateUpdatesSwitch.isChecked = immediateUpdatesEnabled
        saveButton.visibility = if (immediateUpdatesEnabled) View.GONE else View.VISIBLE

        immediateUpdatesSwitch.setOnCheckedChangeListener { _, isChecked ->
            saveButton.visibility = if (isChecked) View.GONE else View.VISIBLE
            // Save the immediate updates preference
            settingsManager.setBoolean("immediate_updates_enabled", isChecked)

            // If switching to immediate updates mode and there are pending changes, save them
            if (isChecked && pendingChanges) {
                saveAllSettings()
                pendingChanges = false
                Toast.makeText(requireContext(), "Pending changes applied", Toast.LENGTH_SHORT).show()
            }
        }

        // Configure SeekBars with proper ranges and ensure visibility
        radiusSeekBar.max = 500 // Max 50 km
        radiusSeekBar.min = 1 // Minimum 1 km

        movedUpdateSeekBar.max = 50
        movedUpdateSeekBar.min = 1 // Minimum 1 km

        checkFrequencySeekBar.max = 3600
        checkFrequencySeekBar.min = 5 // Minimum 5 seconds

        eventExpirySeekBar.max = 72 // Max 72 hours
        eventExpirySeekBar.min = 1 // Minimum 1 hour

        proximityThresholdSeekBar.max = 50 // Max 5.0 km (using 10x for precision)
        proximityThresholdSeekBar.min = 1 // Minimum 0.1 km

        notificationCooldownSeekBar.max = 90 // 90 steps
        notificationCooldownSeekBar.min = 0

        voiceAnnouncementIntervalSeekBar.max = 500 // Max 500 meters
        voiceAnnouncementIntervalSeekBar.min = 50 // Minimum 50 meters

        // Set up the report notification recycler view
        reportNotificationAdapter = ReportNotificationAdapter(requireContext()) { tile, isEnabled ->
            // Save the notification preference when toggled
            settingsManager.setReportTypeNotificationEnabled(tile.id, isEnabled)

            // Update the tile in the database
            lifecycleScope.launch(Dispatchers.IO) {
                val updatedTile = tile.copy(notificationsEnabled = isEnabled)
                tileDao.update(updatedTile)
            }

            // Show a toast message
            val message = if (isEnabled) {
                "Notifications enabled for ${tile.title}"
            } else {
                "Notifications disabled for ${tile.title}"
            }
            Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show()
        }

        reportNotificationRecyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = reportNotificationAdapter
            addItemDecoration(DividerItemDecoration(requireContext(), LinearLayoutManager.VERTICAL))
        }
    }

    private fun loadReportTypes() {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                // Get all tiles (both active and inactive)
                val allTiles = tileDao.getAll()

                // If no tiles are found, create some default ones for testing
                if (allTiles.isEmpty()) {
                    Log.d("SettingsFragment", "No tiles found, creating default tiles")
                    createDefaultTiles()
                    // Get the tiles again after creating defaults
                    val updatedTiles = tileDao.getAll()
                    withContext(Dispatchers.Main) {
                        reportNotificationAdapter.submitList(updatedTiles)
                    }
                } else {
                    Log.d("SettingsFragment", "Found ${allTiles.size} tiles")
                    withContext(Dispatchers.Main) {
                        // Update the adapter with all tiles
                        reportNotificationAdapter.submitList(allTiles)
                    }
                }
            } catch (e: Exception) {
                Log.e("SettingsFragment", "Error loading report types", e)
                withContext(Dispatchers.Main) {
                    val userFriendlyMessage = ErrorMessageUtils.getUserFriendlyErrorMessage(e)
                    Toast.makeText(requireContext(), userFriendlyMessage, Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    private suspend fun createDefaultTiles() {
        val defaultTiles = listOf(
            Tile(title = "Power Outage", iconName = "ic_power_out", order = 1),
            Tile(title = "Road Blocked", iconName = "ic_road_blocked", order = 2),
            Tile(title = "Car Crash", iconName = "ic_car_crash", order = 3),
            Tile(title = "Warning", iconName = "ic_warning", order = 4),
            Tile(title = "Water Outage", iconName = "ic_water_outage", order = 5)
        )

        defaultTiles.forEach { tile ->
            tileDao.insert(tile)
        }
    }

    // Filter events based on expiry time
    fun filterExpiredEvents(events: List<EventLocalReport>): List<EventLocalReport> {
        val settings = getEventSettings()
        val expiryTimeMillis = settings.eventExpiry * 60 * 60 * 1000L
        val currentTimeMillis = System.currentTimeMillis()

        return events.filter { event ->
            (currentTimeMillis - event.dateTime) <= expiryTimeMillis
        }
    }

    private fun loadSettings() {
        // Get saved values from SettingsManager
        val radius = settingsManager.getUpdateRadius()
        val frequency = settingsManager.getCheckFrequency()
        val expiry = settingsManager.getEventExpiry()
        val moveUpdate = settingsManager.getMoveUpdate()
        val proximityThreshold = settingsManager.getProximityThreshold()
        val notificationCooldown = settingsManager.getNotificationCooldown()

        // Get battery optimization settings
        val activityRecognitionEnabled = settingsManager.isActivityRecognitionEnabled()
        val speedBasedFrequencyEnabled = settingsManager.isSpeedBasedFrequencyEnabled()

        // Get new feature settings
        val realtimeUpdates = settingsManager.isRealtimeDistanceUpdatesEnabled()
        val keepScreenOn = settingsManager.isKeepScreenOnEnabled()
        val voiceAnnouncements = settingsManager.isVoiceAnnouncementsEnabled()
        val voiceAnnouncementInterval = settingsManager.getVoiceAnnouncementInterval()

        // Update UI
        movedUpdateSeekBar.progress = moveUpdate
        radiusSeekBar.progress = radius
        checkFrequencySeekBar.progress = frequency
        eventExpirySeekBar.progress = expiry
        proximityThresholdSeekBar.progress = (proximityThreshold * 10).toInt()

        // Update battery optimization UI
        activityRecognitionSwitch.isChecked = activityRecognitionEnabled
        speedBasedFrequencySwitch.isChecked = speedBasedFrequencyEnabled

        // Update new feature UI
        realtimeUpdatesSwitch.isChecked = realtimeUpdates
        keepScreenOnSwitch.isChecked = keepScreenOn
        voiceAnnouncementsSwitch.isChecked = voiceAnnouncements
        voiceAnnouncementIntervalSeekBar.progress = voiceAnnouncementInterval

        // Calculate progress for notification cooldown
        val cooldownProgress = ((notificationCooldown - SettingsManager.MIN_NOTIFICATION_COOLDOWN) * 90 /
                (SettingsManager.MAX_NOTIFICATION_COOLDOWN - SettingsManager.MIN_NOTIFICATION_COOLDOWN)).toInt()
        notificationCooldownSeekBar.progress = cooldownProgress.coerceIn(0, 90)

        // Update text displays
        updateRadiusText(radius)
        updateFrequencyText(frequency)
        updateExpiryText(expiry)
        updateMoveText(moveUpdate)
        updateProximityThresholdText(proximityThreshold)
        updateNotificationCooldownText(notificationCooldown)
        updateVoiceAnnouncementIntervalText(voiceAnnouncementInterval)

        // Force refresh SeekBars to ensure visibility
        refreshSeekBarVisibility()
    }

    // Get the current settings
    fun getEventSettings(): EventSettings {
        return settingsManager.getEventSettings()
    }

    /**
     * Force refresh SeekBar visibility to ensure they are always visible
     * regardless of their current progress value
     */
    private fun refreshSeekBarVisibility() {
        // Force invalidate and refresh all SeekBars
        val seekBars = listOf(
            radiusSeekBar,
            movedUpdateSeekBar,
            checkFrequencySeekBar,
            eventExpirySeekBar,
            proximityThresholdSeekBar,
            notificationCooldownSeekBar,
            voiceAnnouncementIntervalSeekBar
        )

        seekBars.forEach { seekBar ->
            seekBar.invalidate()
            seekBar.requestLayout()
            // Ensure the SeekBar is visible and enabled
            seekBar.visibility = View.VISIBLE
            seekBar.alpha = 1.0f
        }

        // Special handling for voice announcement interval based on switch state
        val isVoiceEnabled = voiceAnnouncementsSwitch.isChecked
        voiceAnnouncementIntervalSeekBar.isEnabled = isVoiceEnabled
        voiceAnnouncementIntervalValueText.alpha = if (isVoiceEnabled) 1.0f else 0.5f
    }

    private fun setupListeners() {
        // Set up listeners for battery optimization features
        activityRecognitionSwitch.setOnCheckedChangeListener { _, isChecked ->
            if (immediateUpdatesSwitch.isChecked) {
                settingsManager.setActivityRecognitionEnabled(isChecked)

                // If enabling Activity Recognition, check if permission is granted
                if (isChecked && Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    val mainActivity = requireActivity() as? MainActivity
                    if (mainActivity != null) {
                        // Check if permission is already granted
                        val hasPermission = ActivityCompat.checkSelfPermission(
                            requireContext(),
                            Manifest.permission.ACTIVITY_RECOGNITION
                        ) == PackageManager.PERMISSION_GRANTED

                        if (!hasPermission) {
                            // Request permission through MainActivity
                            mainActivity.requestActivityRecognitionPermissionFromSettings()
                        } else {
                            Toast.makeText(requireContext(),
                                "Smart location tracking enabled",
                                Toast.LENGTH_SHORT).show()
                        }
                    }
                } else {
                    Toast.makeText(requireContext(),
                        if (isChecked) "Smart location tracking enabled" else "Smart location tracking disabled",
                        Toast.LENGTH_SHORT).show()
                }
            } else {
                pendingChanges = true
            }
        }

        speedBasedFrequencySwitch.setOnCheckedChangeListener { _, isChecked ->
            if (immediateUpdatesSwitch.isChecked) {
                settingsManager.setSpeedBasedFrequencyEnabled(isChecked)
            } else {
                pendingChanges = true
            }
        }

        // Set up listeners for new navigation features
        realtimeUpdatesSwitch.setOnCheckedChangeListener { _, isChecked ->
            if (immediateUpdatesSwitch.isChecked) {
                settingsManager.setRealtimeDistanceUpdatesEnabled(isChecked)
            } else {
                pendingChanges = true
            }
        }

        keepScreenOnSwitch.setOnCheckedChangeListener { _, isChecked ->
            if (immediateUpdatesSwitch.isChecked) {
                settingsManager.setKeepScreenOnEnabled(isChecked)
            } else {
                pendingChanges = true
            }
        }

        voiceAnnouncementsSwitch.setOnCheckedChangeListener { _, isChecked ->
            if (immediateUpdatesSwitch.isChecked) {
                settingsManager.setVoiceAnnouncementsEnabled(isChecked)
            } else {
                pendingChanges = true
            }

            // Enable/disable the interval seekbar based on voice announcements setting
            voiceAnnouncementIntervalSeekBar.isEnabled = isChecked
            voiceAnnouncementIntervalValueText.alpha = if (isChecked) 1.0f else 0.5f
        }

        voiceAnnouncementIntervalSeekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                // Ensure minimum value of 50 meters
                val actualValue = if (progress < 50) 50 else progress
                updateVoiceAnnouncementIntervalText(actualValue)

                if (fromUser) {
                    if (immediateUpdatesSwitch.isChecked) {
                        settingsManager.setVoiceAnnouncementInterval(actualValue)
                    } else {
                        pendingChanges = true
                    }
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })

        movedUpdateSeekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                val minutes = progress + 1
                updateMoveText(minutes)

                if (fromUser) {
                    if (immediateUpdatesSwitch.isChecked) {
                        // Update setting in real-time
                        settingsManager.setMoveUpdate(minutes)
                        settingsViewModel.updateRadius(minutes)
                    } else {
                        // Mark that we have pending changes
                        pendingChanges = true
                    }
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {}

            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })
        radiusSeekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                val actualValue = if (progress < 1) 1 else progress
                updateRadiusText(actualValue)

                if (fromUser) {
                    if (immediateUpdatesSwitch.isChecked) {
                        // Update immediately
                        settingsManager.setUpdateRadius(actualValue)
                        settingsViewModel.updateRadius(actualValue)
                        //Toast.makeText(requireContext(), "Events will be updated with new radius", Toast.LENGTH_SHORT).show()
                    } else {
                        // Mark that we have pending changes
                        pendingChanges = true
                    }
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {}

            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })

        checkFrequencySeekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                val actualValue = if (progress < 5) 5 else progress
                updateFrequencyText(actualValue)

                if (fromUser) {
                    if (immediateUpdatesSwitch.isChecked) {
                        // Update setting in real-time
                        settingsManager.setCheckFrequency(actualValue)
                        settingsViewModel.updateCheckFrequency(actualValue)
                    } else {
                        // Mark that we have pending changes
                        pendingChanges = true
                    }
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {}

            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })

        eventExpirySeekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                val actualValue = if (progress < 1) 1 else progress
                updateExpiryText(actualValue)

                if (fromUser) {
                    if (immediateUpdatesSwitch.isChecked) {
                        // Update setting in real-time
                        settingsManager.setEventExpiry(actualValue)
                        settingsViewModel.updateEventExpiry(actualValue)

                        // Show toast about Events fragment update
                        Toast.makeText(requireContext(), "Events will be filtered with new expiry time", Toast.LENGTH_SHORT).show()
                    } else {
                        // Mark that we have pending changes
                        pendingChanges = true
                    }
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {}

            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })

        proximityThresholdSeekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                val threshold = progress / 10.0f
                updateProximityThresholdText(threshold)

                if (fromUser) {
                    if (immediateUpdatesSwitch.isChecked) {
                        // Update setting in real-time
                        settingsManager.setProximityThreshold(threshold)
                    } else {
                        // Mark that we have pending changes
                        pendingChanges = true
                    }
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {}

            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })

        notificationCooldownSeekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                // Calculate cooldown value between MIN and MAX
                val range = SettingsManager.MAX_NOTIFICATION_COOLDOWN - SettingsManager.MIN_NOTIFICATION_COOLDOWN
                val cooldownMs = SettingsManager.MIN_NOTIFICATION_COOLDOWN + (progress * range / 90)

                updateNotificationCooldownText(cooldownMs)
                if (fromUser) {
                    if (immediateUpdatesSwitch.isChecked) {
                        // Update setting in real-time
                        settingsManager.setNotificationCooldown(cooldownMs)
                    } else {
                        // Mark that we have pending changes
                        pendingChanges = true
                    }
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })

        // Add save button click listener
        view?.findViewById<Button>(R.id.save_settings_button)?.setOnClickListener {
            if (pendingChanges) {
                saveAllSettings()
                pendingChanges = false
                Toast.makeText(context, "Settings saved successfully", Toast.LENGTH_SHORT).show()
                applySettingsImmediately()
            }
        }
    }

    private fun updateRadiusText(value: Int) {
        radiusValueText.text = getString(R.string.radius_value_format, value)
    }

    private fun updateMoveText(value: Int) {
        movedUpdateValueText.text = getString(R.string.move_value_format, value)
    }

    private fun updateFrequencyText(value: Int) {
        checkFrequencyValueText.text = getString(R.string.frequency_value_format, value)
    }

    private fun updateExpiryText(value: Int) {
        eventExpiryValueText.text = getString(R.string.expiry_value_format, value)
    }

    private fun updateProximityThresholdText(threshold: Float) {
        proximityThresholdValueText.text = getString(R.string.proximity_threshold_format, threshold)
    }

    private fun updateVoiceAnnouncementIntervalText(value: Int) {
        voiceAnnouncementIntervalValueText.text = "$value meters"
    }

    private fun updateNotificationCooldownText(cooldownMs: Long) {
        val text = when {
            cooldownMs < 60000L -> "${cooldownMs / 1000} seconds"
            else -> String.format("%.1f minutes", cooldownMs / 60000f)
        }
        notificationCooldownValueText.text = text
    }

    private fun saveAllSettings() {
        settingsManager.setUpdateRadius(radiusSeekBar.progress)
        settingsManager.setCheckFrequency(checkFrequencySeekBar.progress)
        settingsManager.setEventExpiry(eventExpirySeekBar.progress)
        settingsManager.setMoveUpdate(movedUpdateSeekBar.progress)
        settingsManager.setProximityThreshold(proximityThresholdSeekBar.progress / 10.0f)

        val range = SettingsManager.MAX_NOTIFICATION_COOLDOWN - SettingsManager.MIN_NOTIFICATION_COOLDOWN
        val cooldownMs = SettingsManager.MIN_NOTIFICATION_COOLDOWN +
                (notificationCooldownSeekBar.progress * range / 90)
        settingsManager.setNotificationCooldown(cooldownMs)

        // Save battery optimization settings
        settingsManager.setActivityRecognitionEnabled(activityRecognitionSwitch.isChecked)
        settingsManager.setSpeedBasedFrequencyEnabled(speedBasedFrequencySwitch.isChecked)

        // Save new navigation feature settings
        settingsManager.setRealtimeDistanceUpdatesEnabled(realtimeUpdatesSwitch.isChecked)
        settingsManager.setKeepScreenOnEnabled(keepScreenOnSwitch.isChecked)
        settingsManager.setVoiceAnnouncementsEnabled(voiceAnnouncementsSwitch.isChecked)

        // Ensure minimum value of 50 meters for voice announcement interval
        val intervalValue = if (voiceAnnouncementIntervalSeekBar.progress < 50) 50 else voiceAnnouncementIntervalSeekBar.progress
        settingsManager.setVoiceAnnouncementInterval(intervalValue)
    }

    /**
     * Apply settings immediately to all relevant components
     */
    private fun applySettingsImmediately() {
        // Restart the ProximityService to apply new settings
        val intent = Intent(requireContext(), ProximityService::class.java).apply {
            action = "RESTART_SERVICE"
        }
        requireContext().startService(intent)

        // Show confirmation to user
        Toast.makeText(requireContext(), "Settings applied", Toast.LENGTH_SHORT).show()
    }
}
