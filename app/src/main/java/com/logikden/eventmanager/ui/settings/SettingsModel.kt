package com.logikden.eventmanager.ui.settings

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData

class SettingsViewModel : androidx.lifecycle.ViewModel() {
    // LiveData can be observed by other components if needed
    private val _updateRadius = MutableLiveData<Int>()
    val updateRadius: LiveData<Int> = _updateRadius

    private val _checkFrequency = MutableLiveData<Int>()
    val checkFrequency: LiveData<Int> = _checkFrequency

    private val _eventExpiry = MutableLiveData<Int>()
    val eventExpiry: LiveData<Int> = _eventExpiry

    private val _notificationsEnabled = MutableLiveData<Boolean>()
    val notificationsEnabled: LiveData<Boolean> = _notificationsEnabled

    private val _syncOnlyWifi = MutableLiveData<Boolean>()
    val syncOnlyWifi: LiveData<Boolean> = _syncOnlyWifi

    fun updateRadius(radius: Int) {
        _updateRadius.value = radius
    }

    fun updateCheckFrequency(frequency: Int) {
        _checkFrequency.value = frequency
    }

    fun updateEventExpiry(expiry: Int) {
        _eventExpiry.value = expiry
    }

    fun updateNotificationsEnabled(enabled: Boolean) {
        _notificationsEnabled.value = enabled
    }

    fun updateSyncOnlyWifi(onlyWifi: Boolean) {
        _syncOnlyWifi.value = onlyWifi
    }
}