package com.logikden.eventmanager.ui.splash

import android.app.Application
import android.location.Location
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.logikden.eventmanager.data.AppDatabase
import com.logikden.eventmanager.services.LocationService
import com.logikden.eventmanager.utils.LocationConstants
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class SplashViewModel(application: Application) : AndroidViewModel(application) {
    private val TAG = "SplashViewModel"

    private val _isInitialized = MutableLiveData<Boolean>()
    val isInitialized: LiveData<Boolean> = _isInitialized

    private val _locationStatus = MutableLiveData<LocationStatus>()
    val locationStatus: LiveData<LocationStatus> = _locationStatus

    private val _databaseStatus = MutableLiveData<DatabaseStatus>()
    val databaseStatus: LiveData<DatabaseStatus> = _databaseStatus

    private val locationService = LocationService.getInstance(application)
    private lateinit var database: AppDatabase

    // Timeout for location initialization (2 seconds)
    private val LOCATION_TIMEOUT = 2000L

    init {
        _isInitialized.value = false
        _locationStatus.value = LocationStatus.INITIALIZING
        _databaseStatus.value = DatabaseStatus.INITIALIZING
    }

    fun initialize() {
        initializeDatabase()
        initializeLocation()
    }

    /**
     * Force location status to READY, used when we detect a valid location on resume
     */
    fun forceLocationReady() {
        if (_locationStatus.value != LocationStatus.READY) {
            Log.d(TAG, "Forcing location status to READY from ${_locationStatus.value}")
            _locationStatus.value = LocationStatus.READY
            checkInitializationComplete()
        }
    }

    private fun initializeDatabase() {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                _databaseStatus.postValue(DatabaseStatus.LOADING)
                database = AppDatabase.getDatabase(getApplication(), viewModelScope)

                // Perform any necessary database operations
                val tileCount = database.tileDao().getCount()

                _databaseStatus.postValue(DatabaseStatus.READY)
                checkInitializationComplete()
            } catch (e: Exception) {
                _databaseStatus.postValue(DatabaseStatus.ERROR)
            }
        }
    }

    private fun initializeLocation() {
        _locationStatus.value = LocationStatus.INITIALIZING

        try {
            // Check if location permission is granted
            if (!locationService.checkLocationPermission()) {
                Log.d(TAG, "Location permission not granted")
                _locationStatus.value = LocationStatus.PERMISSION_DENIED
                return // Don't proceed further
            }

            // Check if location is enabled
            if (!locationService.isLocationEnabled()) {
                Log.d(TAG, "Location services disabled")
                _locationStatus.value = LocationStatus.ERROR
                return // Don't proceed further
            }

            // If location is enabled but not available yet, that's okay
            // We'll proceed and get location updates in the background

            // First check if we already have a valid location
            // For splash screen, we'll accept any location to proceed
            val currentLocation = locationService.getCurrentLocation()
            if (currentLocation != null) {
                Log.d(TAG, "Already have a location: $currentLocation with accuracy ${currentLocation.accuracy}m")
                _locationStatus.value = LocationStatus.READY
                checkInitializationComplete()
                return
            }

            // If we don't have a valid location yet, try to get one
            _locationStatus.value = LocationStatus.REQUESTING

            // Register a location callback
            val locationCallback = object : LocationService.LocationUpdateCallback {
                override fun onLocationReceived(location: Location) {
                    Log.d(TAG, "Location received: $location, accuracy: ${location.accuracy}m")
                    // For splash screen, we'll be more lenient with accuracy (100m instead of 10m)
                    // and we'll accept any location update to proceed
                    if (_locationStatus.value == LocationStatus.ERROR ||
                        _locationStatus.value == LocationStatus.REQUESTING ||
                        _locationStatus.value == LocationStatus.INITIALIZING) {
                        Log.d(TAG, "Accepting location update to proceed from ${_locationStatus.value} state")
                        _locationStatus.value = LocationStatus.READY
                        locationService.removeLocationCallback(this)
                        checkInitializationComplete()
                    } else {
                        Log.d(TAG, "Ignoring location update as status is already ${_locationStatus.value}")
                    }
                }

                override fun onLocationPermissionDenied() {
                    Log.d(TAG, "Location permission denied callback")
                    _locationStatus.value = LocationStatus.PERMISSION_DENIED
                    locationService.removeLocationCallback(this)
                    checkInitializationComplete()
                }

                override fun onLocationDisabled() {
                    Log.d(TAG, "Location disabled callback")
                    _locationStatus.value = LocationStatus.ERROR
                    locationService.removeLocationCallback(this)
                    checkInitializationComplete()
                }
            }

            locationService.addLocationCallback(locationCallback)

            // Request location updates with improved settings
            locationService.requestLocationUpdates(
                LocationConstants.UPDATE_INTERVAL,
                LocationConstants.FASTEST_INTERVAL,
                LocationConstants.MIN_DISTANCE_CHANGE
            )

            // Also try to get last known location
            locationService.getLastKnownLocation()

            // Set a shorter timeout to avoid waiting too long
            viewModelScope.launch {
                delay(LOCATION_TIMEOUT)
                if (_locationStatus.value == LocationStatus.REQUESTING ||
                    _locationStatus.value == LocationStatus.INITIALIZING) {
                    Log.d(TAG, "Location timeout reached, proceeding anyway")
                    // Proceed even if we don't have a location fix yet
                    // The app will continue to get location updates in the background
                    _locationStatus.value = LocationStatus.READY
                    locationService.removeLocationCallback(locationCallback)
                    checkInitializationComplete()
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error in location initialization", e)
            // Even if there's an error, let's proceed with initialization
            _locationStatus.value = LocationStatus.READY
            checkInitializationComplete()
        }
    }

    private fun checkInitializationComplete() {
        // Consider location ready if it's READY, or if there's an ERROR or PERMISSION_DENIED
        // We'll handle those states separately in the UI, but we should still proceed
        val locationReady = _locationStatus.value == LocationStatus.READY ||
                          _locationStatus.value == LocationStatus.ERROR ||
                          _locationStatus.value == LocationStatus.PERMISSION_DENIED

        val databaseReady = _databaseStatus.value == DatabaseStatus.READY

        Log.d(TAG, "Checking initialization: location=$locationReady (${_locationStatus.value}), database=$databaseReady (${_databaseStatus.value})")

        // Proceed if database is ready and location is either ready or has an error
        if (databaseReady && locationReady) {
            Log.d(TAG, "Initialization complete, proceeding to main activity")
            _isInitialized.postValue(true)
        }
    }

    enum class LocationStatus {
        INITIALIZING,
        REQUESTING,
        READY,
        PERMISSION_DENIED,
        ERROR
    }

    enum class DatabaseStatus {
        INITIALIZING,
        LOADING,
        READY,
        ERROR
    }
}
