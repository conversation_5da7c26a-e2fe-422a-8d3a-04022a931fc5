package com.logikden.eventmanager.ui.events

import android.app.Application
import android.location.Location
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.logikden.eventmanager.data.dto.GroupedEvent
import com.logikden.eventmanager.services.NetworkMonitor
import com.logikden.eventmanager.utils.LocationState
import com.logikden.eventmanager.utils.Resource
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import com.logikden.eventmanager.helper.DateHelper
import android.content.Context
import android.os.Build
import android.util.Log


import androidx.annotation.RequiresApi
import com.logikden.eventmanager.utils.SettingsManager
import com.logikden.eventmanager.utils.ErrorMessageUtils
import kotlinx.coroutines.flow.first

private const val UPDATE_INTERVAL = 1000L // 1 second for real-time updates
private const val DISTANCE_UPDATE_INTERVAL = 1000L // 1 second for distance updates
private const val NEARBY_THRESHOLD = 1.5f // 1.5 km
private const val TAG = "RemoteEventViewModel"



@RequiresApi(Build.VERSION_CODES.O)
class RemoteEventViewModel(
    private val application: Application,
    private val repository: RemoteEventRepository
) : ViewModel() {
    private val TAG = "RemoteEventViewModel"

    private val settingsManager = SettingsManager.getInstance(application)
    private val expiryTime: Int
        get() = settingsManager.getEventExpiry()

    private val _uiState = MutableStateFlow<Resource<List<GroupedEvent>>>(Resource.Loading)
    val uiState: StateFlow<Resource<List<GroupedEvent>>> = _uiState

    private val _locationState = MutableStateFlow<LocationState>(LocationState.Unavailable)
    val locationState: StateFlow<LocationState> = _locationState

    private val updateJobs = mutableMapOf<String, Job>()
    private val networkMonitor = NetworkMonitor(application, viewModelScope, repository)

    private var currentLocation: Location? = null
        set(value) {
            field = value
            value?.let { location ->
                repository.setCurrentLocation(location)
                _locationState.value = LocationState.Available(location)
                updateAllDistances()
            } ?: run {
                _locationState.value = LocationState.Unavailable
            }
        }

    init {
        loadGroupedReports()
    }

    fun setLocation(location: Location) {
        currentLocation = location
    }

    fun refreshData() {
        loadGroupedReports(forceRefresh = true)
    }

    @RequiresApi(Build.VERSION_CODES.O)
    private fun loadGroupedReports(forceRefresh: Boolean = false) {
        viewModelScope.launch {
            try {
                _uiState.value = Resource.Loading

                // Cancel existing update jobs
                cancelAllUpdateJobs()

                // Always try to get data from the local database first
                var hasLocalData = false
                try {
                    val localData = repository.eventRemoteReportDao.getAllReports().first()
                    hasLocalData = localData.isNotEmpty()
                    Log.d(TAG, "Local database has ${localData.size} reports")
                } catch (e: Exception) {
                    Log.e(TAG, "Error checking local data: ${e.message}")
                }

                // Refresh from remote if network is available or force refresh is requested
                if (forceRefresh && repository.hasLocation()) {
                    Log.d(TAG, "Force refresh requested")
                    if (networkMonitor.isNetworkAvailable()) {
                        Log.d(TAG, "Network available, refreshing reports")
                        repository.refreshReports()
                            .onFailure { exception ->
                                // Only show error if we don't have local data
                                if (!hasLocalData) {
                                    val userFriendlyMessage = ErrorMessageUtils.getUserFriendlyErrorMessage(
                                        exception as Exception
                                    )
                                    _uiState.value = Resource.Error(userFriendlyMessage)
                                    return@launch
                                } else {
                                    Log.e(TAG, "Failed to refresh reports but using cached data: ${exception.message}")
                                }
                            }
                    } else {
                        Log.d(TAG, "Network not available, using cached data")
                        // Only show error if we don't have local data
                        if (!hasLocalData) {
                            _uiState.value = Resource.Error("No network connection and no cached data available")
                            return@launch
                        }
                    }
                }

                // Collect from local database and filter by expiry time
                // This will trigger the repository to refresh if needed based on its internal logic
                repository.getGroupedReports().collect { groupedEvents ->
                    val processedEvents = processGroupedEvents(groupedEvents)
                    if (processedEvents.isEmpty() && !hasLocalData) {
                        // If we have no data to show and no local data, show an error
                        _uiState.value = Resource.Error("No reports available for your location")
                    } else {
                        _uiState.value = Resource.Success(processedEvents)
                        startUpdatesForEachReportType()
                    }
                }
            } catch (e: CancellationException) {
                // Don't show cancellation exceptions to users - they're expected when operations are cancelled
                Log.d(TAG, "Operation was cancelled")
                // Don't update UI state for cancellation - let the current state remain
            } catch (e: Exception) {
                Log.e(TAG, "Error loading grouped reports: ${e.message}", e)
                val userFriendlyMessage = ErrorMessageUtils.getUserFriendlyErrorMessage(e)
                _uiState.value = Resource.Error(userFriendlyMessage)
            }
        }
    }

    private fun processGroupedEvents(events: List<GroupedEvent>): List<GroupedEvent> {
        return events.map { group ->
            group.copy(
                events = group.events.filter { event ->
                    if (event.reportTime == null) {
                        // If no report time, assume it's valid
                        true
                    } else {
                        try {
                            // Calculate hours passed
                            // Note: event.reportTime should already be in local time format from the conversion in toEntity()
                            val hoursPassed = DateHelper.getTimePassedInHours(event.reportTime)
                            Log.d(TAG, "Report ${event.reportId}: time=${event.reportTime}, hours passed=$hoursPassed, expiry=$expiryTime")

                            // Also log the formatted time for debugging
                            val formattedTime = DateHelper.formatUtcTimeForDisplay(event.reportTime)
                            Log.d(TAG, "Report ${event.reportId} formatted time: $formattedTime")

                            hoursPassed <= expiryTime
                        } catch (e: Exception) {
                            Log.e(TAG, "Error calculating hours passed for report ${event.reportId}: ${e.message}")
                            // If there's an error, include the report to be safe
                            true
                        }
                    }
                }
            )
        }.filter { it.events.isNotEmpty() }
    }

    fun toggleGroupExpansion(reportName: String) {
        val currentGroups = (_uiState.value as? Resource.Success)?.data ?: return
        val updatedGroups = currentGroups.map { group ->
            if (group.eventType == reportName) {
                group.copy(isExpanded = !group.isExpanded)
            } else {
                group
            }
        }
        _uiState.value = Resource.Success(updatedGroups)
    }

    private fun startUpdatesForEachReportType() {
        (_uiState.value as? Resource.Success)?.data?.forEach { groupedEvent ->
            val reportName = groupedEvent.eventType
            updateJobs[reportName]?.cancel()

            val job = viewModelScope.launch {
                while (isActive) {
                    // Use the faster DISTANCE_UPDATE_INTERVAL for real-time distance updates
                    delay(DISTANCE_UPDATE_INTERVAL)
                    updateDistancesForReportType(reportName)

                    // Log that we're updating distances in real-time
                    //Log.d(TAG, "Real-time distance update for $reportName")
                }
            }
            updateJobs[reportName] = job
        }
    }

    fun updateAllDistances() {
        (_uiState.value as? Resource.Success)?.data?.forEach { groupedEvent ->
            updateDistancesForReportType(groupedEvent.eventType)
        }
    }

    private fun updateDistancesForReportType(reportName: String) {
        val location = currentLocation ?: return
        val currentGroups = (_uiState.value as? Resource.Success)?.data ?: return

        val updatedGroups = currentGroups.map { groupedEvent ->
            if (groupedEvent.eventType == reportName) {
                val updatedEvents = groupedEvent.events.map { event ->
                    val distance = calculateDistance(
                        location.latitude,
                        location.longitude,
                        event.latitude,
                        event.longitude
                    )
                    if (distance <= NEARBY_THRESHOLD && !event.isReached) {
                        event.copy(isReached = true)
                    } else {
                        event
                    }
                }
                if (updatedEvents != groupedEvent.events) {
                    groupedEvent.copy(events = updatedEvents)
                } else {
                    groupedEvent
                }
            } else {
                groupedEvent
            }
        }

        if (updatedGroups != currentGroups) {
            _uiState.value = Resource.Success(updatedGroups)
        }
    }

    private fun calculateDistance(
        lat1: Double,
        lon1: Double,
        lat2: Double,
        lon2: Double
    ): Float {
        val results = FloatArray(1)
        Location.distanceBetween(lat1, lon1, lat2, lon2, results)
        return results[0]  // Returns distance in meters
    }

    private fun cancelAllUpdateJobs() {
        updateJobs.values.forEach { it.cancel() }
        updateJobs.clear()
    }

    override fun onCleared() {
        super.onCleared()
        cancelAllUpdateJobs()
    }

    fun hasLoadedData(): Boolean {
        return uiState.value is Resource.Success
    }

    fun refreshEvents() {
        loadGroupedReports(forceRefresh = true)
    }
}
