package com.logikden.eventmanager.ui.debug

import android.os.Bundle
import android.widget.Button
import android.widget.ScrollView
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import com.logikden.eventmanager.R
import com.logikden.eventmanager.services.ActivityRecognitionManager
import com.logikden.eventmanager.services.LocationService
import com.google.android.gms.location.DetectedActivity
import android.util.Log
import android.widget.LinearLayout
import com.logikden.eventmanager.utils.LocationDebugUtils

/**
 * Debug activity for testing Activity Recognition API
 * Use this to diagnose issues with activity recognition and location updates
 */
class ActivityRecognitionDebugActivity : AppCompatActivity() {

    private lateinit var statusTextView: TextView
    private lateinit var refreshButton: Button
    private lateinit var forceStartButton: Button
    private lateinit var forceStopButton: Button
    private lateinit var scrollView: ScrollView

    private var activityRecognitionManager: ActivityRecognitionManager? = null
    private var locationService: LocationService? = null

    // Activity recognition callback for debugging
    private val debugCallback = object : ActivityRecognitionManager.ActivityRecognitionCallback {
        override fun onDrivingStarted() {
            Log.i("ActivityDebug", "🚗 Driving started")
            runOnUiThread { updateStatus() }
        }

        override fun onDrivingStopped() {
            Log.i("ActivityDebug", "🛑 Driving stopped")
            runOnUiThread { updateStatus() }
        }

        override fun onStationaryDetected() {
            Log.i("ActivityDebug", "🏠 Stationary detected")
            runOnUiThread { updateStatus() }
        }

        override fun onMovementDetected() {
            Log.i("ActivityDebug", "🚶 Movement detected")
            runOnUiThread { updateStatus() }
        }

        override fun onActivityChanged(activity: DetectedActivity) {
            Log.i("ActivityDebug", "📱 Activity changed: ${getActivityString(activity.type)} (${activity.confidence}%)")
            runOnUiThread { updateStatus() }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Create layout programmatically
        val layout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(32, 32, 32, 32)
        }

        // Title
        val titleTextView = TextView(this).apply {
            text = "Activity Recognition Debug"
            textSize = 20f
            setPadding(0, 0, 0, 32)
        }
        layout.addView(titleTextView)

        // Refresh button
        refreshButton = Button(this).apply {
            text = "Refresh Status"
            setOnClickListener { updateStatus() }
        }
        layout.addView(refreshButton)

        // Force start button
        forceStartButton = Button(this).apply {
            text = "Force Start Location Tracking"
            setOnClickListener { forceStartLocationTracking() }
        }
        layout.addView(forceStartButton)

        // Force stop button
        forceStopButton = Button(this).apply {
            text = "Force Stop Location Tracking"
            setOnClickListener { forceStopLocationTracking() }
        }
        layout.addView(forceStopButton)

        // Status text view in scroll view
        statusTextView = TextView(this).apply {
            textSize = 12f
            setPadding(16, 16, 16, 16)
            setBackgroundColor(0xFFF5F5F5.toInt())
        }

        scrollView = ScrollView(this).apply {
            addView(statusTextView)
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.MATCH_PARENT
            ).apply {
                topMargin = 32
            }
        }
        layout.addView(scrollView)

        setContentView(layout)

        // Initialize services
        initializeServices()

        // Initial status update
        updateStatus()
    }

    private fun initializeServices() {
        try {
            activityRecognitionManager = ActivityRecognitionManager.getInstance(this)
            activityRecognitionManager?.addCallback(debugCallback)

            locationService = LocationService.getInstance(this)

            Log.d("ActivityDebug", "Services initialized successfully")
        } catch (e: Exception) {
            Log.e("ActivityDebug", "Error initializing services", e)
        }
    }

    private fun updateStatus() {
        val status = buildString {
            appendLine("=== ACTIVITY RECOGNITION DEBUG ===")
            appendLine()

            // Activity Recognition Status
            activityRecognitionManager?.let { manager ->
                append(manager.getActivityRecognitionStatus())
            } ?: appendLine("❌ ActivityRecognitionManager not available")

            appendLine()
            appendLine("=== LOCATION SERVICE STATUS ===")

            // Location Service Status
            locationService?.let { service ->
                appendLine("- Location permission: ${service.checkLocationPermission()}")
                appendLine("- Location enabled: ${service.isLocationEnabled()}")
                appendLine("- Current location: ${service.getCurrentLocation()}")
                appendLine("- Location state: ${service.locationState.value}")
            } ?: appendLine("❌ LocationService not available")

            appendLine()
            appendLine("=== DETAILED LOCATION DEBUG ===")

            // Comprehensive location debugging
            append(LocationDebugUtils.getLocationDebugInfo(this@ActivityRecognitionDebugActivity))

            appendLine()
            appendLine("=== DEBUGGING TIPS ===")
            appendLine("1. Check if Activity Recognition permission is granted")
            appendLine("2. Try moving the device to simulate driving")
            appendLine("3. Use 'Force Start' to bypass activity recognition")
            appendLine("4. Check logcat for detailed logs with tag 'ActivityRecognitionManager'")
            appendLine("5. Verify location services are enabled in device settings")
            appendLine("6. Check if battery optimization is disabled for this app")
            appendLine()
            appendLine("Last updated: ${System.currentTimeMillis()}")
        }

        statusTextView.text = status

        // Scroll to bottom
        scrollView.post {
            scrollView.fullScroll(ScrollView.FOCUS_DOWN)
        }
    }

    private fun forceStartLocationTracking() {
        activityRecognitionManager?.forceStartLocationTracking()
        updateStatus()
    }

    private fun forceStopLocationTracking() {
        // Simulate stopping driving
        Log.i("ActivityDebug", "Force stopping location tracking")
        // This would need to be implemented in ActivityRecognitionManager
        updateStatus()
    }

    private fun getActivityString(activityType: Int): String {
        return when (activityType) {
            DetectedActivity.IN_VEHICLE -> "IN_VEHICLE"
            DetectedActivity.ON_BICYCLE -> "ON_BICYCLE"
            DetectedActivity.ON_FOOT -> "ON_FOOT"
            DetectedActivity.RUNNING -> "RUNNING"
            DetectedActivity.STILL -> "STILL"
            DetectedActivity.TILTING -> "TILTING"
            DetectedActivity.UNKNOWN -> "UNKNOWN"
            DetectedActivity.WALKING -> "WALKING"
            else -> "UNKNOWN($activityType)"
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        activityRecognitionManager?.removeCallback(debugCallback)
    }
}
