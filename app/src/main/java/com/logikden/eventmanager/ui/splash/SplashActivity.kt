package com.logikden.eventmanager.ui.splash

import android.Manifest
import android.app.AlertDialog
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import android.view.animation.AlphaAnimation
import android.view.animation.Animation
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModelProvider
import com.logikden.eventmanager.MainActivity
import com.logikden.eventmanager.R
import com.logikden.eventmanager.databinding.ActivitySplashBinding
import android.provider.Settings
import androidx.core.app.ActivityCompat
import com.logikden.eventmanager.services.LocationService

class SplashActivity : AppCompatActivity() {

    private lateinit var binding: ActivitySplashBinding
    private lateinit var viewModel: SplashViewModel
    private val minSplashDuration = 1500L // Minimum time to show splash screen (milliseconds)
    private val maxSplashDuration = 10000L // Maximum time to show splash screen (milliseconds)
    private val locationErrorTimeout = 3000L // Time to wait after location error before proceeding
    private var startTime: Long = 0
    private val TAG = "SplashActivity"

    private var currentDialog: AlertDialog? = null
    private var isDialogShowing = false
    private var pendingNavigation = false

    companion object {
        private const val LOCATION_PERMISSION_REQUEST_CODE = 1001
        private const val NOTIFICATION_PERMISSION_REQUEST_CODE = 1002
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // Hide the action bar
        supportActionBar?.hide()

        binding = ActivitySplashBinding.inflate(layoutInflater)
        setContentView(binding.root)
        startTime = System.currentTimeMillis()

        // Animate logo and text
        animateViews()

        // Initialize ViewModel
        viewModel = ViewModelProvider(this)[SplashViewModel::class.java]

        // Check permissions first
        checkAndRequestPermissions()

        setupObservers()

        // Set a hard timeout to proceed to main activity after maxSplashDuration
        Handler(Looper.getMainLooper()).postDelayed({
            if (!hasProceededToMain) {
                Log.d(TAG, "Splash screen timeout reached (${maxSplashDuration}ms), proceeding to MainActivity")
                proceedToMainActivity()
            }
        }, maxSplashDuration)
    }

    private fun setupObservers() {
        viewModel.locationStatus.observe(this) { status ->
            Log.d(TAG, "Location status changed to: $status")
            updateLoadingText(status, viewModel.databaseStatus.value)

            when (status) {
                SplashViewModel.LocationStatus.PERMISSION_DENIED -> {
                    // Show permission dialog and don't proceed until permission granted
                    showPermissionExplanationDialog()
                }
                SplashViewModel.LocationStatus.ERROR -> {
                    // Show location settings dialog and don't proceed until location enabled
                    showLocationSettingsDialog()
                }
                SplashViewModel.LocationStatus.READY -> {
                    // Location is ready, dismiss any dialogs and check if we can proceed
                    if (isDialogShowing) {
                        dismissCurrentDialog()
                    }
                    // Reset pending navigation flag since location is now ready
                    pendingNavigation = false
                    checkInitializationStatus()
                }
                SplashViewModel.LocationStatus.REQUESTING -> {
                    // Update UI to show we're actively requesting location
                    Log.d(TAG, "Actively requesting location...")
                }
                else -> {
                    // For other states (INITIALIZING), just wait
                    Log.d(TAG, "Initializing location services...")
                }
            }
        }

        viewModel.databaseStatus.observe(this) { status ->
            Log.d(TAG, "Database status changed to: $status")
            updateLoadingText(viewModel.locationStatus.value, status)

            // Only check initialization status if database is ready
            if (status == SplashViewModel.DatabaseStatus.READY) {
                checkInitializationStatus()
            }
        }

        viewModel.isInitialized.observe(this) { initialized ->
            if (initialized) {
                // Only proceed if no dialog is showing
                if (!isDialogShowing) {
                    proceedToMainActivity()
                } else {
                    // Set flag to proceed after dialog is dismissed
                    pendingNavigation = true
                }
            }
        }
    }

    private fun checkAndRequestPermissions() {
        when {
            // Check location permission
            !checkLocationPermission() -> {
                requestLocationPermission()
            }
            // Check notification permission for Android 13+
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU && !checkNotificationPermission() -> {
                requestNotificationPermission()
            }
            else -> {
                // All permissions granted, start initialization
                viewModel.initialize()
            }
        }
    }

    private fun checkLocationPermission(): Boolean {
        return checkSelfPermission(Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED
    }

    private fun checkNotificationPermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            checkSelfPermission(Manifest.permission.POST_NOTIFICATIONS) == PackageManager.PERMISSION_GRANTED
        } else true
    }

    private fun requestLocationPermission() {
        ActivityCompat.requestPermissions(
            this,
            arrayOf(
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.ACCESS_COARSE_LOCATION
            ),
            LOCATION_PERMISSION_REQUEST_CODE
        )
    }

    private fun requestNotificationPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ActivityCompat.requestPermissions(
                this,
                arrayOf(Manifest.permission.POST_NOTIFICATIONS),
                NOTIFICATION_PERMISSION_REQUEST_CODE
            )
        }
    }

    private fun showPermissionExplanationDialog() {
        dismissCurrentDialog()

        currentDialog = AlertDialog.Builder(this)
            .setTitle("Permissions Required")
            .setMessage("This app requires location and notification permissions to monitor nearby events and alert you when you're close to them.")
            .setPositiveButton("Grant Permissions") { _, _ ->
                isDialogShowing = false
                checkAndRequestPermissions()
                if (pendingNavigation) {
                    proceedToMainActivity()
                }
            }
            .setNegativeButton("Exit") { _, _ ->
                isDialogShowing = false
                finish()
            }
            .setCancelable(false)
            .create()

        currentDialog?.setOnShowListener {
            isDialogShowing = true
        }

        currentDialog?.show()
    }

    private fun showLocationSettingsDialog() {
        dismissCurrentDialog()

        // Check if location is actually disabled before showing the dialog
        val locationService = LocationService.getInstance(this)
        if (!locationService.isLocationEnabled()) {
            currentDialog = AlertDialog.Builder(this)
                .setTitle("Location Services Required")
                .setMessage("Please enable location services to use this app.")
                .setPositiveButton("Open Settings") { _, _ ->
                    isDialogShowing = false
                    // Open location settings but don't proceed to main activity yet
                    // We'll wait for onResume to check if location is enabled
                    startActivity(Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS))
                    // Set pendingNavigation flag to true so we know to check location on resume
                    pendingNavigation = true
                }
                .setNegativeButton("Exit") { _, _ ->
                    isDialogShowing = false
                    finish()
                }
                .setCancelable(false)
                .create()

            currentDialog?.setOnShowListener {
                isDialogShowing = true
            }

            currentDialog?.show()
        } else {
            // Location is already enabled, just proceed
            Log.d(TAG, "Location is already enabled, proceeding without showing dialog")
            viewModel.forceLocationReady()
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        when (requestCode) {
            LOCATION_PERMISSION_REQUEST_CODE -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    // Location permission granted, check notification permission
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU && !checkNotificationPermission()) {
                        requestNotificationPermission()
                    } else {
                        viewModel.initialize()
                    }
                } else {
                    showPermissionExplanationDialog()
                }
            }
            NOTIFICATION_PERMISSION_REQUEST_CODE -> {
                // Start initialization regardless of notification permission
                viewModel.initialize()
            }
        }
    }

    override fun onResume() {
        super.onResume()

        // Always check location status on resume
        Log.d(TAG, "onResume: Current location status is ${viewModel.locationStatus.value}")

        // Check if location is now enabled
        val locationService = LocationService.getInstance(this)

        if (pendingNavigation || viewModel.locationStatus.value == SplashViewModel.LocationStatus.ERROR) {
            Log.d(TAG, "Checking location status on resume")

            if (locationService.isLocationEnabled()) {
                Log.d(TAG, "Location is now enabled, proceeding")
                // Location is enabled, so we can proceed even if we don't have a fix yet
                viewModel.forceLocationReady()
            } else {
                Log.d(TAG, "Location is still disabled after returning from settings")
                // If still disabled, show the dialog again
                if (viewModel.locationStatus.value == SplashViewModel.LocationStatus.ERROR) {
                    showLocationSettingsDialog()
                }
            }
        } else if (viewModel.locationStatus.value == SplashViewModel.LocationStatus.REQUESTING ||
                   viewModel.locationStatus.value == SplashViewModel.LocationStatus.INITIALIZING) {
            // If we're still waiting for location, check if we already have a valid location
            val currentLocation = locationService.getCurrentLocation()
            if (currentLocation != null) {
                Log.d(TAG, "Found location on resume: $currentLocation")
                // Force status to READY to proceed
                viewModel.forceLocationReady()
            }
        }
    }

    private fun updateLoadingText(locationStatus: SplashViewModel.LocationStatus?, databaseStatus: SplashViewModel.DatabaseStatus?) {
        val loadingText = when {
            locationStatus == SplashViewModel.LocationStatus.REQUESTING -> "Getting location..."
            locationStatus == SplashViewModel.LocationStatus.PERMISSION_DENIED -> "Location permission denied"
            locationStatus == SplashViewModel.LocationStatus.ERROR -> "Location error"
            databaseStatus == SplashViewModel.DatabaseStatus.LOADING -> "Loading data..."
            databaseStatus == SplashViewModel.DatabaseStatus.ERROR -> "Database error"
            locationStatus == SplashViewModel.LocationStatus.READY && databaseStatus == SplashViewModel.DatabaseStatus.READY -> "Ready!"
            else -> "Initializing..."
        }

        Log.d(TAG, "Updating loading text to: $loadingText")
        binding.loadingText.text = loadingText
    }

    private fun animateViews() {
        // Fade in animation
        val fadeIn = AlphaAnimation(0.0f, 1.0f).apply {
            duration = 1000
            fillAfter = true
        }

        // Apply animations
        binding.splashLogo.startAnimation(fadeIn)

        // Delayed animation for text
        Handler(Looper.getMainLooper()).postDelayed({
            binding.appNameText.visibility = View.VISIBLE
            binding.appNameText.startAnimation(fadeIn)
        }, 300)

        Handler(Looper.getMainLooper()).postDelayed({
            binding.loadingIndicator.visibility = View.VISIBLE
            binding.loadingText.visibility = View.VISIBLE
            binding.loadingIndicator.startAnimation(fadeIn)
            binding.loadingText.startAnimation(fadeIn)
        }, 600)
    }

    // Flag to prevent multiple calls to MainActivity
    private var hasProceededToMain = false

    private fun proceedToMainActivity() {
        if (hasProceededToMain || isDialogShowing) return
        hasProceededToMain = true

        val elapsedTime = System.currentTimeMillis() - startTime

        // Calculate remaining time, ensuring we don't exceed maxSplashDuration
        val remainingTime = minOf(
            (minSplashDuration - elapsedTime).coerceAtLeast(0),
            (maxSplashDuration - elapsedTime).coerceAtLeast(0)
        )

        Log.d(TAG, "Proceeding to MainActivity after ${elapsedTime}ms, waiting additional ${remainingTime}ms")

        Handler(Looper.getMainLooper()).postDelayed({
            if (!isFinishing && !isDestroyed && !isDialogShowing) {
                Log.d(TAG, "Launching MainActivity now")
                val intent = Intent(this, MainActivity::class.java)
                startActivity(intent)
                finish()
                overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out)
            }
        }, remainingTime)
    }

    private fun dismissCurrentDialog() {
        currentDialog?.let {
            if (it.isShowing) {
                it.dismiss()
            }
        }
        currentDialog = null
        isDialogShowing = false
    }

    private fun checkInitializationStatus() {
        if (!isDialogShowing && viewModel.isInitialized.value == true) {
            proceedToMainActivity()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        dismissCurrentDialog()
    }
}
