package com.logikden.eventmanager.ui.events

import android.location.Location
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.navigation.findNavController
import androidx.recyclerview.widget.RecyclerView
import com.logikden.eventmanager.R
import com.logikden.eventmanager.data.EventRemoteReport
import com.logikden.eventmanager.helper.DateHelper
import com.logikden.eventmanager.services.LocationService
import android.util.Log
import android.widget.Toast
import androidx.constraintlayout.widget.ConstraintLayout
import com.logikden.eventmanager.utils.DistanceCalculator
import com.logikden.eventmanager.utils.ErrorMessageUtils
import com.logikden.eventmanager.utils.SpeedBasedUIManager
import com.logikden.eventmanager.utils.VoiceNavigationManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class EventDetailAdapter(
    private val events: List<EventRemoteReport>,
    var locationService: LocationService
) : RecyclerView.Adapter<EventDetailAdapter.EventDetailViewHolder>() {

    companion object {
        private const val PROXIMITY_THRESHOLD_KM = 1.5f // 1.5 kilometers for proximity grouping
        private const val TAG = "EventDetailAdapter"
    }

    // Track bound view holders for distance updates
    private val boundViewHolders = mutableSetOf<EventDetailViewHolder>()
    private val speedBasedUIManager = SpeedBasedUIManager.getInstance()

    // Location callback for real-time distance updates
    private val locationCallback = object : LocationService.LocationUpdateCallback {
        override fun onLocationReceived(location: Location) {
            // Only update distances if speed-based UI allows immediate updates
            // or if we're in high/medium speed categories
            if (speedBasedUIManager.shouldUpdateImmediately() && boundViewHolders.isNotEmpty()) {
                Log.v(TAG, "High-speed location update - updating ${boundViewHolders.size} event details immediately")
                boundViewHolders.forEach { holder ->
                    holder.updateDistance()
                    holder.updateSpeedDisplay()
                }
            }
            // For slower speeds, updates will be handled by the UI manager's scheduled updates
        }

        override fun onLocationPermissionDenied() {}
        override fun onLocationDisabled() {}
    }

    // UI update callback for speed-based updates
    private val uiUpdateCallback = object : SpeedBasedUIManager.UIUpdateCallback {
        override fun onUIUpdateRequired() {
            // Only update if we have bound view holders to avoid unnecessary processing
            if (boundViewHolders.isNotEmpty()) {
                Log.v(TAG, "Updating ${boundViewHolders.size} event detail items - ${speedBasedUIManager.getUpdateStatistics()}")

                // Update distances and speed displays for all bound view holders at speed-appropriate intervals
                boundViewHolders.forEach { holder ->
                    holder.updateDistance()
                    holder.updateSpeedDisplay()
                }
            }
        }

        override fun onSpeedCategoryChanged(category: LocationService.SpeedCategory, averageSpeed: Float) {
            Log.d(TAG, "Speed category changed to $category (${averageSpeed} km/h) - providing immediate responsive feedback")

            // Immediately update speed displays when category changes for responsive feedback
            if (boundViewHolders.isNotEmpty()) {
                boundViewHolders.forEach { holder ->
                    holder.updateSpeedDisplay()
                    holder.updateSpeedIndicatorVisibility()
                }
                Log.d(TAG, "Updated ${boundViewHolders.size} speed indicators immediately")
            }
        }
    }

    init {
        // Register for location updates
        locationService.addLocationCallback(locationCallback)

        // Register for speed-based UI updates
        speedBasedUIManager.registerUIComponent("EventDetailAdapter", uiUpdateCallback)
    }

    // Clean up when adapter is no longer needed
    fun cleanup() {
        locationService.removeLocationCallback(locationCallback)
        speedBasedUIManager.unregisterUIComponent("EventDetailAdapter")
        boundViewHolders.clear()
    }

    // Get the merged reports count from the report object
    private fun getMergedReportsCount(currentReport: EventRemoteReport): Int {
        // Return the merged reports count from the report object
        return currentReport.mergedReportsCount
    }

    // Calculate distance between two points in kilometers
    private fun calculateDistance(lat1: Double, lon1: Double, lat2: Double, lon2: Double): Float {
        val results = FloatArray(1)
        android.location.Location.distanceBetween(lat1, lon1, lat2, lon2, results)
        // Convert meters to kilometers
        return results[0] / 1000f
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): EventDetailViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_event_detail, parent, false)
        return EventDetailViewHolder(view)
    }

    override fun onBindViewHolder(holder: EventDetailViewHolder, position: Int) {
        val event = events[position]
        val mergedCount = getMergedReportsCount(event)
        holder.bind(event, locationService, mergedCount)

        // Track this view holder for distance updates
        boundViewHolders.add(holder)
    }

    override fun onViewRecycled(holder: EventDetailViewHolder) {
        super.onViewRecycled(holder)
        // Remove from tracking when view is recycled
        boundViewHolders.remove(holder)
    }

    override fun getItemCount(): Int = events.size

    class EventDetailViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private var isNavigating = false
        private var currentEvent: EventRemoteReport? = null
        private var currentLocationService: LocationService? = null

        // Speed indicator UI elements
        private val speedCategoryIndicator: TextView = itemView.findViewById(R.id.speedCategoryIndicator)
        private val speedCategoryText: TextView = itemView.findViewById(R.id.speedCategoryText)
        private val speedValueText: TextView = itemView.findViewById(R.id.speedValueText)

        fun bind(event: EventRemoteReport, locationService: LocationService, proximityCount: Int) {
            // Store references for distance updates
            currentEvent = event
            currentLocationService = locationService
            //itemView.findViewById<TextView>(R.id.eventDetailName).text = event.reportName

            // Handle time display
            // Note: event.reportTime should already be in local time format from the conversion in toEntity()
            val timeText = if (event.reportTime != null) {
                // Show both relative time (e.g., "2 hours ago") and actual date
                val relativeTime = DateHelper.getTimePassed(event.reportTime)
                //val formattedDate = DateHelper.formatUtcTimeForDisplay(event.reportTime)
                relativeTime
            } else {
                "Unknown time"
            }

            // Log the time conversion for debugging
            if (event.reportTime != null) {
                Log.d("EventDetailAdapter", "Report time: ${event.reportTime}, displayed as: $timeText")
            }

            itemView.findViewById<TextView>(R.id.eventDetailTime).text = timeText

            val container = itemView.findViewById<ConstraintLayout>(R.id.eventDetailContainer)

            // Calculate initial distance
            updateDistance()

            // Update initial speed display
            updateSpeedDisplay()

            // Show/hide speed indicator based on speed-based UI setting
            val speedBasedUIManager = SpeedBasedUIManager.getInstance()
            val speedIndicatorContainer = itemView.findViewById<View>(R.id.eventDetailSpeedIndicator)
            speedIndicatorContainer.visibility = if (speedBasedUIManager.isSpeedBasedUIEnabled.value) {
                View.VISIBLE
            } else {
                View.GONE
            }

            // Make the distance text larger and more prominent
            val distanceView = itemView.findViewById<TextView>(R.id.eventDetailDistance)
            distanceView.textSize = 16f // Larger text size

            // Set the proximity count badge
            val proximityBadge = itemView.findViewById<TextView>(R.id.proximityCountBadge)
            proximityBadge.text = proximityCount.toString()

            // Only show the badge if there are multiple reports in proximity
            proximityBadge.visibility = if (proximityCount > 1) View.VISIBLE else View.GONE

            container.setOnClickListener {
                if (isNavigating) {
                    Log.d("EventDetailAdapter", "Navigation already in progress, ignoring click")
                    return@setOnClickListener
                }
                isNavigating = true
                Log.d("EventDetailAdapter", "Attempting to navigate to event tracking for: ${event.reportName}")

                try {
                    // First check if the view is attached to a window
                    if (!itemView.isAttachedToWindow) {
                        Log.w("EventDetailAdapter", "View is not attached to window, cannot navigate")
                        isNavigating = false
                        return@setOnClickListener
                    }

                    // Then try to find the NavController
                    val navController = try {
                        itemView.findNavController()
                    } catch (e: Exception) {
                        Log.e("EventDetailAdapter", "Failed to find NavController", e)
                        // Don't show toast for navigation controller errors
                        isNavigating = false
                        return@setOnClickListener
                    }

                    // Create the navigation action
                    val action = EventsFragmentDirections.actionNavigationEventsToEventTrackingFragment(
                        eventLat = event.latitude.toFloat(),
                        eventLng = event.longitude.toFloat(),
                        eventName = event.reportName,
                        sourceFragment = "events",
                        reportTypeId = event.reportTypeId
                    )

                    // Try to navigate
                    try {
                        navController.navigate(action)
                        Log.d("EventDetailAdapter", "Successfully navigated to event tracking")
                    } catch (e: Exception) {
                        Log.e("EventDetailAdapter", "Navigation action failed", e)
                        // Only show toast for critical navigation errors, not for expected exceptions
                        val userFriendlyMessage = ErrorMessageUtils.getUserFriendlyErrorMessageIfShouldShow(e)
                        if (userFriendlyMessage != null) {
                            Toast.makeText(
                                itemView.context,
                                userFriendlyMessage,
                                Toast.LENGTH_SHORT
                            ).show()
                        }
                    }
                } catch (e: Exception) {
                    Log.e("EventDetailAdapter", "Unexpected navigation error", e)
                    // Don't show toast for general navigation errors
                    // Just log them for debugging purposes
                } finally {
                    // Reset navigation flag after a delay
                    itemView.postDelayed({
                        isNavigating = false
                        Log.d("EventDetailAdapter", "Navigation flag reset")
                    }, 1000)
                }
            }
        }

        // Method to update distance display when location changes
        fun updateDistance() {
            val event = currentEvent ?: return
            val locationService = currentLocationService ?: return
            val distanceView = itemView.findViewById<TextView>(R.id.eventDetailDistance)

            // Initially set to loading state
            distanceView.text = "Calculating..."

            // Get current location
            val currentLocation = locationService.getCurrentLocation()

            if (currentLocation != null) {
                // Use DistanceCalculator to get route-based distance asynchronously
                CoroutineScope(Dispatchers.Main).launch {
                    try {
                        // Try to get route-based distance first
                        val routeDistance = DistanceCalculator.calculateDistanceTo(
                            itemView.context,
                            currentLocation,
                            event.latitude,
                            event.longitude
                        )

                        if (routeDistance != null) {
                            // Update UI with route-based distance - always in kilometers
                            distanceView.text = LocationUtils.formatNavigationDistance(routeDistance, forceKilometers = true)
                        } else {
                            // Fall back to straight-line distance
                            val straightDistance = locationService.calculateDistanceTo(event.latitude, event.longitude)
                            distanceView.text = if (straightDistance != null) {
                                // Also display straight-line distance in kilometers for consistency
                                LocationUtils.formatNavigationDistance(straightDistance, forceKilometers = true)
                            } else {
                                "Unknown distance"
                            }
                        }
                    } catch (e: Exception) {
                        Log.e("EventDetailAdapter", "Error calculating distance", e)
                        // Fall back to straight-line distance on error
                        val straightDistance = locationService.calculateDistanceTo(event.latitude, event.longitude)
                        distanceView.text = if (straightDistance != null) {
                            LocationUtils.formatNavigationDistance(straightDistance, forceKilometers = true)
                        } else {
                            "Unknown distance"
                        }
                    }
                }
            } else {
                // No location available
                distanceView.text = "Location unavailable"
            }
        }

        /**
         * Update the speed indicator display with current speed information
         */
        fun updateSpeedDisplay() {
            val speedBasedUIManager = SpeedBasedUIManager.getInstance()
            val currentCategory = speedBasedUIManager.currentSpeedCategory.value
            val currentSpeed = speedBasedUIManager.currentAverageSpeed.value

            // Update speed category text
            val categoryName = when (currentCategory) {
                LocationService.SpeedCategory.STATIONARY -> "Still"
                LocationService.SpeedCategory.SLOW -> "Slow"
                LocationService.SpeedCategory.MEDIUM -> "Medium"
                LocationService.SpeedCategory.HIGH -> "Fast"
            }
            speedCategoryText.text = categoryName

            // Update speed value (compact format)
            speedValueText.text = "${String.format("%.0f", currentSpeed)}km/h"

            // Update indicator color and text color based on category
            val color = speedBasedUIManager.getSpeedCategoryColor(currentCategory)
            speedCategoryIndicator.setTextColor(color)
            speedCategoryText.setTextColor(color)
            speedValueText.setTextColor(color)

            // Make the indicator more visible for higher speeds
            when (currentCategory) {
                LocationService.SpeedCategory.STATIONARY -> {
                    speedCategoryIndicator.text = "●"
                    speedCategoryText.alpha = 0.7f
                    speedValueText.alpha = 0.7f
                }
                LocationService.SpeedCategory.SLOW -> {
                    speedCategoryIndicator.text = "●"
                    speedCategoryText.alpha = 0.8f
                    speedValueText.alpha = 0.8f
                }
                LocationService.SpeedCategory.MEDIUM -> {
                    speedCategoryIndicator.text = "●"
                    speedCategoryText.alpha = 1.0f
                    speedValueText.alpha = 1.0f
                }
                LocationService.SpeedCategory.HIGH -> {
                    speedCategoryIndicator.text = "●"
                    speedCategoryText.alpha = 1.0f
                    speedValueText.alpha = 1.0f
                }
            }
        }

        /**
         * Update speed indicator visibility based on settings
         */
        fun updateSpeedIndicatorVisibility() {
            val speedBasedUIManager = SpeedBasedUIManager.getInstance()
            val speedIndicatorContainer = itemView.findViewById<View>(R.id.eventDetailSpeedIndicator)
            speedIndicatorContainer.visibility = if (speedBasedUIManager.isSpeedBasedUIEnabled.value) {
                View.VISIBLE
            } else {
                View.GONE
            }
        }
    }
}