package com.logikden.eventmanager.ui.temporary

import android.Manifest
import android.content.pm.PackageManager
import android.location.Location
import android.os.Bundle
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.app.ActivityCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.google.android.gms.location.*
import com.logikden.eventmanager.data.AppDatabase
import com.logikden.eventmanager.data.LocationEntity
import com.logikden.eventmanager.databinding.FragmentAddLocationBinding

class AddLocationFragment : Fragment() {

    private var _binding: FragmentAddLocationBinding? = null
    private val binding get() = _binding!!

    private lateinit var viewModel: LocationViewModel
    private lateinit var fusedLocationClient: FusedLocationProviderClient
    private lateinit var locationCallback: LocationCallback

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentAddLocationBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val database = AppDatabase.getDatabase(requireContext(), lifecycleScope)
        val factory = LocationViewModelFactory(database,requireActivity().application)
        viewModel = ViewModelProvider(this, factory).get(LocationViewModel::class.java)

        fusedLocationClient = LocationServices.getFusedLocationProviderClient(requireActivity())

        // Initialize the adapter with an empty list

        binding.saveButton.setOnClickListener {
            val locationName = binding.locationNameEditText.text.toString()
            val reportType = "Road Point"

            if (locationName.isNotEmpty()) {
                getCurrentLocation { location ->
                    val locationEntity = LocationEntity(
                        name = locationName,
                        reportType = reportType,
                        latitude = location.latitude,
                        longitude = location.longitude
                    )
                    viewModel.insertLocation(locationEntity)
                    binding.statusTextView.text = "Location saved: $locationName, $reportType"
                    Toast.makeText(requireContext(), "Location saved!", Toast.LENGTH_SHORT).show()
                }
            } else {
                Toast.makeText(requireContext(), "Please enter a location name and select a report type", Toast.LENGTH_SHORT).show()
            }
        }

        startLocationUpdates()
    }


    private fun getCurrentLocation(callback: (Location) -> Unit) {
        if (ActivityCompat.checkSelfPermission(
                requireContext(),
                Manifest.permission.ACCESS_FINE_LOCATION
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            ActivityCompat.requestPermissions(
                requireActivity(),
                arrayOf(Manifest.permission.ACCESS_FINE_LOCATION),
                LOCATION_PERMISSION_REQUEST_CODE
            )
            return
        }

        fusedLocationClient.lastLocation.addOnSuccessListener { location ->
            location?.let {
                callback(it)
            }
        }
    }

    private fun startLocationUpdates() {
        val locationRequest = LocationRequest.create().apply {
            interval = 300000 // 5 minutes
            priority = LocationRequest.PRIORITY_HIGH_ACCURACY
        }

        locationCallback = object : LocationCallback() {
            override fun onLocationResult(locationResult: LocationResult) {
                for (location in locationResult.locations) {
                    val locationEntity = LocationEntity(
                        name = "Auto-Saved",
                        reportType = "Auto Collect",
                        latitude = location.latitude,
                        longitude = location.longitude
                    )
                    viewModel.insertLocation(locationEntity)
                    binding.statusTextView.text = "Auto-saved location: ${location.latitude}, ${location.longitude}"
                }
            }
        }

        if (ActivityCompat.checkSelfPermission(
                requireContext(),
                Manifest.permission.ACCESS_FINE_LOCATION
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            ActivityCompat.requestPermissions(
                requireActivity(),
                arrayOf(Manifest.permission.ACCESS_FINE_LOCATION),
                LOCATION_PERMISSION_REQUEST_CODE
            )
            return
        }

        fusedLocationClient.requestLocationUpdates(
            locationRequest,
            locationCallback,
            Looper.getMainLooper()
        )
    }

    override fun onDestroyView() {
        super.onDestroyView()
        fusedLocationClient.removeLocationUpdates(locationCallback)
        _binding = null
    }

    companion object {
        private const val LOCATION_PERMISSION_REQUEST_CODE = 1001
    }
}