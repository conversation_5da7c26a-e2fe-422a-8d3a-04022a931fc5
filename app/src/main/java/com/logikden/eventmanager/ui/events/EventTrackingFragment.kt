package com.logikden.eventmanager.ui.events
import androidx.fragment.app.Fragment
import com.logikden.eventmanager.databinding.FragmentEventTrackingBinding
import com.logikden.eventmanager.R
import com.logikden.eventmanager.services.LocationService
import org.osmdroid.tileprovider.tilesource.TileSourceFactory
import org.osmdroid.util.GeoPoint
import org.osmdroid.views.MapView
import org.osmdroid.views.overlay.Marker
import org.osmdroid.views.overlay.Polyline
import android.location.Location
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.navigation.fragment.navArgs
import org.osmdroid.config.Configuration
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.launch
import android.preference.PreferenceManager
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import android.Manifest
import android.content.pm.PackageManager
import com.logikden.eventmanager.utils.DistanceCalculator
import com.logikden.eventmanager.utils.LocationConstants
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import android.app.AlertDialog
import android.content.Intent
import android.provider.Settings
import com.logikden.eventmanager.services.routing.RoutingService
import com.logikden.eventmanager.services.routing.RouteInfo
import android.os.Handler
import android.os.Looper
import com.logikden.eventmanager.utils.SettingsManager
import com.logikden.eventmanager.utils.TextToSpeechManager
import com.logikden.eventmanager.utils.WakeLockManager
import com.logikden.eventmanager.services.NavigationService
import com.logikden.eventmanager.data.AppDatabase
import com.logikden.eventmanager.data.EventRemoteReport
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.runBlocking

class EventTrackingFragment : Fragment() {
    private var _binding: FragmentEventTrackingBinding? = null
    private val binding get() = _binding!!
    private lateinit var mapView: MapView
    private lateinit var locationService: LocationService
    private lateinit var routingService: RoutingService
    private lateinit var settingsManager: SettingsManager
    private lateinit var textToSpeechManager: TextToSpeechManager
    private lateinit var wakeLockManager: WakeLockManager
    private var currentLocationMarker: Marker? = null
    private var destinationMarker: Marker? = null
    private var routeLine: Polyline? = null
    private val routeLines = mutableListOf<Polyline>()
    private var isMapReady = false
    private var isFollowingUser = true
    private var currentRouteInfo: RouteInfo? = null

    // Real-time distance update variables
    private var realtimeUpdatesEnabled = true
    private var realtimeUpdateHandler: Handler? = null
    private var realtimeUpdateRunnable: Runnable? = null
    private val REALTIME_UPDATE_INTERVAL = 1000L // 1 second

    // Voice announcement variables
    private var voiceAnnouncementsEnabled = false
    private var voiceAnnouncementInterval = 100 // 100 meters
    private var lastAnnouncedDistance = -1

    // Keep screen on variable
    private var keepScreenOn = false

    // Navigation service variable
    private var navigationServiceStarted = false

    // Source fragment tracking
    private var sourceFragment: String? = null

    // Dialog prevention variables
    private var isLocationDialogShowing = false
    private var lastLocationCheckTime = 0L
    private val LOCATION_CHECK_COOLDOWN = 5000L // 5 seconds

    // Enhanced event tracking variables
    private var primaryEventMarker: Marker? = null
    private val nearbyEventMarkers = mutableListOf<Marker>()
    private var nearbyEvents = mutableListOf<EventRemoteReport>()
    private var proximityThreshold = 1000.0 // Default 1km in meters
    private var lastProximityCheckTime = 0L
    private val PROXIMITY_CHECK_INTERVAL = 5000L // 5 seconds
    private val alertedEvents = mutableSetOf<Int>() // Track which events we've already alerted for

    private val args: EventTrackingFragmentArgs by navArgs()

    companion object {
        private const val LOCATION_PERMISSION_REQUEST_CODE = 1001
        private const val LOCATION_SETTINGS_REQUEST_CODE = 1002
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // Initialize OSMDroid configuration early
        Configuration.getInstance().load(
            requireContext(),
            PreferenceManager.getDefaultSharedPreferences(requireContext())
        )
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        _binding = FragmentEventTrackingBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Initialize services
        locationService = LocationService.getInstance(requireContext())
        routingService = RoutingService.getInstance(requireContext())
        settingsManager = SettingsManager.getInstance(requireContext())
        textToSpeechManager = TextToSpeechManager.getInstance(requireContext())
        wakeLockManager = WakeLockManager.getInstance(requireContext())

        // Get the source fragment parameter
        sourceFragment = args.sourceFragment
        //Log.d("EventTrackingFragment", "Source fragment: $sourceFragment")

        // Load settings
        loadSettings()

        // Setup toolbar
        binding.toolbar.apply {
            title = args.eventName.uppercase()
            // Center align the title
            setTitleCentered(true)
        }

        // Apply keep screen on setting
        if (keepScreenOn) {
            wakeLockManager.setKeepScreenOn(requireActivity().window, true)
            wakeLockManager.acquireWakeLock()
        }

        // Initialize TTS if voice announcements are enabled
        if (voiceAnnouncementsEnabled) {
            textToSpeechManager.initialize()
        }

        // Start navigation service for persistent notification and voice navigation
        startNavigationService()

        // Initialize routing service
        routingService = RoutingService.getInstance(requireContext())

        viewLifecycleOwner.lifecycleScope.launch {
            initializeMap()
            setupLocationService()
            setupPrimaryEventMarker()
            loadNearbyEvents()
            setupMapControls()
            setupBottomPanel()
            isMapReady = true
        }
    }

    private fun initializeMap() {
        mapView = binding.mapView
        mapView.apply {
            setTileSource(TileSourceFactory.MAPNIK)
            setMultiTouchControls(true)
            setBuiltInZoomControls(true)

            // Set initial zoom and center on destination
            val destinationPoint = GeoPoint(args.eventLat.toDouble(), args.eventLng.toDouble())
            controller.apply {
                setZoom(15.0)
                setCenter(destinationPoint)
            }
        }
    }

    /**
     * Load settings from SettingsManager
     */
    private fun loadSettings() {
        realtimeUpdatesEnabled = settingsManager.isRealtimeDistanceUpdatesEnabled()
        keepScreenOn = settingsManager.isKeepScreenOnEnabled()
        voiceAnnouncementsEnabled = settingsManager.isVoiceAnnouncementsEnabled()
        voiceAnnouncementInterval = settingsManager.getVoiceAnnouncementInterval()
        proximityThreshold = settingsManager.getProximityThreshold() * 1000.0 // Convert km to meters

        //Log.d("EventTrackingFragment", "Loaded settings: realtimeUpdates=$realtimeUpdatesEnabled, " +

        // Set up real-time updates if enabled
        if (realtimeUpdatesEnabled) {
            setupRealtimeUpdates()
        }
    }

    /**
     * Set up real-time distance updates
     */
    private fun setupRealtimeUpdates() {
        realtimeUpdateHandler = Handler(Looper.getMainLooper())
        realtimeUpdateRunnable = object : Runnable {
            override fun run() {
                // Get current location
                val currentLocation = locationService.getCurrentLocation()
                if (currentLocation != null) {
                    // Update location on map
                    updateLocationOnMap(currentLocation)
                }

                // Schedule next update
                realtimeUpdateHandler?.postDelayed(this, REALTIME_UPDATE_INTERVAL)
            }
        }
    }

    private fun setupLocationService() {
        locationService = LocationService.getInstance(requireContext())
        locationService.addLocationCallback(object : LocationService.LocationUpdateCallback {
            override fun onLocationReceived(location: Location) {
                if (location.accuracy <= 20f) { // 20 meters accuracy threshold
                    // Only update immediately if real-time updates are disabled
                    // Otherwise, the real-time update handler will handle it
                    if (!realtimeUpdatesEnabled) {
                        updateLocationOnMap(location)
                    }
                }
            }

            override fun onLocationPermissionDenied() {
                // Use a safe way to show Toast that checks for null context
                showToastSafely("Location permission required")
                requestLocationPermission()
            }

            override fun onLocationDisabled() {
                // Show location settings dialog
                showLocationRequiredDialog()
            }
        })

        // Check location permission first
        if (locationService.checkLocationPermission()) {
            // Then check if location is enabled
            if (locationService.isLocationEnabled()) {
                // If both permission granted and location enabled, request updates
                locationService.requestLocationUpdates(
                    LocationConstants.UPDATE_INTERVAL,
                    LocationConstants.FASTEST_INTERVAL,
                    LocationConstants.MIN_DISTANCE_CHANGE
                )
            } else {
                // If permission granted but location disabled, show settings dialog
                showLocationRequiredDialog()
            }
        } else {
            // Request permissions if not granted
            requestLocationPermission()
        }
    }

    private fun requestLocationPermission() {
        requestPermissions(
            arrayOf(Manifest.permission.ACCESS_FINE_LOCATION),
            LOCATION_PERMISSION_REQUEST_CODE
        )
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        when (requestCode) {
            LOCATION_PERMISSION_REQUEST_CODE -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    locationService.requestLocationUpdates(
                        LocationConstants.UPDATE_INTERVAL,
                        LocationConstants.FASTEST_INTERVAL,
                        LocationConstants.MIN_DISTANCE_CHANGE
                    )
                } else {
                    Toast.makeText(context, "Location permission required", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    /**
     * Setup the primary event marker with distinct styling
     */
    private fun setupPrimaryEventMarker() {
        val primaryEventPoint = GeoPoint(args.eventLat.toDouble(), args.eventLng.toDouble())

        // Create primary event marker with larger, distinct icon
        primaryEventMarker = Marker(mapView).apply {
            position = primaryEventPoint
            title = "${args.eventName} (Primary Event)"
            setAnchor(Marker.ANCHOR_CENTER, Marker.ANCHOR_BOTTOM)

            // Use a larger, distinct icon for the primary event
            icon = ContextCompat.getDrawable(requireContext(), R.drawable.ic_destination_marker)?.apply {
                mutate()
                // Make it larger and use primary color
                setBounds(0, 0, intrinsicWidth * 2, intrinsicHeight * 2)
                setTint(ContextCompat.getColor(requireContext(), R.color.zatani_primary))
            }

            setInfoWindow(null)
        }

        // Keep the old destinationMarker reference for compatibility
        destinationMarker = primaryEventMarker

        mapView.overlays.add(primaryEventMarker)
        //Log.d("EventTrackingFragment", "Primary event marker set at ${primaryEventPoint.latitude}, ${primaryEventPoint.longitude}")
    }

    /**
     * Load nearby events of the same type within proximity radius
     */
    private suspend fun loadNearbyEvents() {
        try {
            val reportTypeId = args.reportTypeId
            if (reportTypeId <= 0) {
                //Log.d("EventTrackingFragment", "No valid report type ID provided, skipping nearby events")
                return
            }

            val database = AppDatabase.getDatabase(requireContext(), viewLifecycleOwner.lifecycleScope)
            val dao = database.remoteEventReportDao()

            // Get all events of the same type
            val allEventsOfType = dao.getReportsByType(reportTypeId).first()

            val primaryEventLat = args.eventLat.toDouble()
            val primaryEventLng = args.eventLng.toDouble()

            // Filter events within proximity radius (excluding the primary event)
            nearbyEvents = allEventsOfType.filter { event ->
                val distance = LocationUtils.calculateDistance(
                    primaryEventLat, primaryEventLng,
                    event.latitude, event.longitude
                ) * 1000 // Convert to meters

                // Include if within proximity radius and not the primary event
                distance <= proximityThreshold &&
                !(event.latitude == primaryEventLat && event.longitude == primaryEventLng)
            }.toMutableList()

            //Log.d("EventTrackingFragment", "Found ${nearbyEvents.size} nearby events of type $reportTypeId within ${proximityThreshold}m")

            // Add markers for nearby events
            withContext(Dispatchers.Main) {
                addNearbyEventMarkers()
            }

        } catch (e: Exception) {
            Log.e("EventTrackingFragment", "Error loading nearby events", e)
        }
    }

    /**
     * Add markers for nearby events with fire icon styling
     */
    private fun addNearbyEventMarkers() {
        // Clear existing nearby markers
        nearbyEventMarkers.forEach { mapView.overlays.remove(it) }
        nearbyEventMarkers.clear()

        nearbyEvents.forEach { event ->
            val eventPoint = GeoPoint(event.latitude, event.longitude)
            val isUserSubmitted = checkIfEventSubmittedByUser(event)

            val marker = Marker(mapView).apply {
                position = eventPoint
                title = if (isUserSubmitted) {
                    "${event.reportName} (Your Report)"
                } else {
                    "${event.reportName} (Nearby)"
                }
                setAnchor(Marker.ANCHOR_CENTER, Marker.ANCHOR_BOTTOM)

                // Use fire icon for all reported events
                icon = ContextCompat.getDrawable(requireContext(), R.drawable.ic_fire_marker)?.apply {
                    mutate()
                    // Keep the fire marker's original colors (orange/red with white flame)
                }

                setInfoWindow(null)
            }

            nearbyEventMarkers.add(marker)
            mapView.overlays.add(marker)

            //Log.d("EventTrackingFragment", "Added fire marker for event: ${event.reportName}")
        }

        mapView.invalidate()
        //Log.d("EventTrackingFragment", "Added ${nearbyEventMarkers.size} nearby event markers with fire icons")
    }

    /**
     * Check if an event was submitted by the current user
     * @param event The event to check
     * @return true if the event was submitted by the current user, false otherwise
     */
    private fun checkIfEventSubmittedByUser(event: EventRemoteReport): Boolean {
        return try {
            // Use a reasonable radius (1 km) and time window (2 hours) to find user-submitted reports
            val radiusKm = 1.0 // 1 kilometer
            val timeWindowMs = 2 * 60 * 60 * 1000L // 2 hours in milliseconds
            val currentTime = System.currentTimeMillis()
            val minTime = currentTime - timeWindowMs

            // Query the database for recent reports by this user with the same report type
            val database = AppDatabase.getDatabase(requireContext(), viewLifecycleOwner.lifecycleScope)
            val reportDao = database.pendingEventReportDao()

            // Get recent reports by type
            val recentReports = runBlocking(Dispatchers.IO) {
                reportDao.getRecentReportsByType(event.reportTypeId, minTime)
            }

            // Filter for reports owned by "me" (the current user)
            val userReports = recentReports.filter { it.owner == "me" }

            // Check if any of the user's reports are within the radius of this event
            userReports.any { userReport ->
                val distance = LocationUtils.calculateDistance(
                    event.latitude, event.longitude,
                    userReport.latitude, userReport.longitude
                )
                distance <= radiusKm
            }
        } catch (e: Exception) {
            Log.e("EventTrackingFragment", "Error checking if event was submitted by user", e)
            false
        }
    }

    private fun setupMapControls() {
        // Reset following mode when user interacts with map
        mapView.setOnTouchListener { _, _ ->
            isFollowingUser = false
            false // Don't consume the event
        }

        binding.recenterButton.setOnClickListener {
            isFollowingUser = true
            currentLocationMarker?.position?.let { position ->
                mapView.controller.animateTo(position)
            }
        }
    }

    private fun updateLocationOnMap(location: Location) {
        if (!isMapReady || !isAdded) return

        // Get the destination point
        val destinationPoint = GeoPoint(args.eventLat.toDouble(), args.eventLng.toDouble())

        viewLifecycleOwner.lifecycleScope.launch {
            try {
                val currentPoint = GeoPoint(location.latitude, location.longitude)

                // Update or create current location marker
                if (currentLocationMarker == null) {
                    currentLocationMarker = Marker(mapView).apply {
                        title = "Current Location"
                        setAnchor(Marker.ANCHOR_CENTER, Marker.ANCHOR_CENTER)
                        icon = ContextCompat.getDrawable(requireContext(), R.drawable.ic_navigation_arrow)?.apply {
                            mutate()
                            setTint(ContextCompat.getColor(requireContext(), R.color.teal_700))
                        }
                        mapView.overlays.add(this)
                    }
                }

                currentLocationMarker?.apply {
                    position = currentPoint
                    // Calculate bearing from current location to destination
                    val bearingToDestination = calculateBearing(
                        currentPoint.latitude, currentPoint.longitude,
                        destinationPoint.latitude, destinationPoint.longitude
                    )
                    rotation = bearingToDestination
                }

                // Calculate route-based distance using DistanceCalculator
                //Log.d("EventTrackingFragment", "Calculating route from (${location.latitude}, ${location.longitude}) to (${destinationPoint.latitude}, ${destinationPoint.longitude})")

                // Use the DistanceCalculator to get route-based distance
                val routeInfo = DistanceCalculator.calculateRouteDistance(
                    requireContext(),
                    location.latitude,
                    location.longitude,
                    destinationPoint.latitude,
                    destinationPoint.longitude
                )

                // Update the current route info
                currentRouteInfo = routeInfo

                //Log.d("EventTrackingFragment", "Route calculation result: $routeInfo")

                // Update route line with the calculated route points
                if (routeInfo != null) {
                    updateRouteLine(routeInfo.routePoints)

                    withContext(Dispatchers.Main) {
                        // Get formatted strings
                        val distanceStr = routeInfo.getFormattedDistance()
                        val durationStr = routeInfo.getFormattedDuration()

                        //Log.d("EventTrackingFragment", "Updating UI with distance: $distanceStr, duration: $durationStr")

                        // Update distance text - ensure we always show in km for home fragment
                        if (sourceFragment == "home") {
                            // Always show in kilometers for home fragment
                            val distanceKm = routeInfo.distanceKm
                            binding.distanceText.text = when {
                                distanceKm < 0.1 -> "0.1 km" // Minimum display value
                                distanceKm < 10 -> "%.1f km".format(distanceKm)
                                else -> "${distanceKm.toInt()} km"
                            }
                        } else {
                            binding.distanceText.text = distanceStr
                        }

                        // Announce distance if voice announcements are enabled
                        if (voiceAnnouncementsEnabled) {
                            announceDistance((routeInfo.distanceKm * 1000).toFloat())
                        }

                        // Update duration text
                        binding.durationText.text = durationStr

                        // Show or hide the fallback indicator
                        binding.fallbackIndicator.visibility = if (routeInfo.isFallback) View.VISIBLE else View.GONE

                        if (isFollowingUser) {
                            // Calculate bearing to destination for map orientation
                            val bearingToDestination = calculateBearing(
                                currentPoint.latitude, currentPoint.longitude,
                                destinationPoint.latitude, destinationPoint.longitude
                            )
                            mapView.controller.animateTo(
                                currentPoint,
                                mapView.zoomLevelDouble,
                                300L,
                                bearingToDestination
                            )
                        }

                        // Check proximity to nearby events
                        checkProximityToNearbyEvents(location)

                        mapView.invalidate()
                    }
                } else {
                    // Fallback to route-based distance calculation using LocationService
                    val routeDistance = locationService.calculateRouteDistanceTo(destinationPoint.latitude, destinationPoint.longitude)

                    withContext(Dispatchers.Main) {
                        if (routeDistance != null) {
                            // We got a route-based distance from LocationService
                            // Always force kilometers for consistency
                            val forceKm = true
                            binding.distanceText.text = LocationUtils.formatDistance(routeDistance, forceKilometers = forceKm)
                            binding.durationText.text = "--"
                            binding.fallbackIndicator.visibility = View.VISIBLE

                            // Announce distance if voice announcements are enabled
                            if (voiceAnnouncementsEnabled) {
                                announceDistance(routeDistance)
                            }
                        } else {
                            // Last resort: use straight-line distance
                            val straightDistance = locationService.calculateDistanceTo(destinationPoint.latitude, destinationPoint.longitude)

                            if (straightDistance != null) {
                                // Always force kilometers for consistency
                                val forceKm = true
                                binding.distanceText.text = LocationUtils.formatDistance(straightDistance, forceKilometers = forceKm)
                                binding.durationText.text = "--"
                                binding.fallbackIndicator.visibility = View.VISIBLE

                                // Announce distance if voice announcements are enabled
                                if (voiceAnnouncementsEnabled) {
                                    announceDistance(straightDistance)
                                }
                            } else {
                                binding.distanceText.text = "--"
                                binding.durationText.text = "--"
                                binding.fallbackIndicator.visibility = View.VISIBLE
                            }
                        }

                        // Update route line with straight line
                        updateRouteLine(listOf(currentPoint, destinationPoint))

                        if (isFollowingUser) {
                            // Calculate bearing to destination for map orientation
                            val bearingToDestination = calculateBearing(
                                currentPoint.latitude, currentPoint.longitude,
                                destinationPoint.latitude, destinationPoint.longitude
                            )
                            mapView.controller.animateTo(
                                currentPoint,
                                mapView.zoomLevelDouble,
                                300L,
                                bearingToDestination
                            )
                        }

                        // Check proximity to nearby events
                        checkProximityToNearbyEvents(location)

                        mapView.invalidate()
                    }
                }
            } catch (e: Exception) {
                Log.e("EventTrackingFragment", "Error updating location", e)
            }
        }
    }

    /**
     * Check proximity to nearby events and trigger alerts
     * Filters out user-submitted reports to avoid self-notifications
     */
    private fun checkProximityToNearbyEvents(currentLocation: Location) {
        val currentTime = System.currentTimeMillis()

        // Throttle proximity checks to avoid excessive processing
        if (currentTime - lastProximityCheckTime < PROXIMITY_CHECK_INTERVAL) {
            return
        }
        lastProximityCheckTime = currentTime

        nearbyEvents.forEach { event ->
            val distance = LocationUtils.calculateDistance(
                currentLocation.latitude, currentLocation.longitude,
                event.latitude, event.longitude
            ) * 1000 // Convert to meters

            // Check if this event was submitted by the user
            val isUserSubmitted = checkIfEventSubmittedByUser(event)

            // Only show alerts for events NOT submitted by the user
            if (!isUserSubmitted && distance <= proximityThreshold && !alertedEvents.contains(event.reportId)) {
                alertedEvents.add(event.reportId)
                showProximityAlert(event, distance.toDouble())
                //Log.d("EventTrackingFragment", "Proximity alert triggered for event ${event.reportName} at ${distance.toInt()}m")
            } else if (isUserSubmitted && distance <= proximityThreshold) {
                // Log that we're suppressing the alert for user-submitted report
                //Log.d("EventTrackingFragment", "Suppressing proximity alert for user-submitted report: ${event.reportName}")
            }

            // Reset alert if user moves far away (allows re-alerting if they return)
            if (distance > proximityThreshold * 1.5 && alertedEvents.contains(event.reportId)) {
                alertedEvents.remove(event.reportId)
                //Log.d("EventTrackingFragment", "Reset proximity alert for event ${event.reportName}")
            }
        }
    }

    /**
     * Show proximity alert for a nearby event
     */
    private fun showProximityAlert(event: EventRemoteReport, distance: Double) {
        try {
            val distanceText = if (distance < 1000) {
                "${distance.toInt()}m"
            } else {
                "%.1f km".format(distance / 1000)
            }

            // Create and show alert dialog
            val alertDialog = AlertDialog.Builder(requireContext())
                .setTitle("Nearby Event Alert")
                .setMessage("You are approaching: ${event.reportName}\nDistance: $distanceText")
                .setPositiveButton("Navigate") { _, _ ->
                    // Switch navigation to this event
                    switchToEvent(event)
                }
                .setNegativeButton("Dismiss") { dialog, _ ->
                    dialog.dismiss()
                }
                .setNeutralButton("View on Map") { _, _ ->
                    // Center map on this event
                    centerMapOnEvent(event)
                }
                .create()

            alertDialog.show()

            // Also announce via TTS if enabled
            if (voiceAnnouncementsEnabled) {
                announceDistance(distance.toFloat())
            }



        } catch (e: Exception) {
            Log.e("EventTrackingFragment", "Error showing proximity alert", e)
        }
    }

    /**
     * Switch navigation to a different event
     */
    private fun switchToEvent(event: EventRemoteReport) {
        try {
            // Update the destination marker to the new event
            destinationMarker?.let { mapView.overlays.remove(it) }

            val newDestinationPoint = GeoPoint(event.latitude, event.longitude)
            destinationMarker = Marker(mapView).apply {
                position = newDestinationPoint
                title = "${event.reportName} (New Destination)"
                setAnchor(Marker.ANCHOR_CENTER, Marker.ANCHOR_BOTTOM)
                icon = ContextCompat.getDrawable(requireContext(), R.drawable.ic_destination_marker)?.apply {
                    mutate()
                    setBounds(0, 0, intrinsicWidth * 2, intrinsicHeight * 2)
                    setTint(ContextCompat.getColor(requireContext(), R.color.zatani_primary))
                }
                setInfoWindow(null)
            }

            mapView.overlays.add(destinationMarker)

            // Update toolbar title
            binding.toolbar.title = event.reportName.uppercase()

            // Center map on new destination
            mapView.controller.animateTo(newDestinationPoint, 16.0, 1000L)

            // Clear route lines to force recalculation
            routeLines.forEach { mapView.overlays.remove(it) }
            routeLines.clear()
            routeLine?.let { mapView.overlays.remove(it) }
            routeLine = null

            mapView.invalidate()

            //Log.d("EventTrackingFragment", "Switched navigation to event: ${event.reportName}")

        } catch (e: Exception) {
            Log.e("EventTrackingFragment", "Error switching to event", e)
        }
    }

    /**
     * Center map on a specific event
     */
    private fun centerMapOnEvent(event: EventRemoteReport) {
        val eventPoint = GeoPoint(event.latitude, event.longitude)
        mapView.controller.animateTo(eventPoint, 17.0, 1000L)
        isFollowingUser = false
    }

    private fun updateRouteLine(routePoints: List<GeoPoint>) {
        // Remove existing route lines
        if (routeLine != null) {
            mapView.overlays.remove(routeLine)
            routeLine = null
        }

        // Clear and remove all existing route lines
        routeLines.forEach { mapView.overlays.remove(it) }
        routeLines.clear()

        // Get the current distance to destination in meters
        val distanceMeters = currentRouteInfo?.let {
            // Convert km to meters, ensure it's not zero to avoid display issues
            val meters = (it.distanceKm * 1000).toFloat()
            if (meters < 1f) 1f else meters // Minimum 1 meter to avoid "0 m" display
        } ?: locationService.calculateDistanceTo(args.eventLat.toDouble(), args.eventLng.toDouble())

        // If we have at least 2 points, create a gradient route
        if (routePoints.size >= 2) {
            // Create gradient route lines
            createGradientRouteLines(routePoints, distanceMeters)
        } else {
            // Fallback to a single line if we don't have enough points
            routeLine = createSingleColorRouteLine(routePoints, distanceMeters)
            mapView.overlays.add(routeLine)
        }
    }

    /**
     * Create a single-color route line with color based on distance
     * @param routePoints List of points that make up the route
     * @param distanceMeters Current distance to destination in meters
     * @return A Polyline with color based on distance
     */
    private fun createSingleColorRouteLine(routePoints: List<GeoPoint>, distanceMeters: Float?): Polyline {
        // Create a new polyline
        val polyline = Polyline(mapView)
        polyline.outlinePaint.strokeWidth = 8f
        polyline.outlinePaint.strokeCap = android.graphics.Paint.Cap.ROUND
        polyline.outlinePaint.strokeJoin = android.graphics.Paint.Join.ROUND
        polyline.outlinePaint.isAntiAlias = true

        // If we have a valid distance, create a color based on proximity
        if (distanceMeters != null) {
            try {
                // Get the color based on distance
                polyline.outlinePaint.color = getColorForDistance(distanceMeters)
                //Log.d("EventTrackingFragment", "Route color updated based on distance: $distanceMeters meters")
            } catch (e: Exception) {
                Log.e("EventTrackingFragment", "Error creating route color", e)
                // Fallback to default color
                polyline.outlinePaint.color = ContextCompat.getColor(requireContext(), R.color.colorRoute)
            }
        } else {
            // Default color if we don't have distance information
            polyline.outlinePaint.color = ContextCompat.getColor(requireContext(), R.color.colorRoute)
        }

        // Set the route points
        polyline.setPoints(routePoints)

        return polyline
    }

    /**
     * Create multiple polylines with gradient colors from green to red along the route
     * @param routePoints List of points that make up the route
     * @param totalDistanceMeters Total distance to destination in meters
     */
    private fun createGradientRouteLines(routePoints: List<GeoPoint>, totalDistanceMeters: Float?) {
        if (routePoints.size < 2 || totalDistanceMeters == null) {
            // Fallback to single line if we don't have enough points or distance info
            routeLine = createSingleColorRouteLine(routePoints, totalDistanceMeters)
            mapView.overlays.add(routeLine)
            return
        }

        try {
            // Calculate the total route length
            var routeLength = 0f
            for (i in 0 until routePoints.size - 1) {
                val results = FloatArray(1)
                Location.distanceBetween(
                    routePoints[i].latitude, routePoints[i].longitude,
                    routePoints[i + 1].latitude, routePoints[i + 1].longitude,
                    results
                )
                routeLength += results[0]
            }

            // Create segments with different colors
            var distanceCovered = 0f
            for (i in 0 until routePoints.size - 1) {
                val startPoint = routePoints[i]
                val endPoint = routePoints[i + 1]

                // Calculate segment length
                val results = FloatArray(1)
                Location.distanceBetween(
                    startPoint.latitude, startPoint.longitude,
                    endPoint.latitude, endPoint.longitude,
                    results
                )
                val segmentLength = results[0]

                // Calculate the distance from the end of this segment to the destination
                val distanceToDestination = totalDistanceMeters - (distanceCovered + segmentLength)

                // Create a polyline for this segment
                val segmentLine = Polyline(mapView)
                segmentLine.outlinePaint.strokeWidth = 8f
                segmentLine.outlinePaint.strokeCap = android.graphics.Paint.Cap.ROUND
                segmentLine.outlinePaint.strokeJoin = android.graphics.Paint.Join.ROUND
                segmentLine.outlinePaint.isAntiAlias = true

                // Set the color based on the distance to destination
                segmentLine.outlinePaint.color = getColorForDistance(distanceToDestination)

                // Set the points for this segment
                segmentLine.setPoints(listOf(startPoint, endPoint))

                // Add to the map and our list
                mapView.overlays.add(segmentLine)
                routeLines.add(segmentLine)

                // Update distance covered
                distanceCovered += segmentLength
            }

            //Log.d("EventTrackingFragment", "Created ${routeLines.size} gradient route segments")
        } catch (e: Exception) {
            Log.e("EventTrackingFragment", "Error creating gradient route lines", e)
            // Fallback to single line
            routeLine = createSingleColorRouteLine(routePoints, totalDistanceMeters)
            mapView.overlays.add(routeLine)
        }
    }

    /**
     * Get a color based on the distance to destination
     * @param distanceMeters Distance to destination in meters
     * @return Color for the given distance
     */
    private fun getColorForDistance(distanceMeters: Float): Int {
        // Ensure we have a valid distance (at least 1 meter)
        val safeDistance = if (distanceMeters < 1f) 1f else distanceMeters

        // Define proximity thresholds (in meters)
        val farThreshold = 2000f     // 2 km - fully green
        val midThreshold = 1000f      // 1 km - yellow
        val nearThreshold = 200f      // 200 m - orange
        // Below nearThreshold will be red

        // Get the colors
        val greenColor = ContextCompat.getColor(requireContext(), R.color.colorRouteGreen)
        val yellowColor = ContextCompat.getColor(requireContext(), R.color.colorRouteYellow)
        val orangeColor = ContextCompat.getColor(requireContext(), R.color.colorRouteOrange)
        val redColor = ContextCompat.getColor(requireContext(), R.color.colorRouteRed)

        // Calculate the color based on distance
        return when {
            safeDistance >= farThreshold -> greenColor
            safeDistance <= nearThreshold -> redColor
            safeDistance <= midThreshold -> {
                // Gradient between orange and yellow
                val ratio = (safeDistance - nearThreshold) / (midThreshold - nearThreshold)
                interpolateColor(orangeColor, yellowColor, ratio)
            }
            else -> {
                // Gradient between yellow and green
                val ratio = (safeDistance - midThreshold) / (farThreshold - midThreshold)
                interpolateColor(yellowColor, greenColor, ratio)
            }
        }
    }

    /**
     * Interpolate between two colors based on a ratio (0.0 to 1.0)
     * @param colorStart Starting color
     * @param colorEnd Ending color
     * @param ratio Ratio between the two colors (0.0 = start, 1.0 = end)
     * @return Interpolated color
     */
    private fun interpolateColor(colorStart: Int, colorEnd: Int, ratio: Float): Int {
        val clampedRatio = ratio.coerceIn(0f, 1f)

        val startA = (colorStart shr 24) and 0xff
        val startR = (colorStart shr 16) and 0xff
        val startG = (colorStart shr 8) and 0xff
        val startB = colorStart and 0xff

        val endA = (colorEnd shr 24) and 0xff
        val endR = (colorEnd shr 16) and 0xff
        val endG = (colorEnd shr 8) and 0xff
        val endB = colorEnd and 0xff

        val a = startA + (clampedRatio * (endA - startA)).toInt()
        val r = startR + (clampedRatio * (endR - startR)).toInt()
        val g = startG + (clampedRatio * (endG - startG)).toInt()
        val b = startB + (clampedRatio * (endB - startB)).toInt()

        return (a shl 24) or (r shl 16) or (g shl 8) or b
    }

    private fun setupBottomPanel() {
        // Make sure the bottom panel has all the necessary views
        binding.fallbackIndicator.visibility = View.GONE
    }

    /**
     * Announce the distance using Text-to-Speech
     */
    private fun announceDistance(distanceInMeters: Float) {
        if (!voiceAnnouncementsEnabled || !isAdded) return

        textToSpeechManager.announceDistance(distanceInMeters, voiceAnnouncementInterval)
    }



    override fun onResume() {
        super.onResume()
        mapView.onResume()

        // Start real-time updates if enabled
        if (realtimeUpdatesEnabled && realtimeUpdateRunnable != null) {
            realtimeUpdateHandler?.postDelayed(realtimeUpdateRunnable!!, REALTIME_UPDATE_INTERVAL)
        }

        // Apply keep screen on setting
        if (keepScreenOn) {
            wakeLockManager.setKeepScreenOn(requireActivity().window, true)
            wakeLockManager.acquireWakeLock()
        }

        // Only check location if we don't already have a working location service
        if (!isLocationServiceWorking()) {
            //Log.d("EventTrackingFragment", "Location service not working, setting up...")
            setupLocationService()
        } else {
            // If location service is already working, just ensure updates are active
            locationService.requestLocationUpdates(
                LocationConstants.UPDATE_INTERVAL,
                LocationConstants.FASTEST_INTERVAL,
                LocationConstants.MIN_DISTANCE_CHANGE
            )
            //Log.d("EventTrackingFragment", "Location service already working, resumed updates")
        }
    }

    override fun onPause() {
        super.onPause()
        mapView.onPause()
        // Stop location updates when fragment is paused
        locationService.stopLocationUpdates()

        // Stop real-time updates
        realtimeUpdateHandler?.removeCallbacks(realtimeUpdateRunnable ?: return)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        //Log.d("EventTrackingFragment", "onDestroyView called")
        isMapReady = false

        try {
            // Stop location updates
            locationService.stopLocationUpdates()
            //Log.d("EventTrackingFragment", "Stopped location updates")

            // Safely remove location callbacks
            if (locationService.locationCallbacks.isNotEmpty()) {
                try {
                    locationService.removeLocationCallback(locationService.locationCallbacks.first())
                    //Log.d("EventTrackingFragment", "Removed location callback")
                } catch (e: Exception) {
                    Log.e("EventTrackingFragment", "Error removing location callback: ${e.message}")
                }
            }

            // Clean up resources
            if (realtimeUpdateHandler != null && realtimeUpdateRunnable != null) {
                realtimeUpdateHandler?.removeCallbacks(realtimeUpdateRunnable!!)
                realtimeUpdateHandler = null
                realtimeUpdateRunnable = null
                //Log.d("EventTrackingFragment", "Cleaned up real-time update resources")
            }

            // Release wake lock if acquired
            if (keepScreenOn) {
                wakeLockManager.setKeepScreenOn(requireActivity().window, false)
                wakeLockManager.releaseWakeLock()
                //Log.d("EventTrackingFragment", "Released wake lock")
            }

            // Shutdown TTS if initialized
            if (voiceAnnouncementsEnabled) {
                textToSpeechManager.shutdown()
                //Log.d("EventTrackingFragment", "Shut down text-to-speech")
            }

            // Stop navigation service using stopService
            if (navigationServiceStarted) {
                try {
                    // Use stopService directly
                    val intent = Intent(requireContext(), NavigationService::class.java)
                    requireContext().stopService(intent)
                    navigationServiceStarted = false
                    //Log.d("EventTrackingFragment", "Stopped navigation service")
                } catch (e: Exception) {
                    Log.e("EventTrackingFragment", "Error stopping navigation service: ${e.message}")
                }
            }

            // Clean up route lines
            if (routeLine != null) {
                mapView.overlays.remove(routeLine)
                routeLine = null
            }

            // Clean up gradient route lines
            routeLines.forEach { mapView.overlays.remove(it) }
            routeLines.clear()

            // Clean up nearby event markers
            nearbyEventMarkers.forEach { mapView.overlays.remove(it) }
            nearbyEventMarkers.clear()
            nearbyEvents.clear()
            alertedEvents.clear()

            //Log.d("EventTrackingFragment", "Cleaned up route lines and nearby event markers")

            mapView.onDetach()
            //Log.d("EventTrackingFragment", "Detached map view")
        } catch (e: Exception) {
            Log.e("EventTrackingFragment", "Error in onDestroyView: ${e.message}")
        } finally {
            _binding = null
            //Log.d("EventTrackingFragment", "Cleared binding reference")
        }
    }

    /**
     * Start the navigation service
     */

//    private fun startNavigationService() {
//        if (navigationServiceStarted) return
//
//        // Start the navigation service with destination details
//        NavigationService.startNavigation(
//            requireContext(),
//            args.eventName,
//            args.eventLat.toDouble(),
//            args.eventLng.toDouble()
//        )
//
//        navigationServiceStarted = true
//        //Log.d("EventTrackingFragment", "Started navigation service")
//    }

    /**
     * Stop the navigation service
     */
    private fun stopNavigationService() {
        if (!navigationServiceStarted) {
            //Log.d("EventTrackingFragment", "Navigation service not started, nothing to stop")
            return
        }

        try {
            // Use a regular Intent instead of the companion method
            val intent = Intent(requireContext(), NavigationService::class.java)
            requireContext().stopService(intent)
            //Log.d("EventTrackingFragment", "Sent stopService command to navigation service")
        } catch (e: Exception) {
            Log.e("EventTrackingFragment", "Error stopping navigation service: ${e.message}")
        } finally {
            // Mark as stopped regardless of success to prevent further attempts
            navigationServiceStarted = false
        }
    }

    /**
     * Handle exit navigation button click
     * This provides a safe way to exit navigation and return to the previous screen
     */
    private fun exitNavigation() {
        try {
            // Simply navigate back immediately
            findNavController().popBackStack()
            //Log.d("EventTrackingFragment", "Exited navigation")

            // The onDestroyView method will handle cleanup
        } catch (e: Exception) {
            Log.e("EventTrackingFragment", "Error exiting navigation: ${e.message}")
            // Try to force exit if navigation fails
            try {
                requireActivity().onBackPressed()
            } catch (e2: Exception) {
                Log.e("EventTrackingFragment", "Failed to exit navigation: ${e2.message}")
            }
        }
    }

    /**
     * Check if location service is working properly
     */
    private fun isLocationServiceWorking(): Boolean {
        return locationService.checkLocationPermission() &&
               locationService.isLocationEnabled() &&
               locationService.getCurrentLocation() != null
    }

    /**
     * Shows a dialog when location is required but disabled
     */
    private fun showLocationRequiredDialog() {
        if (!isAdded || isDetached || isLocationDialogShowing) return

        // Prevent showing dialog too frequently
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastLocationCheckTime < LOCATION_CHECK_COOLDOWN) {
            //Log.d("EventTrackingFragment", "Location dialog cooldown active, skipping")
            return
        }

        lastLocationCheckTime = currentTime
        isLocationDialogShowing = true

        try {
            //Log.d("EventTrackingFragment", "Showing location required dialog")
            AlertDialog.Builder(requireContext())
                .setTitle("Location Required")
                .setMessage("This feature requires location services to be enabled. Please enable location in your device settings.")
                .setPositiveButton("Open Settings") { dialog, _ ->
                    dialog.dismiss()
                    isLocationDialogShowing = false
                    // Open device location settings
                    val intent = Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS)
                    startActivity(intent)
                }
                .setNegativeButton("Cancel") { dialog, _ ->
                    dialog.dismiss()
                    isLocationDialogShowing = false
                }
                .setOnDismissListener {
                    isLocationDialogShowing = false
                }
                .setCancelable(false)
                .show()
        } catch (e: Exception) {
            //Log.e("EventTrackingFragment", "Error showing location dialog: ${e.message}")
            isLocationDialogShowing = false
        }
    }

    /**
     * Safely shows a Toast message, checking for null context and using the application context if needed
     */
    private fun showToastSafely(message: String) {
        try {
            val ctx = context ?: return // Return early if context is null
            if (isAdded && !isDetached) { // Check if fragment is still attached
                Toast.makeText(ctx.applicationContext, message, Toast.LENGTH_SHORT).show()
            }
        } catch (e: Exception) {
            // Log the error but don't crash
            Log.e("EventTrackingFragment", "Error showing toast: ${e.message}")
        }
    }

    /**
     * Calculate bearing from one point to another
     * @param startLat Starting latitude
     * @param startLng Starting longitude
     * @param endLat Ending latitude
     * @param endLng Ending longitude
     * @return Bearing in degrees (0-360)
     */
    private fun calculateBearing(startLat: Double, startLng: Double, endLat: Double, endLng: Double): Float {
        val lat1 = Math.toRadians(startLat)
        val lat2 = Math.toRadians(endLat)
        val deltaLng = Math.toRadians(endLng - startLng)

        val y = kotlin.math.sin(deltaLng) * kotlin.math.cos(lat2)
        val x = kotlin.math.cos(lat1) * kotlin.math.sin(lat2) -
                kotlin.math.sin(lat1) * kotlin.math.cos(lat2) * kotlin.math.cos(deltaLng)

        val bearing = Math.toDegrees(kotlin.math.atan2(y, x))

        // Normalize to 0-360 degrees
        return ((bearing + 360) % 360).toFloat()
    }
}