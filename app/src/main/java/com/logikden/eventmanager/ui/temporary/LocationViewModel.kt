package com.logikden.eventmanager.ui.temporary
import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.viewModelScope
import com.logikden.eventmanager.data.AppDatabase
import com.logikden.eventmanager.data.LocationEntity
import com.logikden.eventmanager.data.LocationRepository
import kotlinx.coroutines.launch

class LocationViewModel(appDb: AppDatabase,application: Application) : AndroidViewModel(application) {
    private val repository: LocationRepository = LocationRepository(appDb.locationDao())

    val allLocations: LiveData<List<LocationEntity>> = repository.allLocations

    fun insertLocation(location: LocationEntity) {
        viewModelScope.launch {
            repository.insertLocation(location)
        }
    }
}