package com.logikden.eventmanager.ui.events

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.logikden.eventmanager.R
import com.logikden.eventmanager.data.EventRemoteReport
import com.logikden.eventmanager.data.dto.GroupedEvent

class EventTypeAdapter(
    private val groupedEvents: List<GroupedEvent>,
    private val onEventTypeClick: (ViewHolder, List<EventRemoteReport>) -> Unit
) : RecyclerView.Adapter<EventTypeAdapter.ViewHolder>() {

    companion object {
        private const val PROXIMITY_THRESHOLD_KM = 1.5f // 1.5 kilometers for proximity grouping
    }

    // Calculate the total number of reports in proximity groups for a given event type
    private fun calculateTotalReportsInProximity(events: List<EventRemoteReport>): Int {
        // Simply sum up the mergedReportsCount for each event
        return events.sumOf { it.mergedReportsCount }
    }

    // Calculate distance between two points in kilometers
    private fun calculateDistance(lat1: Double, lon1: Double, lat2: Double, lon2: Double): Float {
        val results = FloatArray(1)
        android.location.Location.distanceBetween(lat1, lon1, lat2, lon2, results)
        // Convert meters to kilometers
        return results[0] / 1000f
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_event_type, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val groupedEvent = groupedEvents[position]
        val totalReportsCount = calculateTotalReportsInProximity(groupedEvent.events)
        holder.bind(groupedEvent, totalReportsCount)

        // If the item is expanded, trigger the callback to load the details
        if (groupedEvent.isExpanded && holder.recyclerView.visibility == View.VISIBLE) {
            onEventTypeClick(holder, groupedEvent.events)
        }

        holder.itemView.setOnClickListener {
            if (holder.recyclerView.visibility == View.VISIBLE) {
                holder.recyclerView.visibility = View.GONE
            } else {
                holder.recyclerView.visibility = View.VISIBLE
                onEventTypeClick(holder, groupedEvent.events)
            }
        }
    }

    override fun getItemCount(): Int {
        return groupedEvents.size
    }

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val eventTypeTextView: TextView = itemView.findViewById(R.id.eventTypeName)
        private val eventCountTextView: TextView = itemView.findViewById(R.id.eventCount)
        val recyclerView: RecyclerView = itemView.findViewById(R.id.eventDetails)

        fun bind(groupedEvent: GroupedEvent, totalReportsCount: Int) {
            eventTypeTextView.text = groupedEvent.eventType
            // Show the total reports count instead of just the number of event instances
            eventCountTextView.text = "$totalReportsCount"
            recyclerView.visibility = if (groupedEvent.isExpanded) View.VISIBLE else View.GONE
        }
    }
}