package com.logikden.eventmanager.ui.temporary

import android.app.Application
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.logikden.eventmanager.data.AppDatabase

class LocationViewModelFactory(
    private val appDb: AppDatabase,
    var application: Application
) : ViewModelProvider.Factory {

    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(LocationViewModel::class.java)) {
            return LocationViewModel(appDb,application) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class")
    }
}