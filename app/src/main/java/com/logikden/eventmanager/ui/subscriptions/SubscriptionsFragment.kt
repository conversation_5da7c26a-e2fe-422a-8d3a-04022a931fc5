package com.logikden.eventmanager.ui.subscriptions
import android.app.AlertDialog
import android.content.Context
import android.content.res.ColorStateList
import android.graphics.Rect
import android.location.LocationListener
import android.location.LocationManager
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.util.TypedValue
import android.view.Gravity
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.Toast
import android.widget.ImageView
import androidx.core.content.ContextCompat
import com.google.android.material.card.MaterialCardView
import androidx.core.view.MenuProvider
import androidx.core.view.children
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey
import com.google.android.material.button.MaterialButton
import com.logikden.eventmanager.R
import com.logikden.eventmanager.data.AppDatabase
import com.logikden.eventmanager.data.EventRemoteReportDao
import com.logikden.eventmanager.data.EventReportDao
import com.logikden.eventmanager.data.Tile
import com.logikden.eventmanager.data.TileDao
import com.logikden.eventmanager.data.dto.ReportType
import com.logikden.eventmanager.data.dto.SubscriptionItem
import com.logikden.eventmanager.data.dto.SubscriptionRequest
import com.logikden.eventmanager.databinding.FragmentSubscriptionsBinding
import com.logikden.eventmanager.services.RetrofitClient
import com.logikden.eventmanager.ui.home.EventReportRepository
import com.logikden.eventmanager.ui.home.EventViewModel
import com.logikden.eventmanager.ui.home.LocalEventViewModelFactory
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.Locale


class SubscriptionsFragment : Fragment() {

    private var _binding: FragmentSubscriptionsBinding? = null
    private val binding get() = _binding!!

    private lateinit var subscriptionsContainer: LinearLayout
    private lateinit var locationManager: LocationManager
    private var locationListener: LocationListener? = null

    private var listener: OnCheckTilesListener? = null

    private lateinit var database: AppDatabase
    private lateinit var tileDao: TileDao
    private lateinit var reportDao: EventReportDao
    private lateinit var remoteReportDao: EventRemoteReportDao

    private var clickStates = mutableMapOf<Int, ButtonState>()

    private var clickedButton: MaterialButton? = null

    private lateinit var viewModel: EventViewModel

    private val apiService = RetrofitClient.instance

    private lateinit var eventRepository: EventReportRepository

    private var tiles = mutableListOf<Tile>()

    enum class ButtonState {
        UNSUBSCRIBED, SUBSCRIBED, FAVORITE
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentSubscriptionsBinding.inflate(inflater, container, false)

        // Initialize database
        database = AppDatabase.getDatabase(requireContext(), lifecycleScope)
        tileDao = database.tileDao()
        reportDao= database.pendingEventReportDao()
        remoteReportDao= database.remoteEventReportDao()

        // Initialize viewModel
        eventRepository = EventReportRepository(database.pendingEventReportDao(), apiService,requireContext())

        val factory = LocalEventViewModelFactory(requireActivity().application, eventRepository)

        viewModel = ViewModelProvider(this, factory).get(EventViewModel::class.java)

        // Initialize locationManager
        locationManager = requireContext().getSystemService(Context.LOCATION_SERVICE) as LocationManager

        eventRepository = EventReportRepository(reportDao, apiService,requireContext())

        // Initialize progressBar early to avoid null reference
        progressBar = _binding!!.progressBar

        // Show loading indicator before starting data load
        progressBar.visibility = View.VISIBLE

        lifecycleScope.launch(Dispatchers.IO) {
            loadTilesFromDatabase()
            withContext(Dispatchers.Main) {
                loadGridItems() // This will call hideLoading()
            }
        }
        return binding.root
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        if (context is OnCheckTilesListener) {
            listener = context
        } else {
            throw RuntimeException("$context must implement OnCheckTilesListener")
        }
    }


    override fun onDetach() {
        super.onDetach()
        listener = null
    }

    interface OnCheckTilesListener {
        fun onCheckTilesClicked()
    }



    companion object {
        fun newInstance() = SubscriptionsFragment()
    }

    private fun loadGridItems() {
        subscriptionsContainer = binding.root.findViewById(R.id.subscriptionsContainer)
        subscriptionsContainer.removeAllViews()

        // Hide loading indicator when container is ready to be populated
        hideLoading()

        tiles.sortedBy { it.order }.forEach { tile ->
            // Inflate the subscription bar item
            val itemView = layoutInflater.inflate(R.layout.item_subscription_bar, subscriptionsContainer, false)
            val cardView = itemView as MaterialCardView
            val button = cardView.findViewById<MaterialButton>(R.id.subscriptionButton)

            // Set button properties
            button.text = tile.title
            button.tag = tile.id

            // Set the icon based on the tile's iconName
            val iconResId = resources.getIdentifier(tile.iconName, "drawable", requireContext().packageName)
            if (iconResId != 0) {
                button.icon = ContextCompat.getDrawable(requireContext(), iconResId)
            }

            // Initialize button state
            val initialState = if (tile.isActive) ButtonState.SUBSCRIBED else ButtonState.UNSUBSCRIBED
            clickStates[tile.id] = initialState

            // Set initial appearance (don't show toast during initial setup)
            updateButtonAppearance(button, initialState, false)

            // Set click listener
            button.setOnClickListener {
                clickedButton = button
                handleClicked(tile)
            }

            // Add the item to the container
            subscriptionsContainer.addView(cardView)
        }
    }

    // This method is no longer needed as we're using the inflated layout
    // The functionality has been moved to loadGridItems()

    private fun mapApiTileToLocalTile(apiTile: ReportType): Tile {
        val iconName = when (apiTile.reportName.lowercase(Locale.getDefault())) {
            "power outage" -> "ic_power_out"
            "road blocked" -> "ic_road_blocked"
            "road accident" -> "ic_car_crash"
            "road hazard" -> "ic_warning"
            "water outage" -> "ic_water_outage"
            else -> "ic_default"
        }

        return Tile(
            id = apiTile.id,
            title = apiTile.reportName,
            iconName = iconName,
            order = apiTile.typeOrder,
            isActive = apiTile.isSubscribed,
            favourite = false
        )
    }


    private suspend fun loadTilesFromDatabase() {
        withContext(Dispatchers.IO) {
            if (isNetworkAvailable()) {
                try {
                    // Fetch and update tiles according to the new logic
                    fetchAndUpdateTilesFromApi()
                } catch (e: Exception) {
                    Log.e("SubscriptionsFragment", "Failed to fetch from API, falling back to database", e)
                    // If fetch fails, load from database
                    tiles = tileDao.getAll().toMutableList()
                }
            } else {
                // No internet, load from database
                tiles = tileDao.getAll().toMutableList()

                // If database is empty, try to fetch from API
                if (tiles.isEmpty()) {
                    fetchAndSaveTilesFromApi()
                }
            }
        }
    }

    /**
     * Fetches tiles from the API and updates the local database according to the following rules:
     * 1. If a tile ID already exists in the database, only update the tile name
     * 2. If a tile ID does not exist in the database, insert the new tile
     */
    private suspend fun fetchAndUpdateTilesFromApi() {
        try {
            val response = withContext(Dispatchers.IO) {
                RetrofitClient.instance.getReportTypes().execute()
            }

            if (response.isSuccessful) {
                var apiTiles = response.body()

                if (apiTiles != null) {
                    apiTiles=apiTiles.filter { it.custom == false && it.owner == "System" && it.reportStatus == 1 }
                    withContext(Dispatchers.IO) {
                        tileDao.deleteAll()
                        tileDao.insertAll(mapApiTilesToLocalTiles(apiTiles))
                        tiles = tileDao.getAll().toMutableList()
                        Log.d("SubscriptionsFragment", "Loaded ${tiles.size} tiles from database after update")
                    }
                } else {
                    Log.e("SubscriptionsFragment", "API returned null for report types")
                }
            } else {
                Log.e("SubscriptionsFragment", "Error fetching tiles: ${response.errorBody()?.string()}")
            }
        } catch (e: Exception) {
            Log.e("SubscriptionsFragment", "Error fetching tiles: ${e.message}")
            throw e  // Rethrow to be caught by the caller
        }
    }

    private suspend fun fetchAndSaveTilesFromApi() {
        try {
            val response = withContext(Dispatchers.IO) {
                RetrofitClient.instance.getReportTypes().execute()
            }

            if (response.isSuccessful) {
                var apiTiles = response.body()
                if (apiTiles != null) {
                    apiTiles=apiTiles.filter { it.custom == false && it.owner == "System" && it.reportStatus == 1 }
                    val localTiles = apiTiles.map { mapApiTileToLocalTile(it) }
                    withContext(Dispatchers.IO) {
                        tileDao.deleteAll()
                        tileDao.insertAll(localTiles)
                    }
                } else {
                    Log.e("SubscriptionsFragment", "API returned null for report types")
                }
            } else {
                Log.e("SubscriptionsFragment", "Error fetching tiles: ${response.errorBody()?.string()}")
            }
        } catch (e: Exception) {
            Log.e("SubscriptionsFragment", "Error fetching tiles: ${e.message}")
        }
    }

    private suspend fun createSubscriber(subscriptions: SubscriptionRequest): String? = withContext(Dispatchers.IO) {
        try {
            withContext(Dispatchers.Main) {
                progressBar.visibility = View.VISIBLE
            }
            val response = RetrofitClient.instance.postSubscriber(subscriptions)
            withContext(Dispatchers.Main) {
                progressBar.visibility = View.GONE
            }
            return@withContext response.subscriberId
        }catch (e: Exception) {
            withContext(Dispatchers.Main) {
                progressBar.visibility = View.GONE
            }
            Log.e("SubscriptionsFragment", "Error creating subscriber: ${e.message}", e)
            return@withContext null
        }
    }

    private suspend fun subscribe(req: SubscriptionRequest): Int {
        return try {
            withContext(Dispatchers.Main) {
                progressBar.visibility = View.VISIBLE
            }

            val response = RetrofitClient.instance.postSubscriptions(req.subscriptions)
            withContext(Dispatchers.Main) {
                progressBar.visibility = View.GONE
            }
            if (response.isSuccessful && response.code() == 201) {
                1 // Success
            } else {
                0 // Failure
            }


        } catch (e: Exception) {
            withContext(Dispatchers.Main) {
                progressBar.visibility = View.GONE
            }
            Log.e("SubscriptionsFragment", "Error subscribing: ${e.message}")
            0 // Failure
        }
    }

    private suspend fun unsubscribe(req: SubscriptionRequest, tile: Tile): Int {
        return try {
            withContext(Dispatchers.Main) {
                progressBar.visibility = View.VISIBLE
            }
            val response = RetrofitClient.instance.unsubscribe(req.subscriptions)
            withContext(Dispatchers.Main) {
                progressBar.visibility = View.GONE
            }
            if (response.isSuccessful) {
                lifecycleScope.launch(Dispatchers.IO) {
                    tile.notificationsEnabled = false
                    tileDao.update(tile)

                    remoteReportDao.deleteReport(tile.id.toLong())
                    reportDao.deleteById(tile.id)
                }
                1 //
            } else {
                0 // Failure
            }
        } catch (e: Exception) {
            withContext(Dispatchers.Main) {
                progressBar.visibility = View.GONE
            }
            Log.e("SubscriptionsFragment", "Error unsubscribing: ${e.message}")
            0 // Failure
        }
    }

    private fun handleClicked(tile: Tile) {
        val currentState = clickStates[tile.id] ?: ButtonState.UNSUBSCRIBED

        // Calculate the next state
        val nextState = when (currentState) {
            ButtonState.UNSUBSCRIBED -> ButtonState.SUBSCRIBED
            ButtonState.SUBSCRIBED -> ButtonState.FAVORITE
            ButtonState.FAVORITE -> ButtonState.UNSUBSCRIBED
        }

        lifecycleScope.launch {
            try {
                // For unsubscribe and favorite, the UI is updated in their respective handlers
                // For subscribe, update the UI here for immediate feedback
                if (currentState == ButtonState.UNSUBSCRIBED) {
                    // Update UI immediately for subscription
                    withContext(Dispatchers.Main) {
                        clickStates[tile.id] = nextState
                        clickedButton?.let { updateButtonAppearance(it, nextState, true) }
                    }
                }

                // Perform the action based on current state
                when (currentState) {
                    ButtonState.UNSUBSCRIBED -> handleSubscription(tile)
                    ButtonState.SUBSCRIBED -> handleUnsubscription(tile)
                    ButtonState.FAVORITE -> handleFavorite(tile)
                }

                // Notify listener that tiles have changed
                withContext(Dispatchers.Main) {
                    listener?.onCheckTilesClicked()
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    // Only show a generic error message, not the technical details
                    Toast.makeText(context, "Unable to update subscription", Toast.LENGTH_SHORT).show()
                    Log.e("SubscriptionsFragment", "Error in handleClicked", e)
                    // Reset button state on error
                    clickStates[tile.id] = currentState
                    clickedButton?.let { updateButtonAppearance(it, currentState, false) }
                }
            }
        }
    }

    private suspend fun handleSubscription(tile: Tile) {
        try {
            val subscriberId = getSubscriberId()

            if (subscriberId.isNullOrEmpty()) {
                handleNewSubscriber(tile)
            } else {
                handleExistingSubscriber(tile)
            }
        } catch (e: Exception) {
            Log.e("SubscriptionsFragment", "Error in handleSubscription: ${e.message}")
            withContext(Dispatchers.Main) {
                Toast.makeText(context, "Unable to update subscription", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private suspend fun handleNewSubscriber(tile: Tile) {
        try {
            val subscriptionItem = SubscriptionItem(reportTypeId = tile.id)
            val request = SubscriptionRequest(listOf(subscriptionItem))

            val newSubscriberId = withContext(Dispatchers.IO) {
                createSubscriber(request)
            }

            if (!newSubscriberId.isNullOrEmpty()) {
                saveSubscriberId(newSubscriberId)
                updateTileStateInDatabase(tile.id, ButtonState.SUBSCRIBED)
                withContext(Dispatchers.Main) {
                    changeButtonColor(ButtonState.SUBSCRIBED)
                }
            } else {
                throw Exception("Failed to create subscription")
            }
        } catch (e: Exception) {
            Log.e("SubscriptionsFragment", "Error in handleNewSubscriber: ${e.message}")
            withContext(Dispatchers.Main) {
                Toast.makeText(context, "Unable to create subscription", Toast.LENGTH_SHORT).show()
            }
            throw e
        }
    }

    private suspend fun handleExistingSubscriber(tile: Tile) {
        try {
            val result = withContext(Dispatchers.IO) {
                subscribe(SubscriptionRequest(listOf(SubscriptionItem(reportTypeId = tile.id))))
            }

            if (result == 1) {
                updateTileStateInDatabase(tile.id, ButtonState.SUBSCRIBED)
                withContext(Dispatchers.Main) {
                    changeButtonColor(ButtonState.SUBSCRIBED)
                }
            } else {
                throw Exception("Failed to subscribe")
            }
        } catch (e: Exception) {
            Log.e("SubscriptionsFragment", "Error in handleExistingSubscriber: ${e.message}")
            withContext(Dispatchers.Main) {
                Toast.makeText(context, "Unable to subscribe", Toast.LENGTH_SHORT).show()
            }
            throw e
        }
    }

    private suspend fun handleUnsubscription(tile: Tile) {
        try {
            // First, update the UI immediately to provide instant feedback
            withContext(Dispatchers.Main) {
                clickStates[tile.id] = ButtonState.UNSUBSCRIBED
                clickedButton?.let { updateButtonAppearance(it, ButtonState.UNSUBSCRIBED, true) }
            }

            val subscriptionItem = SubscriptionItem(reportTypeId = tile.id)
            val request = SubscriptionRequest(listOf(subscriptionItem))

            val result = withContext(Dispatchers.IO) {
                unsubscribe(request, tile)
            }

            if (result == 1) {
                // Update the database
                updateTileStateInDatabase(tile.id, ButtonState.UNSUBSCRIBED)
            } else {
                // If the API call fails, revert the UI change
                withContext(Dispatchers.Main) {
                    clickStates[tile.id] = ButtonState.SUBSCRIBED
                    clickedButton?.let { updateButtonAppearance(it, ButtonState.SUBSCRIBED, false) }
                }
                throw Exception("Failed to unsubscribe")
            }
        } catch (e: Exception) {
            Log.e("SubscriptionsFragment", "Error in handleUnsubscription: ${e.message}")
            withContext(Dispatchers.Main) {
                Toast.makeText(context, "Unable to unsubscribe", Toast.LENGTH_SHORT).show()
            }
            throw e
        }
    }

    private suspend fun handleFavorite(tile: Tile) {
        try {
            // First, update the UI immediately to provide instant feedback
            withContext(Dispatchers.Main) {
                clickStates[tile.id] = ButtonState.UNSUBSCRIBED
                clickedButton?.let { updateButtonAppearance(it, ButtonState.UNSUBSCRIBED, true) }
            }

            withContext(Dispatchers.IO) {
                val allTiles = tileDao.getAll().toMutableList()

                // Find the tile in the list
                val tileInList = allTiles.find { it.id == tile.id }

                if (tileInList != null) {
                    // Update orders
                    allTiles.forEach { t ->
                        if (t.id == tile.id) {
                            t.order = 1
                            t.favourite = true
                        } else {
                            t.order = t.order + 1
                        }
                    }

                    // Update database in a single transaction
                    tileDao.updateAll(allTiles)

                    tiles = allTiles

                    withContext(Dispatchers.Main) {
                        loadGridItems()
                    }
                } else {
                    // If the database update fails, revert the UI change
                    withContext(Dispatchers.Main) {
                        clickStates[tile.id] = ButtonState.FAVORITE
                        clickedButton?.let { updateButtonAppearance(it, ButtonState.FAVORITE, false) }
                        Toast.makeText(context, "Error updating favorite", Toast.LENGTH_SHORT).show()
                    }
                    Log.e("SubscriptionsFragment", "Tile with ID ${tile.id} not found in database")
                }
            }
        } catch (e: Exception) {
            // If an exception occurs, revert the UI change
            withContext(Dispatchers.Main) {
                clickStates[tile.id] = ButtonState.FAVORITE
                clickedButton?.let { updateButtonAppearance(it, ButtonState.FAVORITE, false) }
                Toast.makeText(context, "Error updating favorite", Toast.LENGTH_SHORT).show()
            }
            Log.e("SubscriptionsFragment", "Error in handleFavorite: ${e.message}")
        }
    }

    fun changeButtonColor(state:ButtonState){
        clickedButton?.let { button ->
            updateButtonAppearance(button, state, true)
            // Always notify the listener to check for active tiles
            listener?.onCheckTilesClicked()
        }
    }

    private lateinit var progressBar: ProgressBar

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // No need to initialize progressBar here as it's already done in onCreateView
        // No need to call showLoading() here as it's already done in onCreateView

        // Menu handling is now done in MainActivity
    }

    private fun showLoading() {
        progressBar.visibility = View.VISIBLE
        // Only hide subscriptionsContainer if it's initialized
        if (::subscriptionsContainer.isInitialized) {
            subscriptionsContainer.visibility = View.GONE
        }
    }

    private fun hideLoading() {
        progressBar.visibility = View.GONE
        // Only show subscriptionsContainer if it's initialized
        if (::subscriptionsContainer.isInitialized) {
            subscriptionsContainer.visibility = View.VISIBLE
        }
    }
    // This comment is intentionally left empty to remove the duplicate method

    fun refreshData() {
        // Show loading indicator
        showLoading()

        // Refresh data from database
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                loadTilesFromDatabase()

                withContext(Dispatchers.Main) {
                    // Update UI
                    loadGridItems() // This will call hideLoading()
                    Toast.makeText(context, "Subscriptions refreshed", Toast.LENGTH_SHORT).show()
                }
            } catch (e: Exception) {
                Log.e("SubscriptionsFragment", "Error refreshing data: ${e.message}")
                withContext(Dispatchers.Main) {
                    hideLoading()
                    Toast.makeText(context, "Error refreshing data", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    fun resetAllSubscriptions() {
        AlertDialog.Builder(requireContext())
            .setTitle("Reset Subscriptions and reports")
            .setMessage("Are you sure you want to reset all subscriptions and reports?")
            .setPositiveButton("Yes") { _, _ ->
                clearSubscriberId()
                resetAllTilesToUnsubscribed()
                Toast.makeText(context, "All subscriptions reset", Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton("No", null)
            .show()
    }
    // Helper method to clear subscriber ID
    private fun clearSubscriberId() {
        try {
            val masterKey = MasterKey.Builder(requireContext())
                .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
                .build()

            val sharedPreferences = EncryptedSharedPreferences.create(
                requireContext(),
                "secure_prefs",
                masterKey,
                EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
                EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
            )

            sharedPreferences.edit().remove("subscriber_id").apply()
        } catch (e: Exception) {
            Log.e("SubscriptionsFragment", "Error clearing subscriber ID: ${e.message}")
        }
    }

    // Reset all tiles to unsubscribed state
    private fun resetAllTilesToUnsubscribed() {
        // Update the UI
        for (i in 0 until subscriptionsContainer.childCount) {
            val cardView = subscriptionsContainer.getChildAt(i) as? MaterialCardView
            cardView?.let {
                val button = it.findViewById<MaterialButton>(R.id.subscriptionButton)
                val tileId = button.tag as Int
                lifecycleScope.launch(Dispatchers.IO){
                    reportDao.deleteAll()
                }
                clickStates[tileId] = ButtonState.UNSUBSCRIBED
                updateButtonAppearance(button, ButtonState.UNSUBSCRIBED, false)
            }
        }

        // Update the database
        lifecycleScope.launch(Dispatchers.IO) {
            tiles.forEach { tile ->
                tile.isActive = false
                tile.favourite = false
                tileDao.update(tile)
            }
            withContext(Dispatchers.Main){
                loadGridItems()
                listener?.onCheckTilesClicked()
            }
        }


    }


    // Helper method to get subscriber ID from secure storage
    private fun getSubscriberId(): String? {
        val context = context ?: return null

        // Example using EncryptedSharedPreferences for secure storage
        return try {
            val masterKey = MasterKey.Builder(context)
                .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
                .build()

            val sharedPreferences = EncryptedSharedPreferences.create(
                context,
                "secure_prefs",
                masterKey,
                EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
                EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
            )

            sharedPreferences.getString("subscriber_id", null)
        } catch (e: Exception) {
            Log.e("SubscriptionsFragment", "Error retrieving subscriber ID: ${e.message}")
            null
        }
    }

    // Helper method to save subscriber ID to secure storage
    private fun saveSubscriberId(subscriberId: String) {
        val context = context ?: return

        try {
            val masterKey = MasterKey.Builder(context)
                .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
                .build()

            val sharedPreferences = EncryptedSharedPreferences.create(
                context,
                "secure_prefs",
                masterKey,
                EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
                EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
            )

            sharedPreferences.edit().putString("subscriber_id", subscriberId).apply()
        } catch (e: Exception) {
            Log.e("SubscriptionsFragment", "Error saving subscriber ID: ${e.message}")
        }
    }

    private fun updateButtonAppearance(button: MaterialButton, state: ButtonState, showToast: Boolean = false) {
        val originalText = button.text.toString().split(" ")[0]

        when (state) {
            ButtonState.UNSUBSCRIBED -> {
                context?.let { button.setBackgroundColor(it.getColor(android.R.color.darker_gray)) }
                button.strokeWidth = 0
                button.text = originalText
                button.setTextColor(context?.let { it.getColor(android.R.color.white) } ?: android.graphics.Color.WHITE)
                button.iconTint = ColorStateList.valueOf(context?.let { it.getColor(android.R.color.white) } ?: android.graphics.Color.WHITE)
            }
            ButtonState.SUBSCRIBED -> {
                context?.let { button.setBackgroundColor(it.getColor(R.color.teal_700)) }
                button.strokeWidth = 0
                button.text = "$originalText"
                button.setTextColor(context?.let { it.getColor(android.R.color.white) } ?: android.graphics.Color.WHITE)
                button.iconTint = ColorStateList.valueOf(context?.let { it.getColor(android.R.color.white) } ?: android.graphics.Color.WHITE)
            }
            ButtonState.FAVORITE -> {
                context?.let { button.setBackgroundColor(it.getColor(R.color.teal_700)) }
                button.strokeWidth = dpToPx(2)
                button.strokeColor = context?.let { ColorStateList.valueOf(it.getColor(R.color.teal_200)) }
                button.text = "$originalText ★"
                button.setTextColor(context?.let { it.getColor(R.color.teal_200) } ?: android.graphics.Color.CYAN)
                button.iconTint = ColorStateList.valueOf(context?.let { it.getColor(R.color.teal_200) } ?: android.graphics.Color.CYAN)
            }
        }

        // Force a redraw of the button
        button.invalidate()
    }

    private fun updateTileStateInDatabase(tileId: Int, state: ButtonState) {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val tile = tileDao.getById(tileId)
                if (tile != null) {
                    tile.isActive = state != ButtonState.UNSUBSCRIBED
                    tile.favourite = state == ButtonState.FAVORITE
                    tileDao.update(tile)

                    // Notify the listener on the main thread
                    withContext(Dispatchers.Main) {
                        listener?.onCheckTilesClicked()
                    }
                } else {
                    Log.e("SubscriptionsFragment", "Tile with ID $tileId not found")
                    withContext(Dispatchers.Main) {
                        Toast.makeText(context, "Error updating subscription", Toast.LENGTH_SHORT).show()
                    }
                }
            } catch (e: Exception) {
                Log.e("SubscriptionsFragment", "Error updating tile state: ${e.message}")
                withContext(Dispatchers.Main) {
                    Toast.makeText(context, "Error updating subscription", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    private fun snapToGrid(view: View) {
        val row = ((view.y + view.height / 2) / view.height).toInt()
        val col = ((view.x + view.width / 2) / view.width).toInt()

        view.animate()
            .x(col * view.width.toFloat())
            .y(row * view.height.toFloat())
            .setDuration(200)
            .start()
    }


    private fun updateTileOrderInDatabase() {
        lifecycleScope.launch(Dispatchers.IO) {
            tiles.forEachIndexed { index, tile ->
                tile.order = index + 1
                tileDao.update(tile)
            }
        }
    }

    private fun mapApiTilesToLocalTiles(apiTiles: List<ReportType>): List<Tile> {
        return apiTiles.map { apiTile ->
            val iconName = when (apiTile.reportName.lowercase(Locale.getDefault())) {
                "power outage" -> "ic_power_out"
                "road blocked" -> "ic_road_blocked"
                "road accident" -> "ic_car_crash"
                "road hazard" -> "ic_warning"
                "water outage" -> "ic_water_outage"
                "flooding" -> "ic_flood"
                "fire" -> "ic_fire"
                "gas leak" -> "ic_gas_leak"
                "public disturbance" -> "ic_disturbance"
                "fallen tree" -> "ic_fallen_tree"
                "crime" -> "ic_crime"
                "construction" -> "ic_construction"
                "medical emergency" -> "ic_medical"
                else -> "ic_default"
            }

            Tile(
                id = apiTile.id,
                title = apiTile.reportName,
                iconName = iconName,
                order = apiTile.typeOrder,
                isActive = apiTile.isSubscribed,
                favourite = false
            )
        }
    }

    private fun dpToPx(dp: Int): Int {
        return TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP,
            dp.toFloat(),
            resources.displayMetrics
        ).toInt()
    }

    private fun isNetworkAvailable(): Boolean {
        val connectivityManager = this.requireContext().getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val network = connectivityManager.activeNetwork ?: return false
        val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
        return capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
        locationListener?.let {
            locationManager.removeUpdates(it)
        }
    }
}
