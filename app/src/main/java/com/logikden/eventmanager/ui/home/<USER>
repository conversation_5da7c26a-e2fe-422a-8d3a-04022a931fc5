package com.logikden.eventmanager.ui.home

import androidx.fragment.app.Fragment
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.logikden.eventmanager.data.AppDatabase
import com.logikden.eventmanager.data.Tile
import com.logikden.eventmanager.data.TileDao
import com.logikden.eventmanager.databinding.FragmentCustomEventBinding
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class CustomEventFragment : Fragment() {

    private var _binding: FragmentCustomEventBinding? = null
    private val binding get() = _binding!!

    private lateinit var database: AppDatabase
    private lateinit var tileDao: TileDao
    private lateinit var tileAdapter: TileAdapter
    private var selectedTile: Tile? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentCustomEventBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Initialize database
        database = AppDatabase.getDatabase(requireContext(), viewLifecycleOwner.lifecycleScope)
        tileDao = database.tileDao()

        // Set up RecyclerView
        tileAdapter = TileAdapter(
            onTileClicked = { tile -> onTileClicked(tile) },
            onDeleteClicked = { tile -> deleteTile(tile) }
        )
        binding.tilesRecyclerView.layoutManager = LinearLayoutManager(requireContext())
        binding.tilesRecyclerView.adapter = tileAdapter

        // Load tiles from the database
        loadTiles()

        // Set up Save button
        binding.saveButton.setOnClickListener {
            saveTileToDatabase()
        }

        // Set up Update button
        binding.updateButton.setOnClickListener {
            updateTile()
        }

        // Initially hide the Update button
        binding.updateButton.visibility = View.GONE

        // Show Save button when the form is cleared
        binding.clearButton.setOnClickListener {
            clearForm()
            binding.saveButton.visibility = View.VISIBLE
            binding.updateButton.visibility = View.GONE
        }
    }

    private fun loadTiles() {
        lifecycleScope.launch(Dispatchers.IO) {
            val tiles = tileDao.getAll()
            withContext(Dispatchers.Main) {
                tileAdapter.submitList(tiles)
            }
        }
    }

    private fun saveTileToDatabase() {
        val title = binding.titleInput.text.toString()
        val order = binding.orderInput.text.toString().toIntOrNull() ?: 0

        if (title.isEmpty()) {
            Toast.makeText(requireContext(), "Please fill all fields", Toast.LENGTH_SHORT).show()
            return
        }

        val tile = Tile(
            id = 0, // Auto-generated by Room
            title = title,
            iconName = "ic_local_activity", // Replace with actual icon name
            order = order,
            isActive = true,
            favourite = false
        )

        lifecycleScope.launch(Dispatchers.IO) {
            tileDao.insert(tile)
            withContext(Dispatchers.Main) {
                Toast.makeText(requireContext(), "Tile saved successfully", Toast.LENGTH_SHORT).show()
                clearForm()
                loadTiles()
            }
        }
    }

    private fun updateTile() {
        val title = binding.titleInput.text.toString()
        val order = binding.orderInput.text.toString().toIntOrNull() ?: 0

        if (title.isEmpty() || selectedTile == null) {
            Toast.makeText(requireContext(), "Please select a tile to update", Toast.LENGTH_SHORT).show()
            return
        }

        val updatedTile = selectedTile!!.copy(
            title = title,
            order = order
        )

        lifecycleScope.launch(Dispatchers.IO) {
            tileDao.update(updatedTile)
            withContext(Dispatchers.Main) {
                Toast.makeText(requireContext(), "Tile updated successfully", Toast.LENGTH_SHORT).show()
                clearForm()
                loadTiles()
            }
        }
    }

    private fun deleteTile(tile: Tile) {
        lifecycleScope.launch(Dispatchers.IO) {
            tileDao.delete(tile)
            withContext(Dispatchers.Main) {
                Toast.makeText(requireContext(), "Tile deleted successfully", Toast.LENGTH_SHORT).show()
                loadTiles()
            }
        }
    }

    private fun onTileClicked(tile: Tile) {
        selectedTile = tile
        binding.titleInput.setText(tile.title)
        binding.orderInput.setText(tile.order.toString())

        // Show Update button and hide Save button when editing
        binding.saveButton.visibility = View.GONE
        binding.updateButton.visibility = View.VISIBLE
    }

    private fun clearForm() {
        binding.titleInput.text?.clear()
        binding.orderInput.text?.clear()
        selectedTile = null

        // Show Save button and hide Update button when the form is cleared
        binding.saveButton.visibility = View.VISIBLE
        binding.updateButton.visibility = View.GONE
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}