package com.logikden.eventmanager.ui.home
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.logikden.eventmanager.R
import com.logikden.eventmanager.data.Tile

class TileAdapter(
    private val onTileClicked: (Tile) -> Unit,
    private val onDeleteClicked: (Tile) -> Unit
) : ListAdapter<Tile, TileAdapter.TileViewHolder>(TileDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TileViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_tile, parent, false)
        return TileViewHolder(view)
    }

    override fun onBindViewHolder(holder: TileViewHolder, position: Int) {
        val tile = getItem(position)
        holder.bind(tile)
        holder.itemView.setOnClickListener { onTileClicked(tile) }
        holder.itemView.findViewById<View>(R.id.deleteButton).setOnClickListener {
            onDeleteClicked(tile)
        }
    }

    class TileViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val titleTextView: TextView = itemView.findViewById(R.id.tileTitle)
        private val orderTextView: TextView = itemView.findViewById(R.id.tileOrder)

        fun bind(tile: Tile) {
            titleTextView.text = tile.title
            orderTextView.text = "Order: ${tile.order}"
        }
    }

    class TileDiffCallback : DiffUtil.ItemCallback<Tile>() {
        override fun areItemsTheSame(oldItem: Tile, newItem: Tile): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Tile, newItem: Tile): Boolean {
            return oldItem == newItem
        }
    }
}