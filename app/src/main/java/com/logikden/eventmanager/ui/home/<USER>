package com.logikden.eventmanager.ui.home

import android.content.Context
import android.util.Log
import android.widget.Toast
import com.logikden.eventmanager.data.EventLocalReport
import com.logikden.eventmanager.data.EventReportDao
import com.logikden.eventmanager.data.dto.EventReportRequest
import com.logikden.eventmanager.helper.DateHelper
import com.logikden.eventmanager.services.ApiService
import com.logikden.eventmanager.services.ISyncableRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.withContext
import java.util.TimeZone

object SyncLock {
    val mutex = Mutex()
}

class EventReportRepository(
    private val eventReportDao: EventReportDao,
    private val apiService: ApiService,
    val requireContext: Context
): ISyncableRepository {

    suspend fun findNearbyPointEvents(
        latitude: Double,
        longitude: Double,
        radius: Double = 2.0 // radius in kilometers
    ): List<EventLocalReport> {
        return withContext(Dispatchers.IO) {
            try {
                val allEvents = eventReportDao.getAllReports()

                // Filter events within the specified radius (already in km)
                allEvents.filter { event ->
                    val distance = LocationUtils.calculateDistance(
                        latitude, longitude,
                        event.latitude, event.longitude
                    )
                    distance <= radius
                }
            } catch (e: Exception) {
                Log.e("EventRepository", "Error finding nearby events: ${e.message}", e)
                emptyList()
            }
        }
    }

    suspend fun insertEventReport(report: EventLocalReport) {
        withContext(Dispatchers.IO) {
            try {
                // Insert the report into local database
                val id = eventReportDao.insert(report)
                Log.d("EventReportRepository", "Inserted report with ID: $id")
                syncPendingReports()
            } catch (e: Exception) {
                Log.e("EventReportRepository", "Error inserting report: ${e.message}", e)
            }
        }
    }


    suspend fun insertEventReports(reports: List<EventLocalReport>) {
        withContext(Dispatchers.IO) {
            try {
                // Insert the report into local database
                eventReportDao.insertAll(reports)
                Log.d("EventReportRepository", "Inserted report with ID")
                syncPendingReports()
            } catch (e: Exception) {
                Log.e("EventReportRepository", "Error inserting report: ${e.message}", e)
            }
        }
    }

    suspend fun syncPendingReports() {
        if (SyncLock.mutex.tryLock()) {
            try {
                withContext(Dispatchers.IO) {
                    // Get only unsynced reports
                    val reports = eventReportDao.getUnsyncedReports()

                    Log.d("EventReportRepository", "Found ${reports.size} unsynced reports")

                    for (report in reports) {
                        try {
                            // Double-check that report is still unsynced before sending
                            if (!report.isSynced) {
                                // Convert local time to UTC time for the server
                                val utcTimestamp = DateHelper.localToUtcTimestamp(report.dateTime)
                                val utcTimeString = DateHelper.localTimestampToUtcIsoString(report.dateTime)

                                Log.d("EventReportRepository", "Syncing report: Local time=${report.dateTime}, UTC time=$utcTimestamp, UTC string=$utcTimeString")

                                val request = EventReportRequest(
                                    reportTypeId = report.reportTypeId,
                                    latitude = report.latitude,
                                    longitude = report.longitude,
                                    description = report.description,
                                    subscriberId = 1,
                                    extra = report.extra,
                                )

                                val response = apiService.postEventReport(request)

                                if (response.isSuccessful && response.code() == 201) {
                                    // Mark as synced immediately
                                    eventReportDao.markAsSynced(report.localId)
                                    Log.d(
                                        "EventReportRepository",
                                        "Synced report ID: ${report.localId}"
                                    )
                                }
                            }
                        } catch (e: Exception) {
                            Log.e(
                                "EventReportRepository",
                                "Error syncing report ID: ${report.localId}",
                                e
                            )
                        }
                    }
                }
            } finally {
                SyncLock.mutex.unlock()
            }
        }

    }

    // Get count of unsynced reports
    fun getUnsyncedReportsCount(): Flow<Int> {
        return eventReportDao.getUnsyncedCount()
    }

    suspend fun syncReports() {
        syncPendingReports()
    }

    suspend fun getReportsWithPredefinedCoordinates(): List<EventLocalReport> {
        return eventReportDao.getAllEventReports()
    }

    suspend fun submitEventReport(
        report: EventLocalReport,
        context: Context?
    ): Boolean {
        // Use local time for database operations and duplicate checking
        val currentTime = System.currentTimeMillis()
        val twoHoursInMillis = 2 * 60 * 60 * 1000L

        // Check for similar reports in the database
        val similarReports = eventReportDao.getRecentReportsByType(
            reportTypeId = report.reportTypeId,
            minTime = currentTime - twoHoursInMillis
        )

        // Check if there's a similar report within 1km
        val hasSimilarNearbyReport = similarReports.any { existingReport ->
            val distance = LocationUtils.calculateDistance(
                report.latitude, report.longitude,
                existingReport.latitude, existingReport.longitude
            )


            distance <= 1.0 // 1km
        }

        // If a similar report exists, don't proceed
        if (hasSimilarNearbyReport) {
            withContext(Dispatchers.Main) {
                if (context != null) {
                    Toast.makeText(
                        context,
                        "Similar report already exists nearby",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }
            return false
        }

        // Create new report
        val pendingReport = EventLocalReport(
            reportTypeId = report.reportTypeId,
            latitude = report.latitude,
            longitude = report.longitude,
            description = report.description,
            owner = "me",
            extra = report.extra,
            dateTime = currentTime
        )

        // Insert into local database and get the generated ID
        val localId = eventReportDao.insert(pendingReport)
        return if (localId > 0) {
            withContext(Dispatchers.Main) {
                if (context != null) {
                    Toast.makeText(
                        context,
                        "Report submitted successfully",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }

            true
        } else {
            withContext(Dispatchers.Main) {
                if (context != null) {
                    Toast.makeText(
                        context,
                        "Failed to submit report",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }
            false
        }
    }

    override suspend fun syncPendingData() {
        syncPendingReports()
    }
}