package com.logikden.eventmanager.ui.home

import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.logikden.eventmanager.data.Tile
import com.logikden.eventmanager.databinding.ItemReportTypeBinding
import com.logikden.eventmanager.R

data class ReportTypeItem(
    val tile: Tile,
    val reportCount: Int = 0
)

class ReportTypeAdapter(
    private val onReportSubmit: (Tile) -> Unit,
    private val onReportCountClick: (Tile) -> Unit,
    private val onMoreTilesClick: () -> Unit = {}
) : ListAdapter<ReportTypeItem, RecyclerView.ViewHolder>(ReportTypeDiffCallback()) {

    companion object {
        private const val VIEW_TYPE_REPORT_TYPE = 0
        private const val VIEW_TYPE_MORE_BUTTON = 1
    }

    // Flag to track if buttons should be enabled
    private var buttonsEnabled = false

    // Flag to track if we should show the "More Tiles" button
    private var showMoreButton = false

    // Flag to track if additional tiles are expanded
    private var isExpanded = false

    /**
     * Set the enabled state of all buttons in the adapter
     */
    fun setButtonsEnabled(enabled: Boolean) {
        Log.d("ReportTypeAdapter", "setButtonsEnabled called with enabled=$enabled, current=$buttonsEnabled")
        // Always update the flag, even if it hasn't changed
        buttonsEnabled = enabled
        Log.d("ReportTypeAdapter", "Updating buttons state to $enabled")
        // We'll let the caller handle refreshing the adapter
    }

    /**
     * Set whether to show the "More Tiles" button
     */
    fun setShowMoreButton(show: Boolean) {
        showMoreButton = show
    }

    /**
     * Set whether additional tiles are expanded
     */
    fun setExpanded(expanded: Boolean) {
        isExpanded = expanded
    }

    /**
     * Get whether additional tiles are expanded
     */
    fun isExpanded(): Boolean {
        return isExpanded
    }

    override fun getItemViewType(position: Int): Int {
        return if (position < currentList.size && currentList[position] != null) {
            VIEW_TYPE_REPORT_TYPE
        } else {
            VIEW_TYPE_MORE_BUTTON
        }
    }

    class ViewHolder(private val binding: ItemReportTypeBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: ReportTypeItem, onReportSubmit: (Tile) -> Unit, onReportCountClick: (Tile) -> Unit, buttonsEnabled: Boolean) {
            Log.d("ReportTypeAdapter", "Binding item ${item.tile.title} with buttonsEnabled=$buttonsEnabled")
            binding.apply {
                reportTypeTitle.text = item.tile.title
                reportCount.text = item.reportCount.toString()

                // Set the reports label with singular/plural form
                val reportsLabel = binding.root.findViewById<TextView>(R.id.reportsLabel)
                reportsLabel?.text = if (item.reportCount == 1) "report" else "reports"

                // Adjust spacing between count and label when count is 0
                if (item.reportCount == 0) {
                    reportsLabel?.setPadding(0, 0, 0, 0)
                    reportCount.setPadding(0, 0, 0, 0)
                } else {
                    reportsLabel?.setPadding(0, reportsLabel.paddingTop, 0, 0)
                }

                // Set the icon with teal tint
                val iconResId = root.context.resources.getIdentifier(
                    item.tile.iconName,
                    "drawable",
                    root.context.packageName
                )
                reportTypeIcon.setImageResource(iconResId)
                reportTypeIcon.setColorFilter(android.graphics.Color.WHITE)

                // Set visual state based on location availability
                Log.d("ReportTypeAdapter", "Setting visual state for ${item.tile.title} with buttonsEnabled=$buttonsEnabled")
                if (!buttonsEnabled) {
                    // Disable the report trigger section when location is not available
                    reportTriggerSection.alpha = 0.5f
                    reportTriggerSection.isClickable = false
                    // Add a visual indicator that location is required
                    reportTypeTitle.text = item.tile.title

                    // Add a waiting for location message at the bottom of the card
                    val waitingText = binding.root.findViewById<TextView>(R.id.waitingForLocationText)
                    if (waitingText == null) {
                        // If the waiting text view doesn't exist, create it
                        val textView = TextView(root.context).apply {
                            id = R.id.waitingForLocationText
                            text = "Waiting for location..."
                            textSize = 12f
                            setTextColor(android.graphics.Color.RED)
                            gravity = android.view.Gravity.CENTER
                            setPadding(0, 8, 0, 8)
                        }

                        // Add it to the report trigger section
                        if (reportTriggerSection is ViewGroup) {
                            (reportTriggerSection as ViewGroup).addView(textView)
                        }
                    } else {
                        waitingText.visibility = View.VISIBLE
                    }
                } else {
                    // Enable the report trigger section when location is available
                    reportTriggerSection.alpha = 1.0f
                    reportTriggerSection.isClickable = true
                    reportTypeTitle.text = item.tile.title

                    // Hide the waiting for location message if it exists
                    val waitingText = binding.root.findViewById<TextView>(R.id.waitingForLocationText)
                    waitingText?.visibility = View.GONE

                    // Enhanced click feedback for report trigger section
                    reportTriggerSection.setOnClickListener { view ->
                        // Create a scale animation
                        view.animate()
                            .scaleX(0.95f)
                            .scaleY(0.95f)
                            .setDuration(100)
                            .withEndAction {
                                view.animate()
                                    .scaleX(1f)
                                    .scaleY(1f)
                                    .setDuration(100)
                                    .start()
                                onReportSubmit(item.tile)
                            }
                            .start()
                    }
                }

                // Get reference to the View button
                val viewButton = binding.root.findViewById<TextView>(R.id.viewButton)

                // Show or hide the View button based on report count and location availability
                if (item.reportCount > 0) {
                    // Show the View button
                    viewButton?.visibility = View.VISIBLE

                    if (buttonsEnabled) {
                        // Enable click listeners when location is available
                        viewButton?.alpha = 1.0f
                        viewButton?.isClickable = true
                        viewButton?.setOnClickListener {
                            onReportCountClick(item.tile)
                        }

                        // Show the orange badge for the count
                        reportCount.visibility = View.VISIBLE
                        reportCount.background = root.context.getDrawable(R.drawable.orange_badge_background)
                        reportCountSection.alpha = 1.0f

                        // Set click listener on the entire right section as well
                        reportCountSection.isClickable = true
                        reportCountSection.setOnClickListener { view ->
                            // Create a scale animation
                            view.animate()
                                .scaleX(0.95f)
                                .scaleY(0.95f)
                                .setDuration(100)
                                .withEndAction {
                                    view.animate()
                                        .scaleX(1f)
                                        .scaleY(1f)
                                        .setDuration(100)
                                        .start()
                                    onReportCountClick(item.tile)
                                }
                                .start()
                        }
                    } else {
                        // Disable click listeners when location is not available
                        viewButton?.alpha = 0.5f
                        viewButton?.isClickable = false

                        // Show the orange badge for the count but with reduced opacity
                        reportCount.visibility = View.VISIBLE
                        reportCount.background = root.context.getDrawable(R.drawable.orange_badge_background)
                        reportCountSection.alpha = 0.5f
                        reportCountSection.isClickable = false
                    }
                } else {
                    // Hide the View button and disable click listeners
                    viewButton?.visibility = View.GONE
                    reportCountSection.isClickable = false

                    // Hide the orange badge for zero count but keep the text visible
                    reportCount.background = null
                    reportCount.visibility = View.VISIBLE

                    // For zero count, make the count and label closer together
                    reportCount.textSize = 17f  // Smaller text size for zero

                    // Make the count and label appear closer together
                    val params = LinearLayout.LayoutParams(
                        LinearLayout.LayoutParams.WRAP_CONTENT,
                        LinearLayout.LayoutParams.WRAP_CONTENT
                    )
                    params.setMargins(0, -5, 0, 0)  // Negative top margin to move it up
                    reportsLabel?.layoutParams = params

                    // Also adjust the text for zero count
                    reportsLabel?.text = "report"  // Always singular for zero

                    // Change the background to a more muted color for visual indication
                    reportCountSection.alpha = 0.7f
                }
            }
        }
    }

    class MoreButtonViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val moreTilesButton: LinearLayout = itemView.findViewById(R.id.moreTilesButton)
        val expandIcon: ImageView = itemView.findViewById(R.id.expandIcon)
        val moreTilesText: TextView = itemView.findViewById(R.id.moreTilesText)

        fun bind(isExpanded: Boolean, onMoreTilesClick: () -> Unit) {
            // Update the icon and text based on the expanded state
            if (isExpanded) {
                expandIcon.setImageResource(R.drawable.ic_expand_less)
                moreTilesText.text = "Less Tiles"
            } else {
                expandIcon.setImageResource(R.drawable.ic_expand_more)
                moreTilesText.text = "More Tiles"
            }

            // Set click listener
            moreTilesButton.setOnClickListener {
                onMoreTilesClick()
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_REPORT_TYPE -> {
                ViewHolder(
                    ItemReportTypeBinding.inflate(
                        LayoutInflater.from(parent.context),
                        parent,
                        false
                    )
                )
            }
            VIEW_TYPE_MORE_BUTTON -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_more_tiles_button, parent, false)
                MoreButtonViewHolder(view)
            }
            else -> throw IllegalArgumentException("Unknown view type: $viewType")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is ViewHolder -> {
                val item = getItem(position)
                holder.bind(item, onReportSubmit, onReportCountClick, buttonsEnabled)
            }
            is MoreButtonViewHolder -> {
                holder.bind(isExpanded, onMoreTilesClick)
            }
        }
    }

    override fun getItemCount(): Int {
        return if (showMoreButton) {
            currentList.size + 1 // Add 1 for the "More Tiles" button
        } else {
            currentList.size
        }
    }
}

class ReportTypeDiffCallback : DiffUtil.ItemCallback<ReportTypeItem>() {
    override fun areItemsTheSame(oldItem: ReportTypeItem, newItem: ReportTypeItem) =
        oldItem.tile.id == newItem.tile.id

    override fun areContentsTheSame(oldItem: ReportTypeItem, newItem: ReportTypeItem) =
        oldItem == newItem
}