package com.logikden.eventmanager.ui.home
import android.content.Context
import android.location.Location
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.GridLayout
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.core.view.MenuProvider
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.button.MaterialButton
import com.logikden.eventmanager.R
import com.logikden.eventmanager.data.AppDatabase
import com.logikden.eventmanager.data.EventLocalReport
import com.logikden.eventmanager.data.Tile
import com.logikden.eventmanager.data.TileDao
import com.logikden.eventmanager.databinding.FragmentHomeBinding
import com.logikden.eventmanager.services.LocationService
import com.logikden.eventmanager.services.RetrofitClient
import com.logikden.eventmanager.utils.LocationConstants
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

import com.logikden.eventmanager.ui.events.RemoteEventViewModel
import com.logikden.eventmanager.ui.events.RemoteEventRepository
import com.logikden.eventmanager.ui.events.RemoteEventsViewModelFactory
import com.logikden.eventmanager.utils.Resource
import com.logikden.eventmanager.data.dto.GroupedEvent
import android.os.Build
import androidx.annotation.RequiresApi
import androidx.recyclerview.widget.DividerItemDecoration
import com.logikden.eventmanager.data.EventRemoteReport
import com.logikden.eventmanager.services.LocationService.Companion.requestLocationPermissions
import com.logikden.eventmanager.utils.LocationState
import kotlinx.coroutines.flow.first


class HomeFragment : Fragment() {

    private var _binding: FragmentHomeBinding? = null
    private val binding get() = _binding!!

    private lateinit var database: AppDatabase
    private lateinit var tileDao: TileDao

    private lateinit var adapter: ReportTypeAdapter
    private lateinit var locationService: LocationService
    private lateinit var viewModel: EventViewModel
    private lateinit var eventRepository: EventReportRepository

    // Lists to hold all tiles and visible tiles
    private var allActiveTiles = mutableListOf<Tile>()
    private var visibleTiles = mutableListOf<Tile>()
    private var isExpanded = false

    // Remote event view model for fetching report counts
    private lateinit var remoteViewModel: RemoteEventViewModel
    private lateinit var remoteEventRepository: RemoteEventRepository

    private var hasUnsyncedReports = false
    private val apiService = RetrofitClient.instance

    // Flag to track if location is available
    private var isLocationAvailable = false

    private val locationCallback = object : LocationService.LocationUpdateCallback {
        @RequiresApi(Build.VERSION_CODES.O)
        override fun onLocationReceived(location: Location) {
            // Update remote view model with new location
            remoteEventRepository.setCurrentLocation(location)
            remoteViewModel.setLocation(location)

            Log.d("HomeFragment", "Location received in callback: $location")

            // Update location availability flag and enable buttons
            if (!isLocationAvailable) {
                Log.d("HomeFragment", "Updating buttons state to enabled")
                isLocationAvailable = true
                updateButtonsState(true)
            }
        }

        override fun onLocationPermissionDenied() {
            showToast("Location permissions are required")
        }

        override fun onLocationDisabled() {
            //showToast("GPS is disabled")
        }
    }

    private val locationPermissionRequest by lazy {
        LocationService.createLocationPermissionRequest(this) { granted ->
            if (granted) {
                locationService.requestLocationUpdates(
                    LocationConstants.UPDATE_INTERVAL,
                    LocationConstants.FASTEST_INTERVAL,
                    LocationConstants.MIN_DISTANCE_CHANGE
                )
            } else {
                showToast("Location permissions are required")
            }
        }
    }

    @RequiresApi(Build.VERSION_CODES.O)
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        setupDependencies()
        setupViewModel()
        setupBinding(inflater, container)
        setupRecyclerView()
        setupLocationService()

        // Initially disable buttons until location is available
        updateButtonsState(false)

        // Load report types even if location isn't available yet
        loadReportTypes()

        return binding.root
    }

    private fun setupDependencies() {
        database = AppDatabase.getDatabase(requireContext(), lifecycleScope)
        tileDao = database.tileDao()

        // Setup local event repository
        eventRepository = EventReportRepository(
            database.pendingEventReportDao(),
            apiService,
            requireContext()
        )

        // Setup remote event repository for report counts
        remoteEventRepository = RemoteEventRepository(
            database.remoteEventReportDao(),
            apiService,
            requireActivity().application
        )
    }

    @RequiresApi(Build.VERSION_CODES.O)
    private fun setupViewModel() {
        // Setup local event view model
        val localFactory = LocalEventViewModelFactory(requireActivity().application, eventRepository)
        viewModel = ViewModelProvider(this, localFactory)[EventViewModel::class.java]

        // Setup remote event view model for report counts
        val remoteFactory = RemoteEventsViewModelFactory(requireActivity().application, remoteEventRepository)
        remoteViewModel = ViewModelProvider(this, remoteFactory)[RemoteEventViewModel::class.java]

        // Observe remote view model state for report counts
        lifecycleScope.launch {
            remoteViewModel.uiState.collect { state ->
                when (state) {
                    is Resource.Success -> {
                        // Update the UI with the report counts
                        updateReportCounts(state.data)
                    }
                    is Resource.Error -> {
                        Log.e("HomeFragment", "Error loading report counts: ${state.message}")
                    }
                    is Resource.Loading -> {
                        // Show loading state if needed
                    }
                }
            }
        }
    }

    private fun setupBinding(inflater: LayoutInflater, container: ViewGroup?) {
        _binding = FragmentHomeBinding.inflate(inflater, container, false)
    }

    private fun setupLocationService() {
        locationService = LocationService.getInstance(requireContext())
        
        // Use the throttled flow for UI updates
        viewLifecycleOwner.lifecycleScope.launch {
            locationService.throttledLocationFlow.collect { location ->
                location?.let {
                    // Update remote view model with new location
                    remoteEventRepository.setCurrentLocation(it)
                    remoteViewModel.setLocation(it)

                    Log.d("HomeFragment", "Throttled location received: $it")

                    // Update location availability flag and enable buttons
                    if (!isLocationAvailable) {
                        Log.d("HomeFragment", "Updating buttons state to enabled")
                        isLocationAvailable = true
                        updateButtonsState(true)
                    }
                }
            }
        }
        
        // Keep the callback for permission and disabled notifications
        locationService.addLocationCallback(locationCallback)

        // Check if location permissions are granted
        if (locationService.checkLocationPermission()) {
            // Check if location services are enabled
            if (locationService.isLocationEnabled()) {
                // Check if location is already available
                locationService.getCurrentLocation()?.let { location ->
                    Log.d("HomeFragment", "Location already available in setupLocationService: $location")
                    isLocationAvailable = true
                    updateButtonsState(true)
                }

                // Request location updates with standard settings
                locationService.requestLocationUpdates(
                    LocationConstants.UPDATE_INTERVAL,
                    LocationConstants.FASTEST_INTERVAL,
                    LocationConstants.MIN_DISTANCE_CHANGE
                )
            } else {
                // Prompt user to enable location services
                locationService.promptEnableLocation(requireActivity())
            }
        } else {
            // Request location permissions
            LocationService.requestLocationPermissions(locationPermissionRequest)
        }
    }

    private fun setupRecyclerView() {
        adapter = ReportTypeAdapter(
            onReportSubmit = { tile ->
                handleReportSubmission(tile)
            },
            onReportCountClick = { tile ->
                navigateToEventTracking(tile)
            },
            onMoreTilesClick = {
                toggleExpandedState()
            }
        )

        binding.reportTypesRecyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = <EMAIL>
            addItemDecoration(DividerItemDecoration(context, LinearLayoutManager.VERTICAL))
        }
    }

    private fun handleReportSubmission(tile: Tile) {
        locationService.getCurrentLocation()?.let { location ->
            reportEvent(tile.title, location, tile.id)

            // Show confirmation toast
            showToast("${tile.title} report submitted")

        } ?: run {
            showToast("Waiting for location...")
            locationService.getLastKnownLocation()
        }
    }

    private fun navigateToEventTracking(tile: Tile) {
        // Get the current report count for this tile
        val reportCount = adapter.currentList.find { it.tile.id == tile.id }?.reportCount ?: 0

        locationService.getCurrentLocation()?.let { location ->
            if (reportCount > 1) {
                // If count is more than 1, navigate to events fragment with report type to expand
                val bundle = Bundle().apply {
                    putString("expandReportType", tile.title)
                }
                findNavController().navigate(R.id.action_navigation_home_to_navigation_events, bundle)
            } else {
                // Find the nearest actual event of this type
                lifecycleScope.launch {
                    try {
                        val nearestEvent = findNearestEventOfType(tile.title, location)
                        if (nearestEvent != null) {
                            // Navigate to the actual nearest event location
                            Log.d("HomeFragment", "Navigating to nearest ${tile.title} event at (${nearestEvent.latitude}, ${nearestEvent.longitude})")
                            val bundle = Bundle().apply {
                                putFloat("eventLat", nearestEvent.latitude.toFloat())
                                putFloat("eventLng", nearestEvent.longitude.toFloat())
                                putString("eventName", nearestEvent.reportName)
                                putString("sourceFragment", "home")
                                putInt("reportTypeId", nearestEvent.reportTypeId)
                            }
                            findNavController().navigate(R.id.action_navigation_home_to_eventTrackingFragment, bundle)
                        } else {
                            // Fallback: navigate to events fragment to show all events of this type
                            Log.d("HomeFragment", "No nearest event found for ${tile.title}, navigating to events list")
                            val bundle = Bundle().apply {
                                putString("expandReportType", tile.title)
                            }
                            findNavController().navigate(R.id.action_navigation_home_to_navigation_events, bundle)
                        }
                    } catch (e: Exception) {
                        Log.e("HomeFragment", "Error finding nearest event", e)
                        showToast("Unable to find nearby ${tile.title} events")
                        // Fallback to events fragment on error
                        val bundle = Bundle().apply {
                            putString("expandReportType", tile.title)
                        }
                        findNavController().navigate(R.id.action_navigation_home_to_navigation_events, bundle)
                    }
                }
            }
        } ?: run {
            showToast("Waiting for location...")
            locationService.getLastKnownLocation()
        }
    }

    /**
     * Process the grouped events data from RemoteEventViewModel to get report counts by type
     */
    private fun updateReportCounts(groupedEvents: List<GroupedEvent>) {
        val reportCounts = groupedEvents.associate { group ->
            // Extract the report type ID from the first event in the group
            val reportTypeId = group.events.firstOrNull()?.reportTypeId ?: 0

            // Calculate the total number of reports in proximity for this group
            val totalReportsCount = calculateTotalReportsInProximity(group.events)

            // Associate the report type ID with the total count
            reportTypeId to totalReportsCount
        }

        // Update the adapter with the new report counts
        updateAdapterWithCounts(reportCounts)
    }

    /**
     * Calculate the total number of reports in proximity groups for a given event type
     */
    private fun calculateTotalReportsInProximity(events: List<EventRemoteReport>): Int {
        // Create a set to track processed reports
        val processedReportIds = mutableSetOf<Int>()
        var totalCount = 0

        // Process each report
        for (event in events) {
            // Skip if already processed
            if (event.reportId in processedReportIds) continue

            // Mark this report as processed
            processedReportIds.add(event.reportId)

            // Count this report
            var groupCount = 1

            // Find reports in proximity
            for (otherEvent in events) {
                // Skip if same report or already processed
                if (event.reportId == otherEvent.reportId || otherEvent.reportId in processedReportIds) continue

                // Calculate distance
                val distance = calculateDistance(
                    event.latitude, event.longitude,
                    otherEvent.latitude, otherEvent.longitude
                )

                // If within threshold, count and mark as processed
                if (distance <= 1.5f) { // 1.5 kilometers for proximity grouping
                    groupCount++
                    processedReportIds.add(otherEvent.reportId)
                }
            }

            // Add the group count to total
            totalCount += groupCount
        }

        return totalCount
    }

    /**
     * Calculate distance between two points in kilometers
     */
    private fun calculateDistance(lat1: Double, lon1: Double, lat2: Double, lon2: Double): Float {
        val results = FloatArray(1)
        Location.distanceBetween(lat1, lon1, lat2, lon2, results)
        // Convert meters to kilometers
        return results[0] / 1000f
    }

    /**
     * Find the nearest event of a specific type to the current location
     */
    private suspend fun findNearestEventOfType(eventType: String, currentLocation: Location): EventRemoteReport? {
        return withContext(Dispatchers.IO) {
            try {
                // Get all events from the database using the DAO directly
                val database = AppDatabase.getDatabase(requireContext(), lifecycleScope)
                val remoteEventDao = database.remoteEventReportDao()

                // Get the current list of events using first() to get the current value from Flow
                val allEventsList = remoteEventDao.getAllReports().first()

                // Filter events by type (match tile title with report name)
                val eventsOfType = allEventsList.filter { event ->
                    event.reportName.equals(eventType, ignoreCase = true)
                }

                if (eventsOfType.isEmpty()) {
                    Log.d("HomeFragment", "No events found for type: $eventType")
                    return@withContext null
                }

                // Find the nearest event
                var nearestEvent: EventRemoteReport? = null
                var shortestDistance = Float.MAX_VALUE

                for (event in eventsOfType) {
                    val distance = calculateDistance(
                        currentLocation.latitude, currentLocation.longitude,
                        event.latitude, event.longitude
                    )

                    if (distance < shortestDistance) {
                        shortestDistance = distance
                        nearestEvent = event
                    }
                }

                Log.d("HomeFragment", "Found nearest $eventType event at distance: ${shortestDistance}km")
                return@withContext nearestEvent

            } catch (e: Exception) {
                Log.e("HomeFragment", "Error finding nearest event of type $eventType", e)
                return@withContext null
            }
        }
    }

    /**
     * Update the adapter with the report counts
     */
    private fun updateAdapterWithCounts(reportCounts: Map<Int, Int>) {
        lifecycleScope.launch(Dispatchers.Main) {
            try {
                // Load tiles from database (this will update allActiveTiles and visibleTiles)
                loadTilesFromDatabase()

                // Create report type items with the current report counts
                val reportTypeItems = visibleTiles.map { tile ->
                    ReportTypeItem(
                        tile = tile,
                        reportCount = reportCounts[tile.id] ?: 0
                    )
                }

                // Update the adapter with the new list
                adapter.submitList(reportTypeItems)
            } catch (e: Exception) {
                Log.e("HomeFragment", "Error updating adapter with counts: ${e.message}")
            }
        }
    }

    @RequiresApi(Build.VERSION_CODES.O)
    private fun loadReportTypes() {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                // Load tiles from database
                val tiles = loadTilesFromDatabase()

                // Initialize adapter with empty counts
                val initialItems = tiles.map { tile ->
                    ReportTypeItem(
                        tile = tile,
                        reportCount = 0
                    )
                }

                withContext(Dispatchers.Main) {
                    adapter.submitList(initialItems)
                }

                // Trigger remote data refresh to get report counts if location is available
                locationService.getCurrentLocation()?.let { location ->
                    Log.d("HomeFragment", "Location available during loadReportTypes: $location")
                    remoteEventRepository.setCurrentLocation(location)
                    remoteViewModel.setLocation(location)
                    remoteViewModel.refreshData()

                    // Update location availability flag and enable buttons
                    withContext(Dispatchers.Main) {
                        if (!isLocationAvailable) {
                            Log.d("HomeFragment", "Enabling buttons in loadReportTypes")
                            isLocationAvailable = true
                            updateButtonsState(true)
                        }
                    }
                } ?: run {
                    Log.d("HomeFragment", "No location available during loadReportTypes, buttons will remain disabled")
                }
            } catch (e: Exception) {
                Log.e("HomeFragment", "Error loading report types: ${e.message}")
                withContext(Dispatchers.Main) {
                    // Show a generic error message instead of the technical details
                    showToast("Unable to load report types")
                }
            }
        }
    }

    private suspend fun loadTilesFromDatabase(): MutableList<Tile> {
        return withContext(Dispatchers.IO) {
            // Load all active tiles
            allActiveTiles = tileDao.getAllActive().toMutableList()

            // Check if we need to show the "More Tiles" button
            val showMoreButton = allActiveTiles.size > 6
            adapter.setShowMoreButton(showMoreButton)

            // Determine which tiles to show based on expanded state
            visibleTiles = if (isExpanded || !showMoreButton) {
                allActiveTiles
            } else {
                allActiveTiles.take(6).toMutableList()
            }

            visibleTiles
        }
    }

    /**
     * Toggle between expanded and collapsed states for showing tiles
     */
    private fun toggleExpandedState() {
        isExpanded = !isExpanded
        adapter.setExpanded(isExpanded)

        lifecycleScope.launch {
            // Update the visible tiles based on the new expanded state
            val tiles = withContext(Dispatchers.IO) {
                if (isExpanded) {
                    allActiveTiles
                } else {
                    allActiveTiles.take(6).toMutableList()
                }
            }

            // Create report type items with the current report counts
            val reportCounts = adapter.currentList.associate { it.tile.id to it.reportCount }
            val reportTypeItems = tiles.map { tile ->
                ReportTypeItem(
                    tile = tile,
                    reportCount = reportCounts[tile.id] ?: 0
                )
            }

            // Update the adapter with the new list
            adapter.submitList(reportTypeItems)
        }
    }

    private fun reportEvent(eventType: String, location: Location, reportId: Int) {
        val pendingReport = EventLocalReport(
            reportTypeId = reportId,
            latitude = location.latitude,
            longitude = location.longitude,
            description = eventType,
            dateTime = System.currentTimeMillis(),
            owner = "me",
            isSynced = false,
            extra = "0.0"
        )

        lifecycleScope.launch {
            viewModel.submitEventReport(pendingReport)
        }
    }

    private fun showToast(message: String) {
        Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show()
    }

    @RequiresApi(Build.VERSION_CODES.O)
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        observeUnsyncedReports()

        // Observe location state from the view model
        viewLifecycleOwner.lifecycleScope.launch {
            Log.d("HomeFragment", "Starting to observe location state")
            remoteViewModel.locationState.collect { state ->
                Log.d("HomeFragment", "Location state changed: $state")
                when (state) {
                    is LocationState.Available -> {
                        Log.d("HomeFragment", "Location is available: ${state.location}")
                        isLocationAvailable = true
                        updateButtonsState(true)
                    }
                    LocationState.Unavailable -> {
                        Log.d("HomeFragment", "Location is unavailable")
                        isLocationAvailable = false
                        updateButtonsState(false)
                    }
                }
            }
        }

        // Initially disable buttons until location is available
        updateButtonsState(false)
    }

    override fun onResume() {
        super.onResume()
        // Restart location updates when fragment resumes
        if (locationService.checkLocationPermission() && locationService.isLocationEnabled()) {
            locationService.requestLocationUpdates(
                LocationConstants.UPDATE_INTERVAL,
                LocationConstants.FASTEST_INTERVAL,
                LocationConstants.MIN_DISTANCE_CHANGE
            )

            // Check if location is already available and update buttons accordingly
            locationService.getCurrentLocation()?.let { location ->
                Log.d("HomeFragment", "Location already available in onResume: $location")
                isLocationAvailable = true
                updateButtonsState(true)
            }
        }
    }

    override fun onPause() {
        super.onPause()
        // Stop location updates when fragment is paused to save battery
        locationService.stopLocationUpdates()
    }


    // Menu handling moved to MainActivity

    private fun observeUnsyncedReports() {
        viewModel.unsyncedReportsCount.observe(viewLifecycleOwner) { count ->
            hasUnsyncedReports = count > 0
            // Invalidate options menu to update sync button visibility
            requireActivity().invalidateOptionsMenu()
        }
    }

    /**
     * Update menu visibility based on unsynced reports
     */
    fun updateMenuVisibility(menu: Menu) {
        menu.findItem(R.id.action_sync)?.isVisible = hasUnsyncedReports
    }

    @RequiresApi(Build.VERSION_CODES.O)
    fun refreshReportCounts() {
        locationService.getCurrentLocation()?.let { location ->
            remoteEventRepository.setCurrentLocation(location)
            remoteViewModel.setLocation(location)
            remoteViewModel.refreshData()
        } ?: run {
            showToast("Waiting for location...")
            locationService.getLastKnownLocation()
        }
    }

    /**
     * Sync local reports with the server
     */
    fun syncReports() {
        viewModel.syncLocalReports()
    }

    /**
     * Reset all subscriptions
     */
    @RequiresApi(Build.VERSION_CODES.O)
    fun resetSubscriptions() {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                // Get all tiles
                val allTiles = tileDao.getAll()

                // Reset active and favorite status
                allTiles.forEach { tile ->
                    tile.isActive = false
                    tile.favourite = false
                }

                // Update the database
                tileDao.updateAll(allTiles)

                withContext(Dispatchers.Main) {
                    showToast("All subscriptions have been reset")
                    // Refresh the UI
                    loadReportTypes()
                }
            } catch (e: Exception) {
                Log.e("HomeFragment", "Error resetting subscriptions: ${e.message}")
                withContext(Dispatchers.Main) {
                    showToast("Error resetting subscriptions")
                }
            }
        }
    }

    /**
     * Update the enabled state of buttons based on location availability
     */
    private fun updateButtonsState(enabled: Boolean) {
        Log.d("HomeFragment", "updateButtonsState called with enabled=$enabled")
        // Update the adapter to enable/disable buttons
        adapter.setButtonsEnabled(enabled)

        // Force a refresh of the adapter by resubmitting the current list
        val currentList = adapter.currentList
        adapter.submitList(null)
        adapter.submitList(currentList)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        // Clean up resources
        try {
            locationService.removeLocationCallback(locationCallback)
            locationService.stopLocationUpdates()
        } catch (e: Exception) {
            Log.e("HomeFragment", "Error cleaning up location service: ${e.message}")
        }
        _binding = null
    }
}
