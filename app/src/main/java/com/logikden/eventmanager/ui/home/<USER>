package com.logikden.eventmanager.ui.home

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.logikden.eventmanager.data.EventLocalReport
import com.logikden.eventmanager.data.dto.GroupedEvent
import com.logikden.eventmanager.services.NetworkMonitor
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class EventViewModel(val application: Application, private val repository: EventReportRepository) : ViewModel() {
    private val _events = MutableLiveData<List<GroupedEvent>>()
    val events: LiveData<List<GroupedEvent>> get() = _events

    private val _unsyncedCount = MutableLiveData<Int>()

    private val _unsyncedReportsCount = MutableLiveData<Int>()
    val unsyncedReportsCount: LiveData<Int> = _unsyncedReportsCount

    private val _syncStatus = MutableLiveData<SyncStatus>()
    private var isSyncing = false

    private val networkMonitor = NetworkMonitor(application, viewModelScope, repository)

    init {
        // Start network monitoring when ViewModel is created
        networkMonitor.startMonitoring()
    }


    fun syncLocalReports() {
        if (isSyncing) return

        viewModelScope.launch {
            isSyncing = true
            _syncStatus.value = SyncStatus.SYNCING

            try {
                withContext(Dispatchers.IO) {
                    repository.syncPendingReports()
                }

                updateUnsyncedCount()

                _syncStatus.value = SyncStatus.SUCCESS
            } catch (e: Exception) {
                _syncStatus.value = SyncStatus.FAILED
            } finally {
                isSyncing = false
            }
        }
    }

    // Update the count of unsynced reports
    fun unsyncedReportsCount() {
        viewModelScope.launch {
            val count = withContext(Dispatchers.IO) {
                repository.getUnsyncedReportsCount()
            }
            _unsyncedCount.value = count.first()
        }
    }

    // Submit a new event report
    fun submitEventReport(eventReport: EventLocalReport) {
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                val isSuccess = repository.submitEventReport(eventReport, application)
                if (isSuccess) {
                    syncLocalReports()
                }
            }
        }
    }

    // Update the unsynced count
    private fun updateUnsyncedCount() {
        unsyncedReportsCount()
    }
}

// Status enum for sync operations
enum class SyncStatus {
    IDLE, SYNCING, SUCCESS, FAILED
}