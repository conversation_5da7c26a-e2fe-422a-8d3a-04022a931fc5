<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.ACTIVITY_RECOGNITION" />

    <application
        android:name=".MyApplication"
        android:allowBackup="true"
        android:usesCleartextTraffic="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:enableOnBackInvokedCallback="true"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.EventManager"
        tools:targetApi="tiramisu">

        <service
            android:name=".services.ProximityService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="location"
            tools:ignore="ForegroundServicePermission" />

        <service
            android:name=".services.ProximityJobService"
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:exported="true" />

        <service
            android:name=".services.NavigationService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="location"
            tools:ignore="ForegroundServicePermission" />

        <receiver
            android:name=".services.ActivityRecognitionReceiver"
            android:enabled="true"
            android:exported="false" />

        <activity
            android:name=".ui.splash.SplashActivity"
            android:exported="true"
            android:theme="@style/Theme.EventManager.Splash">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:theme="@style/Theme.EventManager"
            android:launchMode="singleTop" />

        <activity
            android:name=".ui.debug.ActivityRecognitionDebugActivity"
            android:exported="false"
            android:theme="@style/Theme.EventManager"
            android:label="Activity Recognition Debug" />
    </application>
</manifest>
