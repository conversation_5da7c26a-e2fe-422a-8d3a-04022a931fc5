package com.logikden.eventmanager.services

import android.content.Context
import android.content.ContextWrapper
import com.logikden.eventmanager.data.AppDatabase
import com.logikden.eventmanager.data.EventLocalReport
import com.logikden.eventmanager.data.EventReportDao
import com.logikden.eventmanager.utils.LocationUtils
import kotlinx.coroutines.runBlocking
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.Mockito.*
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever
import java.lang.reflect.Method

@RunWith(MockitoJUnitRunner::class)
class ProximityServiceTest {

    @Mock
    private lateinit var mockContext: Context

    @Mock
    private lateinit var mockDatabase: AppDatabase

    @Mock
    private lateinit var mockReportDao: EventReportDao

    private lateinit var proximityService: ProximityService
    private lateinit var checkIfUserSubmittedReportMethod: Method

    @Before
    fun setup() {
        // Create an instance of ProximityService
        proximityService = ProximityService()

        // Get access to the private method using reflection
        checkIfUserSubmittedReportMethod = ProximityService::class.java.getDeclaredMethod(
            "checkIfUserSubmittedReport",
            Double::class.java,
            Double::class.java,
            Int::class.java
        )
        checkIfUserSubmittedReportMethod.isAccessible = true

        // Mock the database access
        whenever(mockContext.applicationContext).thenReturn(mockContext)
        
        // Use reflection to set the mocked database
        val databaseField = AppDatabase::class.java.getDeclaredField("INSTANCE")
        databaseField.isAccessible = true
        databaseField.set(null, mockDatabase)
        
        whenever(mockDatabase.pendingEventReportDao()).thenReturn(mockReportDao)
    }

    @Test
    fun `test checkIfUserSubmittedReport returns true when user submitted report is nearby`() {
        // Arrange
        val latitude = 37.7749
        val longitude = -122.4194
        val reportTypeId = 1
        
        // Create a user report that is nearby (within 100m)
        val userReport = EventLocalReport(
            localId = 1,
            reportTypeId = reportTypeId,
            latitude = latitude + 0.0005, // Approximately 50m away
            longitude = longitude,
            description = "Test Report",
            owner = "me",
            dateTime = System.currentTimeMillis(),
            extra = "0.0"
        )
        
        // Mock the database query to return the user report
        whenever(mockReportDao.getRecentReportsByType(eq(reportTypeId), any())).thenReturn(listOf(userReport))
        
        // Act
        val result = checkIfUserSubmittedReportMethod.invoke(
            proximityService,
            latitude,
            longitude,
            reportTypeId
        ) as Boolean
        
        // Assert
        assert(result) { "Should return true when a user-submitted report is nearby" }
    }
    
    @Test
    fun `test checkIfUserSubmittedReport returns false when no user submitted reports are nearby`() {
        // Arrange
        val latitude = 37.7749
        val longitude = -122.4194
        val reportTypeId = 1
        
        // Create a user report that is far away (more than 100m)
        val userReport = EventLocalReport(
            localId = 1,
            reportTypeId = reportTypeId,
            latitude = latitude + 0.01, // Approximately 1km away
            longitude = longitude,
            description = "Test Report",
            owner = "me",
            dateTime = System.currentTimeMillis(),
            extra = "0.0"
        )
        
        // Mock the database query to return the user report
        whenever(mockReportDao.getRecentReportsByType(eq(reportTypeId), any())).thenReturn(listOf(userReport))
        
        // Act
        val result = checkIfUserSubmittedReportMethod.invoke(
            proximityService,
            latitude,
            longitude,
            reportTypeId
        ) as Boolean
        
        // Assert
        assert(!result) { "Should return false when no user-submitted reports are nearby" }
    }
    
    @Test
    fun `test checkIfUserSubmittedReport returns false when reports are from other users`() {
        // Arrange
        val latitude = 37.7749
        val longitude = -122.4194
        val reportTypeId = 1
        
        // Create a report from another user that is nearby
        val otherUserReport = EventLocalReport(
            localId = 1,
            reportTypeId = reportTypeId,
            latitude = latitude + 0.0005, // Approximately 50m away
            longitude = longitude,
            description = "Test Report",
            owner = "other_user", // Different owner
            dateTime = System.currentTimeMillis(),
            extra = "0.0"
        )
        
        // Mock the database query to return the other user's report
        whenever(mockReportDao.getRecentReportsByType(eq(reportTypeId), any())).thenReturn(listOf(otherUserReport))
        
        // Act
        val result = checkIfUserSubmittedReportMethod.invoke(
            proximityService,
            latitude,
            longitude,
            reportTypeId
        ) as Boolean
        
        // Assert
        assert(!result) { "Should return false when reports are from other users" }
    }
}
